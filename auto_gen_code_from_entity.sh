#!/usr/bin/env bash
packageName="com.vsimtone.rirm2.backend"
packageDir="$(echo $packageName | tr '.' '/')"
entityDir="src/main/kotlin/${packageDir}/entity"
repoDir="src/main/kotlin/${packageDir}/repository"
ctrlDir="src/main/kotlin/${packageDir}/controller"
serviceDir="src/main/kotlin/${packageDir}/service"

function newController() {
  name="$1"
  filePath="${ctrlDir}/${name}Controller.kt"
  test -f $filePath && return 0
  echo Create $filePath
  cat >"$filePath" <<EOF
package ${packageName}.controller

import ${packageName}.entity.${name}
import ${packageName}.entity.Q${name}
import ${packageName}.service.${name}Service
import org.springframework.stereotype.Repository
import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import ${packageName}.bean.ApiResp
import ${packageName}.bean.PageAttr
import ${packageName}.json.JsonViews
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.security.web.csrf.DefaultCsrfToken
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import javax.servlet.http.HttpSession

@RestController()
@RequestMapping("${name}")
class ${name}Controller : BaseController() {

    @Autowired
    lateinit var service: ${name}Service

    @RequestMapping("")
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?
    ): ApiResp {
        val q = Q${name}.a
        val queries = BooleanBuilder()
        if (search != null){
        }
        return jsonOut(service.repo.findAll(queries, pageAttr.get()));
    }

    @RequestMapping("{id:\\\\d+}")
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        return jsonOut(service.repo.findById(id).get());
    }

    @RequestMapping("{id:\\\\d+}/delete", method = [RequestMethod.POST])
    @JsonView(JsonViews.AdminView::class)
    fun delete(@PathVariable("id") id: Long): ApiResp {
        service.repo.deleteById(id)
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun edit(@ModelAttribute("form") form: ${name}): ApiResp {
        var data: ${name};
        if (form.id != null) {
            data = service.repo.findById(form.id!!).get();
        } else {
            data = ${name}();
        }
        service.repo.save(data);
        return jsonOut(data);
    }


}
EOF
}

function newRepo() {
  name="$1"
  filePath="${repoDir}/${name}Repository.kt"
  test -f $filePath && return 0
  echo Create $filePath
  cat >"$filePath" <<EOF
package ${packageName}.repository

import ${packageName}.entity.${name}
import org.springframework.stereotype.Repository

@Repository
interface ${name}Repository : BaseRepository<${name}> {

}
EOF
}

function newService() {
  name="$1"
  filePath="${serviceDir}/${name}Service.kt"
  test -f $filePath && return 0
  echo Create $filePath
  cat >"$filePath" <<EOF
package ${packageName}.service

import ${packageName}.repository.${name}Repository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class ${name}Service : BaseService() {
    @Autowired
    lateinit var repo: ${name}Repository
}
EOF
}

# shellcheck disable=SC2045
for f in $(ls "$entityDir"); do
  name=$(echo "$f" | grep -oE '^[^.]+')
  if [[ "${name}" == "BaseEntity" ]]; then
    echo ignore ${name}
  fi
  newRepo "$name"
  newService "$name"
  newController "$name"
done

# 关闭 pdb
alter pluggable database XEPDB1 close;
      
# 删除 pdb
drop pluggable database XEPDB1 including datafiles;


# 查询当前所在PDB
SELECT SYS_CONTEXT ('USERENV', 'CON_NAME') FROM DUAL;

# 查询所有PDB
select con_id,dbid,NAME,OPEN_MODE from v$pdbs;

# 切换到PDB
alter session set container=ORCLPDB1;

      
      
# 创建表空间
CREATE TABLESPACE RIRM2
DATAFILE '/opt/oracle/oradata/XE/RIRM2.dbf'
SIZE 100M
AUTOEXTEND ON
NEXT 100M
MAXSIZE UNLIMITED;

# 创建用户
CREATE USER "C##RIRM2" IDENTIFIED BY "Waes8uzo"  
DEFAULT TABLESPACE "RIRM2"
TEMPORARY TABLESPACE "TEMP";

# 授权
GRANT CONNECT, RESOURCE TO C##RIRM2;
ALTER USER C##RIRM2 QUOTA UNLIMITED ON RIRM2;

# 密码过期时间调整为无限制
ALTER PROFILE DEFAULT LIMIT PASSWORD_LIFE_TIME UNLIMITED;

# 修改密码
alter user C##RIRM2 identified by W<PERSON>s8uzo;

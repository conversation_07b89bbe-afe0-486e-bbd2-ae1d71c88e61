### SSH会话镜像传输

#### 说明

一体化平台在SSH建立和SSH传输数据时需要实时把已解密的终端输入输出传输到此程序。

#### 接口1. 获取会话镜像节点列表(http)

> 请求方式：HTTP
>
> 请求地址：`/terminal_mirror_node_list`
>
> 请求参数：无
>
> 响应结果：
> ```json
> {
>   "checkData": "",
>   "time": 0,
>   "nodes": [
>     {
>       "ip": "127.0.0.1",
>       "port": 2222,
>       "pubKeySha256": "https证书的sha256公钥"
>     }
>   ]
> }
> ```
> 注：nodes内容为字符串形式的JSON，非文档中的方式，文档中的方式仅为方便阅读。
> time是响应数据的时间,需要校验时间必须在10分钟内的才有效
> checkData的内容为hmac+sha256对nodes做的验证, 验证内容为 `time:nodes`,我们线下需要约定好一个hmac的密钥

### 接口2. 上传镜像数据

> 请求方式：wss(WebSocket+tls)
>
> 请求地址：上面接口返回的数组中随机选择一个,需要根据上述接口返回的证书hash进行证书校验。如果选择的节点无法链接，需要重新选一个重试。
>
> 请求说明: 每个ssh会话在成功建立链接后就应该理解调用该接口建立Websocket通道，
> 随后SSH的每个输入输出都需要尽快的(10秒内)发送数据，
> 在SSH未结束之前该TCP不能关闭。在10秒未有新的数据发送时需要发送Ping类型的消息保活
>
> 消息格式：每个消息内容采用msgpack库封装成二进制通过websocket的二进制消息发送
>
> 每个消息格式说明：`| uint8 | uint64 | bool | byte array |`
>
> 请求消息说明：
>
> 1. 类型: 1为会话开始,2为输出数据,3为输入数据,4为会话结束,5为心跳
> 2. 时间: 表示发送的时间(毫秒)
> 3. 压缩: true表示数据已通过gzip压缩,false表示未压缩,在数据大于4KB时可进行进行gzip压缩。
> 4. 数据定义如下
>>
>>    类型1的数据格式,JSON字符串
>> ```json
>> {
>>   "clientIP":"客户端IP",
>>   "serverIP":"服务端IP",
>>   "serverPort": 22,
>>   "clientUser":"客户端用户",
>>   "sessionId": "会话ID"
>> }
>> ```
>>
>>    类型2和类型3的数据格式,对应的字符串二进制(UTF-8MB4编码),输出可按行发送,或者一次多行,尽量实时,减少缓存带来的延时
>>
>>    类型4的数据内容字符串的结束码(ok为正常,其他视为错误提示)
>>
>>    类型5的数据内容是固定的字符串(ping)
>>
> 
> 响应消息说明:
> 1. 类型: 5为心跳回复信息
> 2. 时间: 发送的时间
> 3. 压缩: 固定false，回复内容不压缩
> 4. 数据
>
>>    类型5的数据内容是固定的字符串(pong)
>> 
>>    其他类型的请求消息不会回复
>>


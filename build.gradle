buildscript {
    ext['springBootVersion'] = '2.7.18'
    ext['kotlinVersion'] = '1.9.25'
    repositories {
        maven {
            url "https://plugins.gradle.org/m2/"
        }
        mavenCentral()
    }
    dependencies {
        classpath("org.springframework.boot:spring-boot-gradle-plugin:2.7.18")
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion"
        classpath "org.jetbrains.kotlin:kotlin-noarg:$kotlinVersion"
        classpath "org.jetbrains.kotlin:kotlin-allopen:$kotlinVersion"
        classpath "com.github.ben-manes:gradle-versions-plugin:0.51.0"
    }
}


apply plugin: "java"
apply plugin: "idea"
apply plugin: "distribution"
apply plugin: "kotlin"
apply plugin: 'kotlin-kapt'
apply plugin: "kotlin-allopen"
apply plugin: "kotlin-spring"
apply plugin: "kotlin-noarg"
apply plugin: "org.jetbrains.kotlin.jvm"
apply plugin: 'org.springframework.boot'
apply plugin: "com.github.ben-manes.versions"

group 'com.vsimtone.rirm2.backend'
version '1.0.0'

java {
    sourceCompatibility = 8
    targetCompatibility = 8
}

repositories {
    mavenCentral()
}

configurations {
    compile.exclude module: "jedis"
    configureEach {
        exclude group: 'commons-logging', module: 'commons-logging'
    }
    // 确保使用兼容的SLF4J版本
    all {
        resolutionStrategy.eachDependency { DependencyResolveDetails details ->
            if (details.requested.group == 'org.slf4j') {
                details.useVersion '1.7.36'
            }
        }
    }
}

kapt {
    correctErrorTypes true
}

dependencies {
    implementation("org.jetbrains.kotlin:kotlin-reflect:$kotlinVersion")
    implementation("org.jetbrains.kotlin:kotlin-stdlib:$kotlinVersion")

    // Spring BOOT
    implementation("org.springframework.boot:spring-boot-starter:${springBootVersion}")
    implementation("org.springframework.boot:spring-boot-starter-web:${springBootVersion}")
    implementation("org.springframework.boot:spring-boot-starter-websocket:$springBootVersion")
    implementation("org.springframework.boot:spring-boot-starter-mail:${springBootVersion}")
    implementation("org.springframework.boot:spring-boot-starter-cache:${springBootVersion}")
    implementation("org.springframework.boot:spring-boot-starter-tomcat:${springBootVersion}")
    implementation("org.springframework.boot:spring-boot-starter-validation:${springBootVersion}")
    implementation("org.springframework.boot:spring-boot-starter-security:${springBootVersion}")
//    implementation("org.springframework.boot:spring-boot-starter-data-elasticsearch:${springBootVersion}")
    implementation("org.springframework.boot:spring-boot-starter-actuator:$springBootVersion")
    implementation("org.springframework.boot:spring-boot-starter-logging:$springBootVersion") {
        exclude group: 'org.slf4j', module: 'slf4j-api'
    }
    implementation 'org.slf4j:slf4j-api:1.7.36'
    runtimeOnly("org.springframework.boot:spring-boot-properties-migrator:$springBootVersion")

    implementation("org.springframework.session:spring-session-data-redis:2.7.0")
    implementation("org.springframework.session:spring-session-core:2.7.0")

    // Gson for Spring Boot 2.x
    implementation 'com.google.code.gson:gson:2.8.9'

    implementation('org.redisson:redisson:3.20.1') {
        exclude group: 'org.slf4j'
    }
    implementation('org.redisson:redisson-spring-data-27:3.20.1') {
        exclude group: 'org.springframework.data'
        exclude group: 'org.slf4j'
    }

//    implementation('org.redisson:redisson-spring-boot-starter:3.23.5') {
//        exclude group: 'org.springframework.boot'
//    }
    implementation("com.esotericsoftware.kryo:kryo5:5.5.0")

    testImplementation("org.springframework.boot:spring-boot-starter-test:${springBootVersion}")

    implementation 'com.github.luben:zstd-jni:1.5.6-3'

    implementation("org.springframework.boot:spring-boot-starter-data-jpa:${springBootVersion}") {
        exclude group: 'org.hibernate'
    }


    implementation('com.fasterxml.jackson.module:jackson-module-kotlin:2.14.2')
    implementation('com.fasterxml.jackson.datatype:jackson-datatype-hibernate5:2.14.2')
    implementation 'com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.14.2'


    implementation('org.apache.poi:poi:5.2.5')
    implementation('org.apache.poi:poi-ooxml:5.2.5')
    implementation("org.apache.commons:commons-compress:1.26.2")

//    implementation('org.apache.poi:poi-ooxml-schemas:5.2.2')
    implementation('org.apache.commons:commons-lang3:3.14.0')
    // https://mvnrepository.com/artifact/org.apache.commons/commons-text
    implementation 'org.apache.commons:commons-text:1.12.0'

    implementation('commons-io:commons-io:2.16.1')
    implementation('commons-codec:commons-codec:1.17.0')
    implementation("org.tukaani:xz:1.9")
    implementation('commons-net:commons-net:3.11.1')

    implementation 'io.netty:netty-all:4.1.111.Final'
    implementation 'io.netty:netty-transport-native-epoll:4.1.111.Final'

    implementation 'com.mysql:mysql-connector-j:8.0.33'
    implementation 'org.mariadb.jdbc:mariadb-java-client:3.4.0'

    implementation 'org.flywaydb:flyway-core:8.5.13'
    implementation 'org.flywaydb:flyway-mysql:8.5.13'

    implementation 'com.oracle.database.jdbc:ojdbc8:21.5.0.0'

    implementation 'org.hibernate:hibernate-core:5.6.15.Final'

    implementation 'org.msgpack:msgpack-core:0.9.8'
    implementation 'org.msgpack:jackson-dataformat-msgpack:0.9.8'
    implementation 'org.bouncycastle:bcprov-jdk15on:1.70'


    implementation 'com.google.guava:guava:33.2.1-jre'
    implementation 'com.github.cage:cage:1.0'

    implementation('com.github.oshi:oshi-core:6.6.5') {
        exclude group: 'org.slf4j'
    }

    implementation("com.hankcs:hanlp:portable-1.8.4")

    implementation("org.apache.commons:commons-vfs2:2.9.0")
    implementation('com.github.abashev:vfs-s3:4.4.0')
    implementation("com.amazonaws:aws-java-sdk-s3:1.12.746")
    implementation 'com.github.mwiede:jsch:0.2.18'

    implementation("eu.agno3.jcifs:jcifs-ng:2.1.10")
    implementation("net.idauto.oss.jcifs:vfs-jcifs-ng:1.0.1")

//    implementation("nz.ac.waikato.cms.weka:weka-stable:3.8.6")


    implementation("com.squareup.okhttp3:okhttp:4.12.0")
    

    // https://mvnrepository.com/artifact/com.google.zxing/core
    implementation 'com.google.zxing:core:3.5.3'

    implementation fileTree("libs")

    implementation 'com.querydsl:querydsl-jpa:5.0.0'
    kapt 'com.querydsl:querydsl-apt:5.0.0:jpa'
    kapt 'javax.persistence:javax.persistence-api:2.2'
    implementation('javax.mail:javax.mail-api:1.6.2')

}

allOpen {
    annotation("javax.persistence.Entity")
    annotation("javax.persistence.Embeddable")
    annotation("javax.persistence.MappedSuperclass")
}

springBoot {
    buildInfo()
}

jar {
    enabled = false
}

bootJar {
    enabled = false
}

tasks.register('customJar', Zip) {
    dependsOn("build")
    archiveFileName = "${project.name}-${project.version}.jar"
    metadataCharset("UTF-8")
    destinationDirectory = file("${layout.buildDirectory.get().asFile}/lib/")
    from("${layout.buildDirectory.get().asFile}/classes/java/main/") {
        into("/")
    }
    from("${layout.buildDirectory.get().asFile}/classes/kotlin/main/") {
        into("/")
        exclude("META-INF")
    }
}

tasks.register('generateScript', CreateStartScripts) {
    dependsOn("customJar")
    outputDir = file('build/scripts')
    mainClass.set('com.vsimtone.rirm2.backend.MainKt')
    applicationName('rirm2-server')
    executableDir("bin")
    classpath = files("lib")
    doLast {
        file("${layout.buildDirectory.get().asFile}/scripts/classpaths").text = '.:' +
                fileTree("${layout.buildDirectory.get().asFile}/lib").collect { 'lib/' + it.name }.join(":") + ':' +
                configurations.runtimeClasspath.collect { 'lib/' + it.name }.join(':')
        def unixScriptFile = file getUnixScript()
        unixScriptFile.text = unixScriptFile.text.replace(
            '#!/bin/sh',
            "#!/usr/bin/env bash"
        )
        unixScriptFile.text = unixScriptFile.text.replace(
            'CLASSPATH=$APP_HOME/lib/lib',
            "CLASSPATH=@bin/classpaths"
        )
        unixScriptFile.text = unixScriptFile.text.replace(
            'eval "set --',
            '. ./auto_java_opts.sh auto\n\neval "set --'
        )
    }
}

tasks.register('pack', Tar) {
    dependsOn("clean", "generateScript")
    archiveBaseName.set("${project.name}")
    archiveExtension.set("tar")
    archiveFileName = archiveBaseName.get() + ".${archiveExtension.get()}"
    destinationDirectory = file("${layout.buildDirectory.get().asFile}")
    exclude("**/application-dev.yml")

    eachFile { file ->
        if (file.name.endsWith(".msi") && file.name.indexOf("${CLIENT_VER_NAME}") == -1) {
            println "Remove ${file.name}"
            file.exclude()
        }
    }

    from(configurations.runtimeClasspath) {
        into("${archiveBaseName.get()}/lib/")
    }

    from("${layout.buildDirectory.get().asFile}/resources/main/") {
        into("${archiveBaseName.get()}/")
    }
    from("${layout.buildDirectory.get().asFile}/scripts/") {
        into("${archiveBaseName.get()}/bin/")
    }
    from("${layout.buildDirectory.get().asFile}/lib/") {
        into("${archiveBaseName.get()}/lib/")
    }
}
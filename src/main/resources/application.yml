spring:
    flyway:
        enabled: true
        locations: db_migrations
        baseline-on-migrate: true
        table: db_migrations
    data:
        redis:
            database: 8
            poolSize: 200
            minIdleSize: 100
            repositories:
                enabled: false
    application:
        name: "Rirm2 Server"
    jpa:
        properties:
            hibernate:
                enable_lazy_load_no_trans: true
                order_inserts: true
                order_updates: true
                jdbc:
                    fetch_size: 500
                    batch_size: 500
        open-in-view: false
    servlet:
        multipart:
            max-request-size: 1073741824
            max-file-size: 1073741824
    cache:
        type: simple
    
    jackson:
        serialization:
            fail-on-empty-beans: false
    output:
        ansi:
            enabled: detect
    datasource:
        hikari:
            maximum-pool-size: 1000 # 最大连接数
            minimum-idle: 10 # 最小连接数
            idle-timeout: 600000 # 空闲超时，10分钟
            max-lifetime: 7200000 # 连接最大使用时间，2个小时
            leakDetectionThresholdMs: 3600000 # 连接超过1个小时未释放检测为连接泄露
        driver-class-name: oracle.jdbc.OracleDriver
management:
    client:
        metrics:
            enabled: true
        health:
            show-details: when_authorized
    clients:
        web:
            exposure:
                include: health,info,metrics
server:
    port: 8089
    
    servlet:
        context-path: /
        session:
            cookie:
                http-only: true
                name: "RIRM2SID"
            timeout: 30m
        encoding:
            charset: UTF-8
            force: true
            enabled: true
    tomcat:
        max-swallow-size: 1GB
        max-http-form-post-size: 1GB
        threads:
            max: 1000
debug: false

# apache vfs file system url
# file://[ absolute-path]
# ftp://[ username[: password]@] hostname[: port][ relative-path]
# ftps://[ username[: password]@] hostname[: port][ absolute-path]
# sftp://[ username[: password]@] hostname[: port][ relative-path]
# smb://[ username[: password]@] hostname[: port][ absolute-path]
# tmp://[ absolute-path]
# ram://[ path]
# s3://[[access-key:secret-key]:sign-region]@endpoint-url/folder-or-bucket/
fs.vfs_base_url: ram:///rirm2-server

app:
    log:
        # 临时启用默认logback配置来解决JDK 1.8兼容性问题
        useDefaultLogback: true
        redirectConsoleToFile: false
        console:
            enable: true
            pattern: ""
            level: "info"
        file:
            enable: true
            path: "logs/app.log"
            max-file-count: 10
            max-file-size: 100MB
            pattern: ""
            level: "info"
        level:
            ROOT: INFO
    schema_update_file: ""
    
debug: false
spring:
    data:
        redis:
            host: #REDIS_HOST#
            password: #REDIS_PASS#
            database: 1
    
    datasource:        
        url: *******************************************#
        driver-class-name: oracle.jdbc.OracleDriver
        username: #DB_USER#
        password: #DB_PASS#
    jpa:
        show-sql: false

fs.vfs_base_url: s3://#MINIO_USER#:#MINIO_PASS#@#MINIO_IP#:9000/vsimtone-rirm2-files

server:
    url: https://#SERVER_IP#/

app:
    schema_update_file: "auto_generate_db_migration.sql"
app.log:
    console:
        enable: false
    file:
        enable: true
        path: "logs/app.log"
        max-file-count: 3
        max-file-size: 100MB
        pattern: ""
        level: "info"
    redirectConsoleToFile: true

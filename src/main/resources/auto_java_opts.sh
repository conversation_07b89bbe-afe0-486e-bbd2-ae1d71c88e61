#!/usr/bin/env bash
function autoSetOpts(){
    TOTAL_GB="$1"
    if [[ -z "${TOTAL_GB}" ]] || [[ ! "${TOTAL_GB}" =~ ^[0-9]+$ ]] || [[ ${TOTAL_GB} -lt 4 ]]; then
        echo "   usage: $0 <max use mem size in GB, min 4>"
        echo "   example for 16GB"
        echo "          $0 16"
        exit 1
    fi
    
    # CPU资源计算
    CPU_CORES=$(nproc)
    GC_THREADS=$((CPU_CORES > 16 ? 16 : (CPU_CORES + 1) / 2))
    CONC_THREADS=$((GC_THREADS / 2))
    [[ $CONC_THREADS -lt 2 ]] && CONC_THREADS=2
    
    # 构建JVM参数
    JVM_OPTS="-Xms$(($TOTAL_GB*1024/8))M -Xmx${TOTAL_GB}G"
    JVM_OPTS+=" -XX:MetaspaceSize=256M -XX:MaxMetaspaceSize=1024M"
    JVM_OPTS+=" -XX:+ExitOnOutOfMemoryError -XX:ReservedCodeCacheSize=512M"
    
    # JDK版本检测（纯Bash方式）
    JAVA_VERSION=$(java -version 2>&1 | grep -o 'version ".*"' | cut -d'"' -f2 | cut -d'.' -f1)
    
    # GC策略选择 - JDK 1.8使用G1GC
    JVM_OPTS+=" -XX:+UseG1GC"
    JVM_OPTS+=" -XX:MaxGCPauseMillis=150"
    JVM_OPTS+=" -XX:G1HeapRegionSize=16M"
    JVM_OPTS+=" -XX:InitiatingHeapOccupancyPercent=45"
    
    # 线程与网络参数
    JVM_OPTS+=" -XX:ParallelGCThreads=${GC_THREADS}"
    JVM_OPTS+=" -XX:ConcGCThreads=${CONC_THREADS}"
    JVM_OPTS+=" -Dio.netty.eventLoopThreads=$((CPU_CORES * 2))"
    
    # Spring Boot优化
    JVM_OPTS+=" -Djava.security.egd=file:/dev/./urandom"
    
    
    JVM_OPTS+=" -Xloggc:logs/gc.log -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=5 -XX:GCLogFileSize=100M"
    echo "${JVM_OPTS}"
}

if [[ "${1}" == "auto" ]]; then
    if [[ "$(echo $JAVA_OPTS | grep -o 'Xmx')" == "" ]]; then
        if [[ "${APP_MAX_MEM_GB}" != "" ]]; then
            export JAVA_OPTS="${JAVA_OPTS} $(autoSetOpts ${APP_MAX_MEM_GB})"
            echo "Auto set JAVA_OPTS: ${JAVA_OPTS}"
        fi
    else
        echo "skip auto set java opts."
    fi
else
    autoSetOpts $1
fi

#!/usr/bin/env bash

name="rirm2-server"
exec="exec ./bin/rirm2-server"
image="openjdk:8-jdk"
run_args="--net host"

build_dev() {
  return
}

docker_restart() {
  echo Restart docker container ${name}
  docker restart ${name}
}

docker_start() {
  echo Start docker container ${name}
  docker start ${name}
}

docker_stop() {
  echo Stop docker container ${name}
  docker stop ${name}
}

docker_rm() {
  echo Remove docker container ${name}
  docker rm ${name}
}

docker_run() {
  docker_stop
  docker_rm
  docker pull ${image}
  echo Run docker container ${name}
  docker run --name $name -id --restart unless-stopped -v `pwd`:/opt/$name ${run_args} -e APP_MAX_MEM_GB="${APP_MAX_MEM_GB}" -e JAVA_OPTS="${JAVA_OPTS}" ${image} /opt/${name}/daemon.sh docker_entrypoint ${exec}
}

entrypoint_init() {
  test -f ~/.entrypoint_init && return
  sed -i 's/deb.debian.org/mirrors.ustc.edu.cn/g' /etc/apt/sources.list
  sed -i 's|security.debian.org|mirrors.ustc.edu.cn/debian-security|g' /etc/apt/sources.list
  apt-get -y update
  apt-get install -y locales locales-all
  locale-gen zh_CN.UTF-8
  ln -fs /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
  dpkg-reconfigure -f noninteractive tzdata
  touch ~/.entrypoint_init
}

docker_entrypoint() {
  entrypoint_init
  export LANG=zh_CN.UTF-8
  export LANGUAGE=zh_CN.UTF-8
  export LC_ALL=zh_CN.UTF-8
  cd /opt/${name}/
  eval $*
}

eval $*

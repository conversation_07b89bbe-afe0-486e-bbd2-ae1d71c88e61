create table alert_record
(
    id               number(19,0) not null,
    created_at       timestamp(6),
    deleted          number(1,0) check (deleted in (0,1)),
    updated_at       timestamp(6),
    mail_send_status varchar2(255 char) check (mail_send_status in ('Waiting','Success','Fail')),
    processed        number(1,0) not null check (processed in (0,1)),
    alert_rule_id    number(19,0),
    cmd_id           number(19,0),
    cmd_category_id  number(19,0),
    cmd_rule_id      number(19,0),
    sess_id          number(19,0),
    primary key (id)
);
create table alert_rule
(
    id                  number(19,0) not null,
    created_at          timestamp(6),
    deleted             number(1,0) check (deleted in (0,1)),
    updated_at          timestamp(6),
    alert_body          clob,
    alert_level         varchar2(255 char) check (alert_level in ('VeryLow','Low','Normal','High','VeryHigh')),
    alert_title         clob,
    mail_receivers      clob,
    serverip            varchar2(255 char),
    command_category_id number(19,0),
    command_rule_id     number(19,0),
    primary key (id)
);
create table appconfig
(
    id         number(19,0) not null,
    created_at timestamp(6),
    deleted    number(1,0) check (deleted in (0,1)),
    updated_at timestamp(6),
    c_key      varchar2(255 char),
    type       varchar2(255 char) check (type in ('String','Integer','Float','Boolean','JsonMap','JsonArray','Long')),
    c_val      clob,
    primary key (id)
);
create table change_form_command
(
    id                  number(19,0) not null,
    created_at          timestamp(6),
    deleted             number(1,0) check (deleted in (0,1)),
    updated_at          timestamp(6),
    change_form_id      varchar2(255 char),
    command             clob,
    change_form_item_id number(19,0),
    from_import_id      number(19,0),
    primary key (id)
);
create table change_form_host
(
    id                  number(19,0) not null,
    created_at          timestamp(6),
    deleted             number(1,0) check (deleted in (0,1)),
    updated_at          timestamp(6),
    address             varchar2(255 char),
    apply_id            varchar2(255 char),
    audit_user          varchar2(255 char),
    begin_time          timestamp(6),
    change_form_id      varchar2(255 char),
    check_user          varchar2(255 char),
    end_time            timestamp(6),
    env_type            varchar2(255 char),
    id_ops_name         varchar2(255 char),
    line_index          varchar2(255 char),
    recycle_user        varchar2(255 char),
    send_user           varchar2(255 char),
    sponsor_time        timestamp(6),
    sponsor_user        varchar2(255 char),
    sys_name            varchar2(255 char),
    team                varchar2(255 char),
    use_time            varchar2(255 char),
    work_record         clob,
    work_type           varchar2(255 char),
    change_form_item_id number(19,0),
    from_import_id      number(19,0),
    primary key (id)
);
create table change_form_item
(
    id                    number(19,0) not null,
    created_at            timestamp(6),
    deleted               number(1,0) check (deleted in (0,1)),
    updated_at            timestamp(6),
    cmd_count             number(19,0) not null,
    desc_text             clob,
    form_id               varchar2(512 char),
    host_count            number(19,0) not null,
    itx_cmd_audit_count   clob,
    f_confirmed_count     clob,
    f_unprocess_count     clob,
    itx_cmd_process_count clob,
    itx_cmd_total_count   number(19,0) not null,
    match_time            timestamp(6),
    name                  varchar2(255 char),
    plan_begin_time       timestamp(6),
    plan_end_time         timestamp(6),
    reason_text           clob,
    sponsor_users         varchar2(255 char),
    status_text           varchar2(255 char),
    sys_names             varchar2(255 char),
    team                  varchar2(255 char),
    type                  varchar2(255 char),
    from_import_id        number(19,0),
    primary key (id)
);
create table change_form_sync_record
(
    id                               number(19,0) not null,
    created_at                       timestamp(6),
    deleted                          number(1,0) check (deleted in (0,1)),
    updated_at                       timestamp(6),
    add_change_form_command_count    number(19,0) not null,
    add_change_form_count            number(19,0) not null,
    add_change_form_host_count       number(19,0) not null,
    errmsg                           varchar2(255 char),
    modify_change_form_command_count number(19,0) not null,
    modify_change_form_count         number(19,0) not null,
    modify_change_form_host_count    number(19,0) not null,
    success                          number(1,0) not null check (success in (0,1)),
    primary key (id)
);
create table command_category
(
    id         number(19,0) not null,
    created_at timestamp(6),
    deleted    number(1,0) check (deleted in (0,1)),
    updated_at timestamp(6),
    name       varchar2(255 char),
    path       varchar2(255 char),
    remark     varchar2(255 char),
    parent_id  number(19,0),
    primary key (id)
);
create table command_rule
(
    id                  number(19,0) not null,
    created_at          timestamp(6),
    deleted             number(1,0) check (deleted in (0,1)),
    updated_at          timestamp(6),
    alert_level         varchar2(255 char) check (alert_level in ('VeryLow','Low','Normal','High','VeryHigh')),
    description         varchar2(255 char),
    name                varchar2(255 char),
    regex               clob,
    rule_code           varchar2(255 char),
    command_category_id number(19,0),
    primary key (id)
);
create table excel_import
(
    id                 number(19,0) not null,
    created_at         timestamp(6),
    deleted            number(1,0) check (deleted in (0,1)),
    updated_at         timestamp(6),
    define_cls         varchar2(255 char),
    define_name        varchar2(255 char),
    errmsg             clob,
    imported_row_count number(10,0) not null,
    results            clob,
    status             varchar2(255 char) check (status in ('Pending','Success','Fail')),
    file_id            number(19,0),
    primary key (id)
);
create table file_folder
(
    id         number(19,0) not null,
    created_at timestamp(6),
    deleted    number(1,0) check (deleted in (0,1)),
    updated_at timestamp(6),
    name       varchar2(255 char),
    path       varchar2(255 char),
    parent_id  number(19,0),
    primary key (id)
);
create table file_info
(
    id             number(19,0) not null,
    created_at     timestamp(6),
    deleted        number(1,0) check (deleted in (0,1)),
    updated_at     timestamp(6),
    disable_delete number(1,0) not null check (disable_delete in (0,1)),
    name           varchar2(512 char),
    f_path         varchar2(512 char),
    sha256sum      varchar2(256 char),
    f_size         number(19,0),
    token          varchar2(512 char),
    f_type         varchar2(255 char),
    folder_id      number(19,0),
    primary key (id)
);
create table rirm2_itx_command
(
    id                  number(19,0) not null,
    created_at          timestamp(6),
    deleted             number(1,0) check (deleted in (0,1)),
    updated_at          timestamp(6),
    audit_result        varchar2(255 char) check (audit_result in ('NoChangeFormHost','NoPass','Pass')),
    f_cmd               clob,
    cmd_desc            varchar2(255 char),
    cmd_id              varchar2(255 char),
    exec_time           timestamp(6),
    process_desc        varchar2(2048 char),
    process_result      varchar2(255 char) check (process_result in ('UnProcess','AutoConfirmed','Confirmed')),
    rule_code           varchar2(255 char),
    screenseq           number(19,0) not null,
    change_form_item_id number(19,0),
    rule_id             number(19,0),
    session_id          number(19,0),
    primary key (id)
);
create table rirm2_itx_event_queue
(
    id         number(19,0) not null,
    audit_time number(38,0),
    body       clob,
    clientip   varchar2(255 char),
    eventseq   number(19,0) not null,
    login_user varchar2(255 char),
    serverip   varchar2(255 char),
    session_id varchar2(255 char),
    type       varchar2(255 char) check (type in ('SessionStart','SessionEnd','UnixScreen','BOCCommand')),
    primary key (id)
);
create table rirm2_itx_session
(
    id            number(19,0) not null,
    created_at    timestamp(6),
    deleted       number(1,0) check (deleted in (0,1)),
    updated_at    timestamp(6),
    begin_time    timestamp(6),
    clientip      varchar2(255 char),
    command_count number(10,0) not null,
    end_time      timestamp(6),
    login_user    varchar2(255 char),
    serverip      varchar2(255 char),
    session_id    varchar2(255 char),
    type          number(3,0) check (type between 0 and 1),
    primary key (id)
);
create index appconfig_i_key on appconfig (c_key);
alter table change_form_item
    add constraint UKlpcx4okaayjc9i8ybw7d0vdhf unique (form_id);
alter table excel_import
    add constraint UKf5qjbjuhp5nipln7ynw7eu517 unique (file_id);
alter table file_folder
    add constraint file_folder_i_path unique (path);
create index file_info_i_token on file_info (token);
create index file_info_i_sha256sum on file_info (sha256sum);
create sequence rirm2_itx_event_queue_seq start with 1 increment by 50;
alter table alert_record
    add constraint FK8591y09yaqvamde19vvuv3i9y foreign key (alert_rule_id) references alert_rule;
alter table alert_record
    add constraint FKb5146lhb75hb4i0xgrnwf0usc foreign key (cmd_id) references rirm2_itx_command;
alter table alert_record
    add constraint FKgm6fuhwlptnk6yfyk5tetmdwk foreign key (cmd_category_id) references command_category;
alter table alert_record
    add constraint FKrrhg49t2wfjwxilvqxvk7cpio foreign key (cmd_rule_id) references command_rule;
alter table alert_record
    add constraint FKgihi9wjgny5exg4h14ducg5y foreign key (sess_id) references rirm2_itx_session;
alter table alert_rule
    add constraint FK50197ndpv39tgqsb8d3lp0g2n foreign key (command_category_id) references command_category;
alter table alert_rule
    add constraint FK5e2tsr36eu9vwl5eb0ol4i7vw foreign key (command_rule_id) references command_rule;
alter table change_form_command
    add constraint FKdcg15hymex7bew2patb9vkue6 foreign key (change_form_item_id) references change_form_item;
alter table change_form_command
    add constraint FK2rg4ii7sit7lvqrncmo6hxxs0 foreign key (from_import_id) references excel_import;
alter table change_form_host
    add constraint FKadc6hmy3jks9bpeaeucapyhn4 foreign key (change_form_item_id) references change_form_item;
alter table change_form_host
    add constraint FKbomaqaxxjdw6yd5jcqjo1g6nm foreign key (from_import_id) references excel_import;
alter table change_form_item
    add constraint FK7saas600a7prc02iwu28p3dx4 foreign key (from_import_id) references excel_import;
alter table command_category
    add constraint FK6qyyr0i3oh3d4ub5238vrq9t1 foreign key (parent_id) references command_category;
alter table command_rule
    add constraint FK3u41mg9e6jiyhjbft0wb5qmaq foreign key (command_category_id) references command_category;
alter table excel_import
    add constraint FKomuxvddx1bbqqgihefwymjb04 foreign key (file_id) references file_info;
alter table file_folder
    add constraint FKkxdqyoabe53be2imcil46e1a foreign key (parent_id) references file_folder;
alter table file_info
    add constraint FKcpfqx1qfog19t2n944yje2vu4 foreign key (folder_id) references file_folder;
alter table rirm2_itx_command
    add constraint FKl4psmqq9ius7qgufmr656hdym foreign key (change_form_item_id) references change_form_item;
alter table rirm2_itx_command
    add constraint FKdaoync9mayb837kx4opt6cn8h foreign key (rule_id) references command_rule;
alter table rirm2_itx_command
    add constraint FKrxmrbwltwxeb117ecuei5767e foreign key (session_id) references rirm2_itx_session;

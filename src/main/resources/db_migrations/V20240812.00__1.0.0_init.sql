create table admin_user
(
    id               number(19,0) not null,
    created_at       timestamp(6),
    deleted          number(1,0) check (deleted in (0,1)),
    updated_at       timestamp(6),
    enabled          number(1,0) not null check (enabled in (0,1)),
    locked           number(1,0) not null check (locked in (0,1)),
    login_fail_count number(10,0) not null,
    nickname         varchar2(255 char),
    password         varchar2(255 char),
    permissions      clob,
    username         varchar2(255 char),
    org_id           number(19,0),
    primary key (id)
);
create table organization
(
    id         number(19,0) not null,
    created_at timestamp(6),
    deleted    number(1,0) check (deleted in (0,1)),
    updated_at timestamp(6),
    name       varchar2(255 char),
    path       varchar2(1024 char),
    parent_id  number(19,0),
    primary key (id)
);
create index admin_user_i_username on admin_user (username);
alter table organization
    add constraint organization_i_path unique (path);
alter table admin_user
    add constraint FK9v4eg2l3gdkw6c3g0kvigfyfy foreign key (org_id) references organization;
alter table organization
    add constraint FKc30yedjwp9qw1f3nn2ytda7tj foreign key (parent_id) references organization;
create table change_form_command_mapping
(
    id         number(19, 0) not null,
    created_at timestamp(6),
    deleted    number(1, 0) check (deleted in (0, 1)),
    updated_at timestamp(6),
    enabled    number(1, 0)  not null check (enabled in (0, 1)),
    remark     varchar2(255 char),
    source     varchar2(255 char),
    target     varchar2(255 char),
    primary key (id)
);
alter table change_form_host add serverip varchar2(255 char);
alter table change_form_item drop column "TYPE";
alter table change_form_item add change_type varchar2(255 char);
alter table change_form_item add change_type2 varchar2(255 char);
alter table change_form_item add type_check varchar2(255 char);
alter table wxtsync_command_record add cmd_too_many_sessions clob;

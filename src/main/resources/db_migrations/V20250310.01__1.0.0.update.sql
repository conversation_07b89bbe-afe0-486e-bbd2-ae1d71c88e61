alter table rirm2_itx_command add match_result varchar2(255 char);
update rirm2_itx_command set match_result = 'Matched' where is_matched = 1;
update rirm2_itx_command set match_result = 'NoChangeFormHost' where is_matched = 0 and audit_result = 'NoChangeFormHost';
update rirm2_itx_command set match_result = 'Unmatched' where is_matched = 0 and audit_result != 'NoChangeFormHost';
update rirm2_itx_command set audit_result = 'Pending' where is_matched = 0 and audit_result = 'NoChangeFormHost';
alter table rirm2_itx_command drop column is_matched;

alter table change_form_item drop column ITX_CMD_AUDIT_COUNT;
alter table change_form_item drop column F_CONFIRMED_COUNT;
alter table change_form_item drop column F_UNPROCESS_COUNT;
alter table change_form_item drop column ITX_CMD_PROCESS_COUNT;
alter table change_form_item drop column F_UNPROCESS_TOTAL_COUNT;
alter table change_form_item drop column F_CONFIRMED_TOTAL_COUNT;
alter table change_form_item drop column F_MISS_MATCH_COUNT;
create table change_form_item_stats (id number(19,0) not null, created_at timestamp(6), deleted number(1,0) check (deleted in (0,1)), updated_at timestamp(6), stats_key varchar2(255 char), stats_val number(19,0), change_form_item_id number(19,0), primary key (id));

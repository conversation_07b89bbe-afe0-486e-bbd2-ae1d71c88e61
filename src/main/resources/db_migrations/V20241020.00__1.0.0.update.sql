alter table change_form_sync_record
    add sync_time_begin timestamp(6);
alter table change_form_sync_record
    add sync_time_end timestamp(6);
alter table change_form_sync_record drop column api_config;
alter table rirm2_itx_command
    add replay_info varchar2(1024 char);
alter table rirm2_itx_command drop column screenseq;
alter table rirm2_itx_event_queue
    add replay_info varchar2(1024 char);
create table wxtsync_command_record
(
    id                     number(19,0) not null,
    created_at             timestamp(6),
    deleted                number(1,0) check (deleted in (0,1)),
    updated_at             timestamp(6),
    add_command_count      number(19,0) not null,
    add_session_count      number(19,0) not null,
    api_down_bytes         number(19,0) not null,
    api_fail_request_count number(19,0) not null,
    api_list_data_count    number(19,0) not null,
    api_succ_request_count number(19,0) not null,
    api_use_time           number(19,0) not null,
    errmsg                 varchar2(255 char),
    ignore_session_count   number(19,0) not null,
    success                number(1,0) not null check (success in (0,1)),
    sync_time_begin        timestamp(6),
    sync_time_end          timestamp(6),
    primary key (id)
);

create index i_ar_alertRule on alert_record (alert_rule_id)  ONLINE;
create index i_ar_sess on alert_record (sess_id) ONLINE;
create index i_ar_cmdRule on alert_record (cmd_rule_id) ONLINE;
create index i_alert_rule_commandRule on alert_rule (command_rule_id) ONLINE;
create index i_cf_cmd_itemId on change_form_command (change_form_item_id) ONLINE;
create index i_cf_cmd_formId on change_form_command (change_form_id) ONLINE;
create index i_cf_host_itemId on change_form_host (change_form_item_id) ONLINE;
create index i_cf_host_formId on change_form_host (change_form_id) ONLINE;
create index i_cf_host_num on change_form_host (id_ops_number) ONLINE;
create index i_cf_host_time1 on change_form_host (begin_time) ONLINE;
create index i_cf_host_time2 on change_form_host (end_time) ONLINE;
create index i_cf_item_time1 on change_form_item (plan_begin_time) ONLINE;
create index i_cf_item_time2 on change_form_item (plan_end_time) ONLINE;
create index i_cfs_record_ctime_succ on change_form_sync_record (created_at, success) ONLINE;
create index i_cc_path on command_category (path) ONLINE;
create index i_cc_parent on command_category (parent_id) ONLINE;
create index i_cr_commandCategory on command_rule (command_category_id) ONLINE;
create index file_folder_i_parent on file_folder (parent_id) ONLINE;
create index organization_i_parent on organization (parent_id) ONLINE;
create index i_ric_cfItem on rirm2_itx_command (change_form_item_id) ONLINE;
create index i_ric_session on rirm2_itx_command (session_id) ONLINE;
create index i_ric_rule on rirm2_itx_command (rule_id) ONLINE;
alter table rirm2_itx_command drop constraint RIRM2_ITX_COMMAND_I_CMDID ONLINE;
alter table rirm2_itx_command add constraint i_ric_cmdId unique (cmd_id);
create index i_rieq_sessionId on rirm2_itx_event_queue (session_id) ONLINE;
create index i_wxtsc_record_ctime_succ on wxtsync_command_record (created_at, success) ONLINE;


create table admin_role
(
    id          number(19, 0) not null,
    created_at  timestamp(6),
    deleted     number(1, 0) check (deleted in (0, 1)),
    updated_at  timestamp(6),
    permissions clob,
    role_name   varchar2(255 char),
    primary key (id)
);
alter table admin_user add role_id number(19, 0);
alter table admin_user add constraint FKovciu5m099lrc4e1dbhmol80l foreign key (role_id) references admin_role;


package com.vsimtone.rirm2.backend.json

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.ser.std.StdSerializer
import com.vsimtone.rirm2.backend.bean.ApiResp
import java.io.IOException

class ApiRespSerializer : StdSerializer<ApiResp>(ApiResp::class.java) {

    @Throws(IOException::class)
    override fun serialize(value: ApiResp, gen: JsonGenerator, provider: SerializerProvider) {
        gen.writeStartObject()
        gen.writeNumberField("errcode", value.errcode)
        gen.writeStringField("errmsg", value.errmsg)
        gen.writeFieldName("data")
        provider.defaultSerializeValue(value.data, gen)
        gen.writeEndObject()
    }

}
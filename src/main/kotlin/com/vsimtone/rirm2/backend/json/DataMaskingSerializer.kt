package com.vsimtone.rirm2.backend.json

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.SerializerProvider
import java.io.IOException

class DataMaskingSerializer : JsonSerializer<String?>() {
    @Throws(IOException::class)
    override fun serialize(s: String?, jsonGenerator: JsonGenerator, serializerProvider: SerializerProvider) {
        if (s == null) jsonGenerator.writeNull() else {
            jsonGenerator.writeString(autoMask(s))
        }
    }

    companion object {
        fun maskString(str: String, maskFirst: Boolean): String {
            val s = StringBuffer()
            for (i in str.indices) {
                if (maskFirst) {
                    if (i >= str.length / 2) s.append(str[i]) else s.append("*")
                } else {
                    if (i >= str.length / 2) s.append("*") else s.append(str[i])
                }
            }
            return s.toString()
        }

        fun autoMask(s: String?): String? {
            s ?: return null
            return if (s.indexOf("@") != -1) {
                val a = s.split("@".toRegex()).toTypedArray()
                maskString(a[0], false) + "@" + a[1]
            } else if (s.matches(Regex(".*\\d{11,20}.*"))) {
                s.substring(0, 4) + "****" + s.substring(s.length - 4)
            } else {
                maskString(s, true)
            }
        }
    }
}
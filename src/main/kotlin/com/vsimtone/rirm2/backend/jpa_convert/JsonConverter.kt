package com.vsimtone.rirm2.backend.jpa_convert

import com.vsimtone.rirm2.backend.utils.JSONUtils
import javax.persistence.AttributeConverter
import java.io.Serializable

open class JsonConverter<T>(impl: Class<T>) : AttributeConverter<T, String>, Serializable {

    private val implCls: Class<T>?

    init {
        this.implCls = impl
    }

    override fun convertToDatabaseColumn(attribute: T?): String? {
        if (attribute == null) return null;
        return JSONUtils.toString(attribute as Any)
    }

    override fun convertToEntityAttribute(dbData: String?): T? {
        if (dbData == null || dbData.isEmpty()) return null
        return JSONUtils.toObject(dbData, implCls!!);
    }
}

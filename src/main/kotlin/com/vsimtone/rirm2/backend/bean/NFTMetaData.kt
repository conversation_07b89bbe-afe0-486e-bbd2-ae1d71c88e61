package com.vsimtone.rirm2.backend.bean

import com.fasterxml.jackson.annotation.JsonAlias
import java.io.Serializable

class NFTMetaData() : Serializable {

    companion object {
        class Attribute {
            var type: String? = null
            var display_type: String? = null
            var trait_type: String? = null
            var description: String? = null
            var value: String? = null
            var display_value: String? = null
            var name: String? = null
            var `class`: String? = null
            var css = mutableMapOf<String, String>()
            var required: String? = null
        }
    }

    @JsonAlias(value = arrayOf("name", "title"))
    var name: String? = null

    var type: String? = null

    @JsonAlias(value = arrayOf("image", "image_original_url", "image_url", "image_link"))
    var image: String? = null
    var image_data: String? = null
    var description: String? = null

    @JsonAlias(value = arrayOf("external_url", "external_link"))
    var external_url: String? = null


    var attributes = mutableListOf<Attribute>()

    var properties = mutableMapOf<String, Attribute>()

    var background_color: String? = null

    var animation_url: String? = null

    var youtube_url: String? = null
}

package com.vsimtone.rirm2.backend.bean

import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable

class PageResult<T>(content: List<T>, pageable: Pageable, total: Long) : PageImpl<T>(content, pageable, total) {
    fun mutableContent(): MutableList<T> {
        val f = this.javaClass.superclass.superclass.getDeclaredField("content")
        f.isAccessible = true
        return f.get(this) as ArrayList<T>;
    }
}
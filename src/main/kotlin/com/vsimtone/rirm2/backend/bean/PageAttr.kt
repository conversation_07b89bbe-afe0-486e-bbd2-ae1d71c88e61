package com.vsimtone.rirm2.backend.bean

import com.fasterxml.jackson.annotation.JsonIgnore
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 14-11-7.
 */
class PageAttr {
    var page = -1L
    var size = -1L
    var countTotal = true

    companion object {
        const val FIRST_NUM = 1L;

        class PageReq(page: Int, size: Int, sort: Sort) : PageRequest(page, size, sort) {
        }
    }

    var sort: ArrayList<String>? = null

    constructor(page: Long, size: Long) {
        this.page = page
        this.size = size
    }

    constructor(page: Long, size: Long, sort: ArrayList<String>?) {
        this.page = page
        this.size = size
        this.sort = sort
    }

    constructor(page: Long, size: Long, sort: ArrayList<String>?, countTotal: <PERSON>olean) {
        this.page = page
        this.size = size
        this.sort = sort
        this.countTotal = countTotal
    }

    constructor(size: Long) {
        this.size = size
    }

    constructor()

    init {
        sort = ArrayList()
    }

    @JsonIgnore
    fun get(): PageRequest {
        if (size < 0) size = 15
        if (page < 1) page = 1
        if (sort != null && sort!!.size == 0) {
            sort!!.add("desc_id")
        }
        val orders = ArrayList<Sort.Order>()
        if (sort != null) {
            for (_s in sort!!) {
                if (_s == "null") continue
                var s = _s
                s = s.replace("[\\s`()]+".toRegex(), "")
                val ss = s.split("_".toRegex(), 2).toTypedArray()
                if (ss.size != 2)
                    throw RuntimeException("Sort is wrong : $s")
                orders.add(Sort.Order(Sort.Direction.fromString(ss[0]), ss[1]))
            }
        }
        var sortObj = Sort.unsorted()
        if (sort != null) sortObj = Sort.by(orders)
        return PageReq((page - 1L).toInt(), size.toInt(), sortObj)
    }
}

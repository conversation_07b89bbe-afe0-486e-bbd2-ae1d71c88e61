package com.vsimtone.rirm2.backend.bean

import java.io.Serializable

class ApiResp : Serializable {
    var data: Any? = null
    var errcode: Int = 0
    var errmsg: String? = null

    companion object {
        // 结果定义，表单验证不通过
        var RESULT_FORM_INVALIDATE = 200

        // 结果定义，验证码错误
        var RESULT_CAPTCHA_INVALIDATE = 201


        fun error(code: Int): ApiResp {
            var resp = ApiResp()
            resp.errcode = code
            return resp;
        }

    }

    constructor() {}

    constructor(data: Any?) {
        this.data = data
    }

    constructor(errcode: Int) {
        this.errcode = errcode
    }

    constructor(errcode: Int, errmsg: String?) {
        this.errcode = errcode
        this.errmsg = errmsg
    }
}
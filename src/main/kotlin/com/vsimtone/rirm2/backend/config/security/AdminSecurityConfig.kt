package com.vsimtone.rirm2.backend.config.security


import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.CurrAuthedUser
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.AdminUser
import com.vsimtone.rirm2.backend.service.APPConfigService
import com.vsimtone.rirm2.backend.service.AdminUserService
import com.vsimtone.rirm2.backend.service.CaptchaService
import com.vsimtone.rirm2.backend.service.OptLogService
import com.vsimtone.rirm2.backend.service.RedisService
import com.vsimtone.rirm2.backend.utils.JSONUtils
import com.vsimtone.rirm2.backend.utils.Log
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.annotation.Order
import org.springframework.security.authentication.*
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.core.Authentication
import org.springframework.security.core.AuthenticationException
import org.springframework.security.core.GrantedAuthority
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.security.core.userdetails.UsernameNotFoundException
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter
import org.springframework.security.web.context.HttpSessionSecurityContextRepository
import org.springframework.security.web.util.matcher.AntPathRequestMatcher


@Configuration
@Order(2)
@EnableWebSecurity
@EnableMethodSecurity(securedEnabled = true, prePostEnabled = true, jsr250Enabled = true)
class AdminSecurityConfig : SecurityConfig() {

    private val logger = Log["admin_security"]

    @Autowired
    lateinit var userService: AdminUserService

    @Autowired
    lateinit var captchaService: CaptchaService

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    lateinit var eventPublisher: AuthenticationEventPublisher

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var optLogService: OptLogService

    var adminLoginAuthenticationFilter = AdminLoginAuthenticationFilter();

    class AdminLoginAuthToken(a: MutableCollection<GrantedAuthority>? = null) : AbstractAuthenticationToken(a) {
        lateinit var username: String;
        lateinit var password: String;

        override fun getCredentials(): Any {
            return ""
        }

        override fun getPrincipal(): Any {
            return username
        }
    }

    inner class AdminLoginAuthenticationFilter() :
        AbstractAuthenticationProcessingFilter(AntPathRequestMatcher("/users/login", "POST")) {
        init {
            setAuthenticationSuccessHandler() { _: HttpServletRequest, resp: HttpServletResponse, _: Authentication ->
                OutputResp(resp, ApiResp(null))
            }
            setAuthenticationFailureHandler() { _: HttpServletRequest, resp: HttpServletResponse, a: AuthenticationException ->
                OutputResp(resp, ApiResp(1, a.message))
            }
            setSecurityContextRepository(HttpSessionSecurityContextRepository())
        }

        override fun attemptAuthentication(
            request: HttpServletRequest,
            response: HttpServletResponse
        ): Authentication? {
            var username = request.getParameter("username");
            var password = request.getParameter("password");
            var domain = request.getParameter("domain");
            var captcha = request.getParameter("captcha");
            val session = request.getSession(false)
            if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
                OutputResp(response, ApiResp(1, "用户账户或密码错误"))
                return null
            }
            if (!captchaService.validateImage(session, captcha)) {
                OutputResp(response, ApiResp(1, "验证码错误"))
                return null
            }

            if (StringUtils.isNotEmpty(domain) && domain != "local") username = "$username@$domain"
            var token = AdminLoginAuthToken()
            token.username = username
            token.password = password
            return this.authenticationManager.authenticate(token)
        }

    }

    inner class AdminAuthProvider : AuthenticationProvider {

        override fun authenticate(authentication: Authentication): Authentication? {
            var token = authentication as AdminLoginAuthToken
            var user: AdminUser? = null
            val username = token.username
            val password = token.password
            token.password = "";
            token.details = null
            redisService.runInLock("login:${username}") {
                val _user = userService.findByUsername(username) ?: throw UsernameNotFoundException("用户账户或密码错误");
                try {
                    val errMsg = userService.checkPassword(_user, password)
                    if (errMsg != null) {
                        throw BadCredentialsException("认证失败: ${errMsg}")
                    }
                } catch (e: Exception) {
                    if (e is BadCredentialsException) throw e;
                    throw InternalAuthenticationServiceException("密码验证失败,请联系管理员!", e)
                }
                if (_user.username == "admin") {
                    _user.permissions = AdminPermissions.getAll().toMutableList()
                }
                if (_user.permissions.isEmpty() && (_user.role == null || _user.role!!.permissions.isEmpty()))
                    throw InsufficientAuthenticationException("用户无管理员权限")
                if (!_user.enabled) throw LockedException("用户已被禁用")
                if (_user.locked) throw DisabledException("用户已被锁定")
                user = _user
            }
            val details = CurrAuthedUser()
            if (user != null) {
                val adminUser = user as AdminUser
                details.id = adminUser.id!!
                details.nickname = adminUser.nickname
                details.username = adminUser.username
                details.permissions = adminUser.permissions
                if (adminUser.role != null) {
                    details.permissions.addAll(adminUser.role!!.permissions)
                    details.permissions = details.permissions.toSet().toMutableList()
                }
                if (details.permissions.isNotEmpty()) {
                    token = AdminLoginAuthToken(details.permissions.map { SimpleGrantedAuthority(it) }.toMutableSet());
                    token.username = username;
                    token.details = details;
                    token.isAuthenticated = true;
                    return token;
                }
            }
            return null;
        }

        override fun supports(authentication: Class<*>?): Boolean {
            if (authentication == AdminLoginAuthToken::class.java) return true
            return false
        }

    }

    @Bean
    fun authenticationManagerBean(): AuthenticationManager {
        val m = ProviderManager(AdminAuthProvider())
        m.setAuthenticationEventPublisher(eventPublisher)
        adminLoginAuthenticationFilter.setAuthenticationManager(m)
        return m;
    }

    @Bean
    fun filterChain(adminHttpSecurity: HttpSecurity): SecurityFilterChain {
        adminHttpSecurity.authenticationManager(authenticationManagerBean());
        adminHttpSecurity.antMatcher("/**")
        adminHttpSecurity.addFilterBefore(
            adminLoginAuthenticationFilter,
            UsernamePasswordAuthenticationFilter::class.java
        )
        commonConfig(
            adminHttpSecurity, arrayOf(
                "/admin_users/curr",
                "/files/**",
                "/file_infos/upload",
                "/excel_imports/upload",
                "/mock_api/**",
                "/files",
                "/captcha"
            )
        )
        adminHttpSecurity.logout() {
            it.logoutUrl("/users/logout").clearAuthentication(true)
            it.invalidateHttpSession(true)
            it.addLogoutHandler { request, response, authentication ->
                val user = userService.currUser()!!
                logger.info("User logout ${user}")
                optLogService.addOptLog(user.username, "退出登录", "用户登录", user.nickname, user);
            }
            it.logoutSuccessHandler { _, response, _ ->
                response.status = 200;
                response.writer.print(JSONUtils.toString(ApiResp()))
            }
        }
        return adminHttpSecurity.build();
    }

}


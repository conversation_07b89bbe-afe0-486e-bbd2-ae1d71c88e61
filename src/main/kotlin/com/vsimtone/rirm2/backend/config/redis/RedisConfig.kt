package com.vsimtone.rirm2.backend.config.redis

import com.fasterxml.jackson.databind.ObjectMapper
import org.apache.commons.lang3.StringUtils
import org.redisson.Redisson
import org.redisson.api.RedissonClient
import org.redisson.client.codec.StringCodec
import org.redisson.codec.SerializationCodec
import org.redisson.config.Config
import org.redisson.config.TransportMode
import org.redisson.spring.cache.CacheConfig
import org.redisson.spring.cache.RedissonSpringCacheManager
import org.redisson.spring.data.connection.RedissonConnectionFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.cache.CacheManager
import org.springframework.cache.annotation.EnableCaching
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import oshi.PlatformEnum
import oshi.SystemInfo


@Configuration
@EnableCaching
class RedisConfig {

    @Autowired
    private val springRedisConfig: SpringRedisConfig? = null

    @Autowired
    lateinit var objectMap: ObjectMapper

    val config: Config
        get() {
            val config = Config()
            if (SystemInfo.getCurrentPlatform() == PlatformEnum.LINUX)
                config.transportMode = TransportMode.EPOLL
            config.useSingleServer()
                .setClientName("rirm2_server")
                .setKeepAlive(true)
                .setTcpNoDelay(true)
                .setAddress("redis://" + springRedisConfig!!.host + ":" + springRedisConfig.port)
                .setPassword(if (StringUtils.isEmpty(springRedisConfig.password)) null else springRedisConfig.password)
                .setDatabase(springRedisConfig.database)
                .setConnectionMinimumIdleSize(springRedisConfig.minIdleSize)
                .setConnectTimeout(springRedisConfig.timeout)
                .setConnectionPoolSize(springRedisConfig.poolSize)
                .setPingConnectionInterval(60 * 1000)
            config.codec = SerializationCodec();
            config.lockWatchdogTimeout = 60 * 1000L // 默认锁续期时间1分钟
            return config
        }

    @Bean
    fun redisson(): Redisson {
        return Redisson.create(config) as Redisson
    }

    @Bean
    fun cacheManager(redissonClient: RedissonClient): CacheManager {
        val config = HashMap<String, CacheConfig>()
        val cacheManager = RedissonSpringCacheManager(redissonClient, config, StringCodec()) //JsonJacksonCodec(objectMap) , MarshallingCodec
        return cacheManager
    }

    @Bean
    fun redissonConnectionFactory(redisson: RedissonClient): RedissonConnectionFactory {
        return RedissonConnectionFactory(redisson)
    }
}

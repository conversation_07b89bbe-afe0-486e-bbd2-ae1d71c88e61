package com.vsimtone.rirm2.backend.config

import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse
import org.springframework.boot.web.servlet.ServletListenerRegistrationBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.web.session.HttpSessionEventPublisher
import org.springframework.session.web.http.CookieHttpSessionIdResolver
import org.springframework.session.web.http.DefaultCookieSerializer
import org.springframework.session.web.http.HttpSessionIdResolver


@Configuration
class SessionConfig {


    class HttpSessionIdResolverByPath : HttpSessionIdResolver {
        var mapping: Map<String, HttpSessionIdResolver>

        constructor(mapping: Map<String, HttpSessionIdResolver>) {
            this.mapping = mapping;
        }

        override fun setSessionId(request: HttpServletRequest, response: HttpServletResponse, sessionId: String) {
            var path = request.servletPath
            var list = mutableListOf<String>()
            mapping.forEach { (p, r) ->
                if (path.startsWith(p)) {
                    r.setSessionId(request, response, sessionId)
                    return@forEach
                }
            }
        }

        override fun expireSession(request: HttpServletRequest, response: HttpServletResponse) {
            var path = request.servletPath
            var list = mutableListOf<String>()
            mapping.forEach { (p, r) ->
                if (path.startsWith(p)) {
                    r.expireSession(request, response)
                    return@forEach
                }
            }
        }

        override fun resolveSessionIds(request: HttpServletRequest): MutableList<String> {
            var path = request.servletPath
            var list = mutableListOf<String>()
            mapping.forEach { (p, r) ->
                if (path.startsWith(p)) {
                    list = r.resolveSessionIds(request)
                    return@forEach
                }
            }
            return list
        }

    }

    fun getHttpSessionIdResolveByName(name: String): HttpSessionIdResolver {
        var cookieHttpSessionIdResolver = CookieHttpSessionIdResolver()
        var serializer = DefaultCookieSerializer();
        serializer.setCookieName(name);
        serializer.setCookiePath("/");
        serializer.setCookieMaxAge(72 * 3600);
        serializer.setUseHttpOnlyCookie(true);
//        serializer.setUseSecureCookie(true);
        cookieHttpSessionIdResolver.setCookieSerializer(serializer);
        return cookieHttpSessionIdResolver;
    }

    @Bean
    fun httpSessionIdResolver(): HttpSessionIdResolver {

        return HttpSessionIdResolverByPath(
            mapOf(
                "/" to getHttpSessionIdResolveByName("_R2SID_")
            )
        );
    }

    @Bean
    fun httpSessionEventPublisher(): ServletListenerRegistrationBean<HttpSessionEventPublisher> {
        return ServletListenerRegistrationBean(HttpSessionEventPublisher())
    }
}
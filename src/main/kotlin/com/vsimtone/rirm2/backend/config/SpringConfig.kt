package com.vsimtone.rirm2.backend.config

import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.json.JsonMapper
import com.vsimtone.rirm2.backend.entity.FileInfo
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.utils.CryptUtils
import com.vsimtone.rirm2.backend.utils.Log
import org.flywaydb.core.internal.resolver.ChecksumCalculator
import org.flywaydb.core.internal.resource.filesystem.FileSystemResource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties
import org.springframework.boot.jdbc.DataSourceBuilder
import org.springframework.cache.annotation.EnableCaching
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.converter.HttpMessageConverter
import org.springframework.http.converter.json.AbstractJackson2HttpMessageConverter
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.scheduling.TaskScheduler
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler
import org.springframework.util.ResourceUtils
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer
import java.io.File
import java.util.concurrent.Executor
import java.util.concurrent.ThreadPoolExecutor
import javax.sql.DataSource


@Configuration
@EnableCaching
class SpringConfig : WebMvcConfigurer {

    @Autowired
    lateinit var jsonMapperBuilder: JsonMapper.Builder;

    var logger: Log = Log.get(SpringConfig::class.java)

    @Bean
    fun taskScheduler(): TaskScheduler {
        val taskScheduler = ThreadPoolTaskScheduler()
        taskScheduler.poolSize = 40
        return taskScheduler
    }

    @Bean
    fun taskExecutor(): Executor {
        val executor = ThreadPoolTaskExecutor()
        executor.corePoolSize = 10
        executor.maxPoolSize = 600
        executor.setQueueCapacity(6000)
        executor.keepAliveSeconds = 60
        executor.threadNamePrefix = "sys-executor-"
        executor.setRejectedExecutionHandler(ThreadPoolExecutor.CallerRunsPolicy())
        return executor
    }

    override fun configureMessageConverters(converters: MutableList<HttpMessageConverter<*>>) {
        val mapper = jsonMapperBuilder.enable(MapperFeature.DEFAULT_VIEW_INCLUSION).build()
        mapper.setConfig(mapper.serializationConfig.withView(JsonViews.Default::class.java))
        converters.removeIf { it is AbstractJackson2HttpMessageConverter }
        converters.add(MappingJackson2HttpMessageConverter(mapper));
    }

    @Bean
    fun dataSource(dsp: DataSourceProperties): DataSource {
        val db_migrations = ResourceUtils.getFile("classpath:db_migrations");
        if (db_migrations.exists()) {
            db_migrations.listFiles { file ->
                if (file.isFile) {
                    val checksum = ChecksumCalculator.calculate(FileSystemResource(null, file.path, Charsets.UTF_8, false))
                    this.logger.info("DB migration file ${file.name} checksum is ${checksum}");
                }
                true
            }
        }
        if (dsp.password.startsWith("sm4:gcm:")) {
            dsp.password = CryptUtils.DecodeSM4GCMPassword("rirm2", dsp.password.substring(8))
        }
        return dsp.initializeDataSourceBuilder().build()
    }

}

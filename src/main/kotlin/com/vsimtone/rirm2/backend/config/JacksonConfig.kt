package com.vsimtone.rirm2.backend.config

import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.json.JsonMapper
import com.fasterxml.jackson.datatype.hibernate5.Hibernate5Module
import org.springframework.beans.factory.config.BeanDefinition.SCOPE_PROTOTYPE
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Scope
import java.text.SimpleDateFormat


@Configuration
class JacksonConfig {
    @Bean
    @Scope(SCOPE_PROTOTYPE)
    fun jsonMapperBuilder(): JsonMapper.Builder {
        var build = JsonMapper.builder();
//        build.serializerByType(PageImpl::class.java, PageSerializer())
//        build.serializerByType(RestResp::class.java, RestRespSerializer())
        build.enable(MapperFeature.DEFAULT_VIEW_INCLUSION)
        build.enable(MapperFeature.USE_ANNOTATIONS)
        build.defaultDateFormat(SimpleDateFormat("yyyy-MM-dd HH:mm:ss"))
        build.addModules(Hibernate5Module())
        return build
    }
}
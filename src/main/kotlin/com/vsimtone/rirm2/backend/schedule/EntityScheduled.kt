package com.vsimtone.rirm2.backend.schedule

import com.google.common.base.CaseFormat
import com.querydsl.core.BooleanBuilder
import com.querydsl.core.types.Predicate
import com.querydsl.core.types.dsl.EntityPathBase
import com.querydsl.core.types.dsl.NumberPath
import com.querydsl.jpa.impl.JPAQuery
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.entity.BaseEntity
import com.vsimtone.rirm2.backend.utils.DateUtil
import com.vsimtone.rirm2.backend.utils.Log
import javax.persistence.EntityManager
import org.springframework.beans.factory.InitializingBean
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.Pageable
import java.util.*

/**
 * Created by z<PERSON><PERSON>n on 16-4-6.
 */
abstract class EntityScheduled<T : BaseEntity> : InitializingBean {

    @Autowired
    lateinit var scheduledService: ScheduledService

    @Autowired
    lateinit var entityManager: EntityManager

    protected var items = HashMap<String, ScheduledItem>()

    protected lateinit var entityClass: Class<T>

    lateinit var a: EntityPathBase<T>

    inner class EntityScheduledItem : ScheduledItem() {
        lateinit var key: String
        var fullPageQuery = false
        var pageSize = 100L
        var onlyIdColumn: NumberPath<Long>? = null
        var sort = arrayListOf<String>()
        var countTotal = false;

        fun config(cb: (EntityScheduledItem) -> Unit) {
            cb(this)
        }

        override fun scheduled() {
            val currDate = Date()
            var pageable: Pageable? = PageAttr(PageAttr.FIRST_NUM, pageSize, sort).get()
            val queries = BooleanBuilder()
            queries.and(listQuery(key, currDate))
            var totalProcessesSize = 0L
            var totalSize = -1L;
            val prevIds = mutableListOf<Long>()
            val started = System.currentTimeMillis()
            beforeProcess(key, currDate)
            do {
                val q = JPAQuery<T>(entityManager).from(a)
                if (onlyIdColumn != null)
                    q.select(onlyIdColumn)
                q.where(queries).offset(pageable!!.offset).limit(pageable.pageSize.toLong())

                if (totalSize == -1L) {
                    if (countTotal) totalSize = q.clone().select(a.count()).fetchOne() ?: 0
                    else totalSize = Long.MAX_VALUE;
                }

                val rawPage = PageImpl(q.fetch() as List<Any>, pageable, totalSize)
                if (rawPage.content.size == 0) break

                var page: PageImpl<T>
                if (onlyIdColumn != null)
                    page = PageImpl<T>(rawPage.content.map {
                        val e = entityClass.getDeclaredConstructor().newInstance()
                        e.id = it as Long
                        return@map e as T
                    }, rawPage.pageable, rawPage.totalElements)
                else
                    page = rawPage as PageImpl<T>
                beforePageProcess(page, key, currDate)
                var processedCount = 0
                for (t in page.content) {
                    if (prevIds.contains(t.id!!)) {
                        val errMsg = "Entity scheduled data repeat, ${this.javaClass.simpleName} ${key} ${pageable} ${t.id!!}"
                        if (fullPageQuery)
                            logger.warn(errMsg)
                        else
                            throw IllegalStateException("Entity scheduled data repeat, ${this.javaClass.simpleName} ${key} ${pageable} ${t.id!!}")
                    } else {
                        if (!fullPageQuery) prevIds.add(t.id!!)
                        process(t, key, currDate)
                        processedCount++
                    }
                }
                if (fullPageQuery || onlyIdColumn != null || processedCount == 0)
                    pageable = page.nextPageable()
                afterPageProcess(page, key, currDate, this)
                totalProcessesSize += page.content.size
                entityManager.flush()
            } while (pageable != Pageable.unpaged())
            afterProcess(totalProcessesSize, key, currDate)
            if (totalProcessesSize > 0) logger.info("${this} done, process_size=${totalProcessesSize}, total_use=${DateUtil.useTimeToHum(System.currentTimeMillis() - started)}")
        }
    }

    protected fun defineItem(key: String, fixDelay: Long): EntityScheduledItem {
        val item = EntityScheduledItem()
        val clsName = CaseFormat.LOWER_CAMEL.converterTo(CaseFormat.LOWER_UNDERSCORE).convert(entityClass.simpleName)
        item.key = key
        item.setId("entity:${clsName}:$key")
        item.fixDelay = fixDelay
        item.runInTran = true
        scheduledService.addItem(item)
        items.put(key, item)
        return item
    }

    protected fun defineItem(key: String, cron: String): EntityScheduledItem {
        val clsName = CaseFormat.LOWER_CAMEL.converterTo(CaseFormat.LOWER_UNDERSCORE).convert(entityClass.simpleName)
        val item = EntityScheduledItem()
        item.key = key
        item.setId("entity:${clsName}:$key")
        item.setCron(cron)
        item.runInTran = true
        scheduledService.addItem(item)
        items.put(key, item)
        return item
    }


    protected var logger = Log.get(this.javaClass)

    abstract fun init(): EntityPathBase<T>

    abstract fun listQuery(key: String, currDate: Date): Predicate?

    abstract fun process(o: T, key: String, currDate: Date)

    open fun beforePageProcess(page: Page<T>, key: String, currDate: Date) {}


    open fun afterPageProcess(page: Page<T>, key: String, currDate: Date, item: EntityScheduledItem) {
    }

    open fun beforeProcess(key: String, currDate: Date) {}


    open fun afterProcess(totalProcessesSize: Long, key: String, currDate: Date) {

    }


    @Throws(Exception::class)
    override fun afterPropertiesSet() {
        a = init()
        if (entityClass == null)
            throw IllegalStateException("entityClass must be set.")
    }
}
package com.vsimtone.rirm2.backend.entity

import com.querydsl.core.annotations.Config
import com.vsimtone.rirm2.backend.jpa_convert.JsonMapConverter
import javax.persistence.*
import java.util.*

//汇总单
@Table(
    indexes = [
        Index(name = "i_cf_item_time1", columnList = "planBeginTime", unique = false),
        Index(name = "i_cf_item_time2", columnList = "planEndTime", unique = false),
    ]
)
@Entity
@Config(defaultVariableName = "a")
class ChangeFormItem : BaseEntity() {

    companion object {
        enum class TypeCheck { Normal, Error, UnConfirmed }
    }

    @Column(length = 512, unique = true)
    lateinit var formId: String
    lateinit var name: String
    var team: String? = null
    var planBeginTime: Date? = null
    var planEndTime: Date? = null
    var statusText: String? = null

    @Lob
    var reasonText: String? = null

    @Lob
    var descText: String? = null

    @ManyToOne
    var fromImport: ExcelImport? = null

    var hostCount = 0L;
    var cmdCount = 0L;

    // 执行命令总数
    var itxCmdTotalCount = 0L;

    var sponsorUsers: String? = null;

    var sysNames: String? = null;

    var matchTime: Date? = null

    var statsTime: Date? = null

    var changeType: String? = null

    var changeType2: String? = null

    @Enumerated(EnumType.STRING)
    var typeCheck: TypeCheck? = null

    @ManyToOne
    var typeCheckEditUser: AdminUser? = null

    var typeCheckEditRemark: String? = null

    override fun toString(): String {
        return "ChangeFormItem(id='$id', formId='$formId', " +
            "typeCheck=$typeCheck, " +
            "planBeginTime=${DEFAULT_DATE_FORMAT.format(planBeginTime)}, " +
            "planEndTime=${DEFAULT_DATE_FORMAT.format(planEndTime)}, " +
            "hostCount=$hostCount, " +
            "cmdCount=$cmdCount, " +
            "itxCmdTotalCount=$itxCmdTotalCount, " +
            "matchTime=${if (matchTime != null) DEFAULT_DATE_FORMAT.format(matchTime) else "null"}"
    }

}
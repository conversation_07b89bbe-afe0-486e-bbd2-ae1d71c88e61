package com.vsimtone.rirm2.backend.entity

import com.querydsl.core.annotations.Config
import javax.persistence.*
import java.util.*

//控制室日志
@Table(
    indexes = [
        Index(name = "i_cf_host_itemId", columnList = "changeFormItem.id", unique = false),
        Index(name = "i_cf_host_formId", columnList = "changeFormId", unique = false),
        Index(name = "i_cf_host_num", columnList = "idOpsNumber", unique = false),
        Index(name = "i_cf_host_time1", columnList = "beginTime", unique = false),
        Index(name = "i_cf_host_time2", columnList = "endTime", unique = false),
    ]
)
@Entity
@Config(defaultVariableName = "a")
class ChangeFormHost : BaseEntity() {

    @ManyToOne
    var changeFormItem: ChangeFormItem? = null

    lateinit var changeFormId: String

    lateinit var lineIndex: String
    lateinit var applyId: String
    lateinit var sponsorTime: Date
    var sponsorUser: String? = null
    var checkUser: String? = null
    var auditUser: String? = null
    var team: String? = null
    var workType: String? = null
    var address: String? = null

    @Lob
    var workRecord: String? = null

    var sysName: String? = null

    var idOpsName: String? = null

    var idOpsNumber: String? = null

    var envType: String? = null

    var beginTime: Date? = null

    var endTime: Date? = null

    var useTime: String? = null

    var sendUser: String? = null

    var recycleUser: String? = null
    
    var serverIP: String? = null


    @ManyToOne
    var fromImport: ExcelImport? = null

}

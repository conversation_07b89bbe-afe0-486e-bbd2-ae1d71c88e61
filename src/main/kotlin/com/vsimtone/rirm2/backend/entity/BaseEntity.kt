package com.vsimtone.rirm2.backend.entity

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.querydsl.core.annotations.Config
import com.vsimtone.rirm2.backend.service.IDGeneratorService
import javax.persistence.*
import org.hibernate.annotations.GenericGenerator
import org.hibernate.annotations.SQLDelete
import org.springframework.format.annotation.DateTimeFormat
import java.io.Serializable
import java.text.SimpleDateFormat
import java.util.*


@MappedSuperclass
@SQLDelete(sql = "UPDATE BaseEntity SET deleted = 1 where id = ?")
@Config(defaultVariableName = "a")
class BaseEntity : Serializable {


    companion object {
        const val DELETE_WHERE = "deleted != 1"
        const val DEFAULT_DATE_PATTERN = "yyyy-MM-dd HH:mm:ss"
        val DEFAULT_DATE_FORMAT = SimpleDateFormat(DEFAULT_DATE_PATTERN)
        
        const val SIZE_64KB = 65535 - 1; // Text
        const val SIZE_16MB = 1024 * 1024 * 16 - 1; // MediumText
        const val SIZE_1GB = 1024 * 1024 * 1024; // LongText

        class IDSerializer : JsonSerializer<Long>() {
            override fun serialize(value: Long?, gen: JsonGenerator, serializers: SerializerProvider) {
                if (value == null)
                    gen.writeNull()
                else
                    gen.writeString(value.toString())
            }
        }

        class IDDeserializer : JsonDeserializer<Long>() {
            override fun deserialize(p: JsonParser, ctxt: DeserializationContext): Long {
                return p.valueAsString.toLong()
            }
        }
    }


    @Id
    @Column(name = "id")
    @GeneratedValue(generator = "id_gen")
    @GenericGenerator(name = "id_gen", strategy = IDGeneratorService.IDGeneratorImplName)
    @JsonSerialize(using = IDSerializer::class)
    @JsonDeserialize(using = IDDeserializer::class)
    var id: Long? = null

    @DateTimeFormat(pattern = DEFAULT_DATE_PATTERN)
    @Column
    var createdAt: Date? = null

    @DateTimeFormat(pattern = DEFAULT_DATE_PATTERN)
    @Column
    var updatedAt: Date? = null

    @JsonIgnore
    @Column
    var deleted: Boolean = false

    @Transient
    @JsonSerialize
    @JsonInclude
    private var bundle: MutableMap<String, Any?>? = HashMap()


    constructor(id: Long?) {
        this.id = id
    }

    constructor() {}

    @PrePersist
    fun onCreate() {
        if (createdAt == null)
            createdAt = Date()
        if (updatedAt == null)
            updatedAt = createdAt
    }

    @PreUpdate
    fun onUpdate() {
        updatedAt = Date()
    }

    fun putBundle(name: String, value: Any?) {
        if (bundle == null) bundle = HashMap()
        bundle!![name] = value
    }

    @JsonSerialize
    @JsonInclude
    fun getBundle(): Map<String, Any?>? {
        return bundle
    }


    override fun toString(): String {
        return javaClass.simpleName + "{" +
            "id=" + id +
            ", deleted=" + deleted +
            ", createdAt=" + (if (createdAt != null) DEFAULT_DATE_FORMAT.format(createdAt) else null) +
            ", updatedAt=" + (if (updatedAt != null) DEFAULT_DATE_FORMAT.format(updatedAt) else null) +
            '}'.toString()
    }

    override fun equals(o: Any?): Boolean {
        if (this === o) return true
        if (o == null || javaClass != o.javaClass) return false

        val that = o as BaseEntity?

        return !if (id != null) id != that!!.id else that!!.id != null

    }

}

package com.vsimtone.rirm2.backend.entity

import com.fasterxml.jackson.annotation.JsonProperty
import com.querydsl.core.annotations.Config
import com.vsimtone.rirm2.backend.jpa_convert.JsonMapConverter
import javax.persistence.*
import java.util.*

@Table(
    name = "rirm2_itx_command",
    indexes = [
        Index(name = "i_ric_cmdId", columnList = "cmdId", unique = true),
        Index(name = "i_ric_cfItem", columnList = "changeFormItem.id", unique = false),
        Index(name = "i_ric_cfHost", columnList = "changeFormHost.id", unique = false),
        Index(name = "i_ric_session", columnList = "session.id", unique = false),
        Index(name = "i_ric_rule", columnList = "rule.id", unique = false),
        Index(name = "i_exec_time", columnList = "execTime", unique = false),
        Index(name = "i_audit_user", columnList = "auditUser.id", unique = false)
    ]
)
@Entity
@Config(defaultVariableName = "a")
class ITXCommand : BaseEntity() {

    companion object {
        enum class MatchResult { Matched, Unmatched }
        // 等待确认, 不合规，合规
        enum class AuditResult { Pending, NoPass, Pass }
        // 未确认、自动处理、已确认
        enum class ProcessResult { UnProcess, AutoConfirmed, Confirmed }
    }

    @ManyToOne(fetch = FetchType.LAZY)
    lateinit var session: ITXSession

    var loginUser: String? = null

    @ManyToOne(fetch = FetchType.LAZY)
    var changeFormItem: ChangeFormItem? = null

    @ManyToOne(fetch = FetchType.LAZY)
    var changeFormHost: ChangeFormHost? = null

    var execTime: Date? = null

    lateinit var cmdId: String

    @Column(length = 4096, name = "f_cmd")
    var cmd: String? = null

    @Column(length = 1024)
    var replayInfo: String? = null

    @ManyToOne(fetch = FetchType.LAZY)
    var rule: CommandRule? = null

    @Lob
    @Convert(converter = JsonMapConverter::class)
    var ruleMatchInfo = mutableMapOf<String, String>()

    var ruleCode: String? = null

    var cmdDesc: String? = null

    @Enumerated(EnumType.STRING)
    var matchResult: MatchResult? = null
    
    @ManyToOne(fetch = FetchType.LAZY)
    var auditUser: AdminUser? = null
    
    @Enumerated(EnumType.STRING)
    var auditResult: AuditResult? = null

    @Enumerated(EnumType.STRING)
    var processResult: ProcessResult? = null

    @Column(length = 2048)
    var processDesc: String? = null
    
    override fun toString(): String {
        return "ITXCommand(id=$id, session=${session.sessionId}, changeFormItem=${changeFormItem?.formId}, execTime=${BaseEntity.DEFAULT_DATE_FORMAT.format(execTime)}, cmd='$cmd', cmdId='$cmdId', auditResult=$auditResult, processResult=$processResult, processDesc=$processDesc)"
    }

}
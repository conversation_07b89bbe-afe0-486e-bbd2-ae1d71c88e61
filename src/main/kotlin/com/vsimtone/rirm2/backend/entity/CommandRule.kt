package com.vsimtone.rirm2.backend.entity

import com.querydsl.core.annotations.Config
import com.vsimtone.rirm2.backend.jpa_convert.JsonArrayConverter
import javax.persistence.*

@Table(
    indexes = [
        Index(name = "i_cr_commandCategory", columnList = "commandCategory.id", unique = false)
    ]
)
@Entity
@Config(defaultVariableName = "a")
class CommandRule : BaseEntity() {

    enum class AlertLevel { Low, Normal, High }

    enum class RegexFlag { IGNORE_CASE, AREA_NAME, AREA_ARGS, MATCH_FULL, MATCH_BEGIN, MATCH_END }

    var enabled = true

    //    规则名称-标志
    lateinit var name: String;

    //    命令分类
    @ManyToOne
    var commandCategory: CommandCategory? = null;

    //    告警等级
    @Enumerated(EnumType.STRING)
    var alertLevel: AlertLevel? = null;

    var ruleCode: String? = null;

    @Column(length = 1024)
    @Convert(converter = JsonArrayConverter::class)
    var regexFlags = mutableListOf<String>();

    @Lob
    @Convert(converter = JsonArrayConverter::class)
    var regex = mutableListOf<String>();

    lateinit var description: String;
}
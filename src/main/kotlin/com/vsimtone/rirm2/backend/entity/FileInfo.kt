package com.vsimtone.rirm2.backend.entity

import com.querydsl.core.annotations.Config
import javax.persistence.*

@Entity
@Table(
    indexes = [
        Index(name = "file_info_i_token", columnList = "token"),
        Index(name = "file_info_i_sha256sum", columnList = "sha256sum")
    ]
)
@Config(defaultVariableName = "a")
class FileInfo : BaseEntity() {

    @ManyToOne
    var folder: FileFolder? = null

    // 文件名称
    @Column(length = 512)
    lateinit var name: String

    // 文件类型
    @Column(name = "f_type")
    lateinit var type: String

    // 文件大小
    @Column(name = "f_size")
    var size: Long = 0

    // 文件标识
    @Column(length = 512)
    lateinit var token: String

    // 文件路径
    @Column(name = "f_path", length = 512)
    lateinit var path: String

    @Column(length = 256)
    lateinit var sha256sum: String

    // 禁止删除
    var disableDelete: Boolean = false


}
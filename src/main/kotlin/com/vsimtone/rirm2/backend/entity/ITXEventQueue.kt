package com.vsimtone.rirm2.backend.entity

import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.querydsl.core.annotations.Config
import javax.persistence.*

@Table(
    name = "rirm2_itx_event_queue",
    indexes = [
        Index(name = "i_rieq_sessionId", columnList = "sessionId", unique = false)
    ]
)
@Entity
@Config(defaultVariableName = "a")
class ITXEventQueue {
    companion object {
        enum class Type { SessionStart, SessionEnd, UnixScreen, UnixCommand, BOCCommand }
    }

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.SEQUENCE)
    @JsonSerialize(using = BaseEntity.Companion.IDSerializer::class)
    @JsonDeserialize(using = BaseEntity.Companion.IDDeserializer::class)
    var id: Long? = null

    @Enumerated(EnumType.STRING)
    lateinit var type: Type

    @Column(length = 2048)
    lateinit var sessionId: String

    @Column(length = 2048)
    var clientIP: String? = null
    
    @Column(length = 2048)
    var serverIP: String? = null

    @Enumerated(EnumType.STRING)
    var serverType: ITXSession.Type? = null

    @Column(length = 2048)
    var loginUser: String? = null

    var eventSEQ: Long = 0

    @Column(length = 2048)
    var replayInfo: String? = null

    @Lob
    @Column()
    var body: String? = null

    @Column(precision = 38)
    var auditTime: Long? = null

    override fun toString(): String {
        return "ITXEventQueue(id=$id, type=$type, sessionId='$sessionId', clientIP=$clientIP, serverIP=$serverIP, serverType=$serverType, loginUser=$loginUser, eventSEQ=$eventSEQ, body=$body, auditTime=$auditTime)"
    }

}

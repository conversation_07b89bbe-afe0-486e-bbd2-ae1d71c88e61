package com.vsimtone.rirm2.backend.entity

import com.querydsl.core.annotations.Config
import javax.persistence.Column
import javax.persistence.Entity
import javax.persistence.EnumType
import javax.persistence.Enumerated
import javax.persistence.Index
import javax.persistence.Table
import java.util.*

@Table(
    name = "rirm2_itx_session",
    indexes = [
        Index(name = "rirm2_itx_session_i_sessionId", columnList = "sessionId", unique = true)
    ]
)
@Entity
@Config(defaultVariableName = "a")
class ITXSession : BaseEntity() {

    enum class Type { Linux, BOC, AIX }

    lateinit var sessionId: String;

    @Enumerated(EnumType.STRING)
    var type: Type? = null;

    @Column(length = 1024)
    var replayInfo: String? = null

    var beginTime: Date? = null;
    var endTime: Date? = null;
    var clientIP: String? = null;
    var serverIP: String? = null;
    var loginUser: String? = null;
    var realUser: String? = null;
    var commandCount = 0
}

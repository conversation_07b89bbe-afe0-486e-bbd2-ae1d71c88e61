package com.vsimtone.rirm2.backend.entity

import com.querydsl.core.annotations.Config
import javax.persistence.*
import org.springframework.format.annotation.DateTimeFormat
import java.util.Date

//实施方案
@Table(
    indexes = [
        Index(name = "i_cf_cmd_itemId", columnList = "changeFormItem.id", unique = false),
        Index(name = "i_cf_cmd_formId", columnList = "changeFormId", unique = false)
    ]
)
@Entity
@Config(defaultVariableName = "a")
class ChangeFormCommand : BaseEntity() {

    @ManyToOne
    var changeFormItem: ChangeFormItem? = null


    lateinit var changeFormId: String

    @Lob
    lateinit var command: String

    @DateTimeFormat(pattern = DEFAULT_DATE_PATTERN)
    var beginTime: Date? = null

    @DateTimeFormat(pattern = DEFAULT_DATE_PATTERN)
    var endTime: Date? = null

    @ManyToOne
    var fromImport: ExcelImport? = null

}
package com.vsimtone.rirm2.backend.entity

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.querydsl.core.annotations.Config
import javax.persistence.*

@Entity
@Table(
    indexes = [
        Index(name = "file_folder_i_path", columnList = "path", unique = true),
        Index(name = "file_folder_i_parent", columnList = "parent.id", unique = false)
    ]
)
@Config(defaultVariableName = "a")
class FileFolder : BaseEntity() {

    @Id
    @Column(name = "id")
    @JsonSerialize(using = BaseEntity.Companion.IDSerializer::class)
    override var id: Long? = null

    lateinit var name: String

    lateinit var path: String

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    var parent: FileFolder? = null

}
package com.vsimtone.rirm2.backend.entity

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import com.querydsl.core.annotations.Config
import javax.persistence.*

@Table(
    indexes = [
        Index(name = "i_cc_path", columnList = "path", unique = false),
        Index(name = "i_cc_parent", columnList = "parent.id", unique = false),
    ]
)
@Entity
@Config(defaultVariableName = "a")
class CommandCategory : BaseEntity() {

    @Id
    @Column(name = "id")
    @JsonSerialize(using = Companion.IDSerializer::class)
    override var id: Long? = null

    lateinit var name: String

    var remark: String? = null

    lateinit var path: String


    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnore
    var parent: CommandCategory? = null
}
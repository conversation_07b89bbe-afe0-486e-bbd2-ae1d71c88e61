package com.vsimtone.rirm2.backend.entity


import com.fasterxml.jackson.annotation.JsonIgnore
import com.querydsl.core.annotations.Config
import com.vsimtone.rirm2.backend.jpa_convert.JsonArrayConverter
import javax.persistence.*
import org.springframework.format.annotation.DateTimeFormat
import java.util.*

@Entity
@Table(
    indexes = [
        Index(name = "admin_user_i_username", columnList = "username")
    ]
)
@Config(defaultVariableName = "a")
class AdminUser : BaseEntity() {

    lateinit var nickname: String;

    lateinit var username: String;

    @JsonIgnore
    var password: String? = null;

    var loginFailCount: Int = 0;

    var enabled: Boolean = false;

    var locked: Boolean = false;

    @ManyToOne
    var org: Organization? = null;

    @ManyToOne
    var role: AdminRole? = null;

    @Lob
    @Convert(converter = JsonArrayConverter::class)
    var permissions: MutableList<String> = mutableListOf();

    @DateTimeFormat(pattern = DEFAULT_DATE_PATTERN)
    @Column
    var lastLoginTime: Date? = null

    var firstLogin: Boolean? = false;
}

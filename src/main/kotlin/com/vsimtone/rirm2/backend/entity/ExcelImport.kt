package com.vsimtone.rirm2.backend.entity

import com.querydsl.core.annotations.Config
import com.vsimtone.rirm2.backend.jpa_convert.JsonMapConverter
import javax.persistence.*

@Entity()
@Table(
    indexes = []
)
@Config(defaultVariableName = "a")
class ExcelImport : BaseEntity() {

    enum class Status { Pending, Success, Fail }

    @OneToOne
    lateinit var file: FileInfo

    @Enumerated(EnumType.STRING)
    var status = Status.Pending

    @Column(length = 4096)
    var errmsg: String? = null


    @Lob
    @Convert(converter = JsonMapConverter::class)
    var results: MutableMap<String, String> = mutableMapOf()

    var defineCls: String? = null

    var defineName: String? = null

    var importedRowCount: Int = 0

}
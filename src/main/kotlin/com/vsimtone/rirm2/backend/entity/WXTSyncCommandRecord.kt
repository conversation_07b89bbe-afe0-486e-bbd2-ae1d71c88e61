package com.vsimtone.rirm2.backend.entity

import com.querydsl.core.annotations.Config
import com.vsimtone.rirm2.backend.jpa_convert.JsonArrayConverter
import com.vsimtone.rirm2.backend.jpa_convert.JsonMapConverter
import javax.persistence.*
import java.util.*

@Table(
    indexes = [
        Index(name = "i_wxtsc_record_ctime_succ", columnList = "createdAt,success", unique = false),
    ]
)
@Entity
@Config(defaultVariableName = "a")
class WXTSyncCommandRecord : BaseEntity() {

    var success: Boolean = false

    var errmsg: String? = null

    //    同步的数据大小
    var apiDownBytes: Long = 0

    //    同步的数据条数
    var apiListDataCount: Long = 0

    //    接口调用时长
    var apiUseTime: Long = 0

    //    同步成功条数
    var apiSuccRequestCount: Long = 0

    //    同步失败条数
    var apiFailRequestCount: Long = 0

    //    同步开始时间
    var syncTimeBegin: Date? = null

    //    同步结束时间
    var syncTimeEnd: Date? = null

    //    忽略的会话数量
    var ignoreSessionCount: Long = 0

    //    新增的会话数量
    var addSessionCount: Long = 0

    //    新增的命令数量
    var addCommandCount: Long = 0

    @Convert(converter = JsonArrayConverter::class)
    @Column(length = SIZE_16MB)
    var cmdTooManySessions: MutableList<String>? = null

    override fun toString(): String {
        return "WXTSyncCommandRecord(success=$success, errmsg=$errmsg, time=${DEFAULT_DATE_FORMAT.format(syncTimeBegin)}/${DEFAULT_DATE_FORMAT.format(syncTimeEnd)}, apiInfo=$apiUseTime/$apiSuccRequestCount/$apiFailRequestCount, ignoreSessionCount=$ignoreSessionCount, addSessionCount=$addSessionCount, addCommandCount=$addCommandCount)"
    }
}

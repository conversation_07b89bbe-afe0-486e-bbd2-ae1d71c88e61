package com.vsimtone.rirm2.backend.entity

import com.querydsl.core.annotations.Config
import javax.persistence.*

@Table(
    indexes = [
        Index(name = "i_ar_alertRule", columnList = "alertRule.id", unique = false),
        Index(name = "i_ar_sess", columnList = "sess.id", unique = false),
        Index(name = "i_ar_cmdRule", columnList = "cmdRule.id", unique = false)
    ]
)
@Entity
@Config(defaultVariableName = "a")
class AlertRecord : BaseEntity() {

    enum class MailSendStatus {
        Waiting, Success, Fail
    }

    @ManyToOne
    lateinit var alertRule: AlertRule

    @ManyToOne
    lateinit var sess: ITXSession

    @ManyToOne
    lateinit var cmd: ITXCommand

    @ManyToOne
    var cmdRule: CommandRule? = null

    @ManyToOne
    var cmdCategory: CommandCategory? = null

    @Enumerated(EnumType.STRING)
    var mailSendStatus: MailSendStatus? = null

    var processed = false;

}
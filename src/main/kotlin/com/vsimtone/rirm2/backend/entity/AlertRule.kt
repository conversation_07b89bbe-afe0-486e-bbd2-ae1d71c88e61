package com.vsimtone.rirm2.backend.entity

import com.querydsl.core.annotations.Config
import javax.persistence.*

@Table(
    indexes = [
        Index(name = "i_alert_rule_commandRule", columnList = "commandRule.id", unique = false)
    ]
)
@Entity
@Config(defaultVariableName = "a")
class AlertRule : BaseEntity() {

    var enabled: Boolean = true

    @Lob
    var alertTitle: String? = null

    @Lob
    var alertBody: String? = null

    @ManyToOne
    var commandRule: CommandRule? = null

    @Enumerated(EnumType.STRING)
    var alertLevel: CommandRule.AlertLevel? = null

    var serverIP: String? = null

    @ManyToOne
    var commandCategory: CommandCategory? = null

    @Lob
    var mailReceivers: String? = null
    override fun toString(): String {
        return "AlertRule(id=$id, title=$alertTitle)"
    }

}
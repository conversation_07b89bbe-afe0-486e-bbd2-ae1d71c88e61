package com.vsimtone.rirm2.backend.entity

import com.querydsl.core.annotations.Config
import com.vsimtone.rirm2.backend.jpa_convert.JsonMapConverter
import javax.persistence.*
import java.util.*

//汇总单
@Table(
    indexes = [
        Index(name = "i_cf_stats_itemId", columnList = "changeFormItem.id", unique = false),
    ]
)
@Entity
@Config(defaultVariableName = "a")
class ChangeFormItemStats : BaseEntity() {

    @ManyToOne
    var changeFormItem: ChangeFormItem? = null

    @Column
    var statsKey: String? = null

    var statsVal: Long? = null
    
    override fun toString(): String {
        return "ChangeFormItemStats(changeFormItem=${changeFormItem?.id}, statsKey=$statsKey, statsVal=$statsVal)"
    }

}

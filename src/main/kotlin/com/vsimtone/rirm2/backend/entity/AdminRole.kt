package com.vsimtone.rirm2.backend.entity

import com.querydsl.core.annotations.Config
import com.vsimtone.rirm2.backend.jpa_convert.JsonArrayConverter
import javax.persistence.*

@Entity
@Table()
@Config(defaultVariableName = "a")
class AdminRole: BaseEntity()  {

    lateinit var roleName: String;

    @Lob
    @Convert(converter = JsonArrayConverter::class)
    var permissions: MutableList<String> = mutableListOf();
}
package com.vsimtone.rirm2.backend.entity

import com.querydsl.core.annotations.Config
import javax.persistence.Column
import javax.persistence.Entity
import javax.persistence.Index
import javax.persistence.Lob
import javax.persistence.Table
import java.util.*

@Table(
    indexes = [
        Index(name = "i_cfs_record_ctime_succ", columnList = "createdAt,success", unique = false),
    ]
)
@Entity
@Config(defaultVariableName = "a")
class ChangeFormSyncRecord : BaseEntity() {

    var success: Boolean = false

    var errmsg: String? = null

    //    同步的数据大小
    var apiDownBytes: Long = 0

    //    同步的数据条数
    var apiListDataCount: Long = 0

    //    接口调用时长
    var apiUseTime: Long = 0

    //    同步成功条数
    var apiSuccRequestCount: Long = 0

    //    同步失败条数
    var apiFailRequestCount: Long = 0

    //    同步开始时间
    var syncTimeBegin: Date? = null

    //    同步结束时间
    var syncTimeEnd: Date? = null


    //    变更单添加数量
    var addChangeFormCount: Long = 0

    //    变更单修改数量
    var modifyChangeFormCount: Long = 0

    //    变更单忽略数量
    var ignoreChangeFormCount: Long = 0

    //    控制室日志添加数量
    var addChangeFormHostCount: Long = 0

    //    控制室日志修改数量
    var modifyChangeFormHostCount: Long = 0

    //    控制室日志忽略数量
    var ignoreChangeFormHostCount: Long = 0

    //    变更单命令添加数量
    var addChangeFormCommandCount: Long = 0

    //    变更单命令修改数量
    var modifyChangeFormCommandCount: Long = 0

    //    变更单命令忽略数量
    var ignoreChangeFormCommandCount: Long = 0

    override fun toString(): String {
        return "ChangeFormSyncRecord(success=$success, errmsg=$errmsg, time=${DEFAULT_DATE_FORMAT.format(syncTimeBegin)}/${DEFAULT_DATE_FORMAT.format(syncTimeEnd)}, apiInfo=$apiUseTime/$apiSuccRequestCount/$apiFailRequestCount, addChangeFormCount=$addChangeFormCount, modifyChangeFormCount=$modifyChangeFormCount, ignoreChangeFormCount=$ignoreChangeFormCount, addChangeFormHostCount=$addChangeFormHostCount, modifyChangeFormHostCount=$modifyChangeFormHostCount, ignoreChangeFormHostCount=$ignoreChangeFormHostCount, addChangeFormCommandCount=$addChangeFormCommandCount, modifyChangeFormCommandCount=$modifyChangeFormCommandCount, ignoreChangeFormCommandCount=$ignoreChangeFormCommandCount)"
    }

}

package com.vsimtone.rirm2.backend.entity


import com.querydsl.core.annotations.Config
import javax.persistence.*

@Table(
    indexes = [Index(name = "appconfig_i_key", columnList = "c_key")]
)
@Entity
@Config(defaultVariableName = "a")
class APPConfig : BaseEntity() {

    @Column(name = "c_key")
    lateinit var key: String

    @Column(name = "c_val", length = 8192)
    var `val`: String? = null

    @Enumerated(EnumType.STRING)
    var type: Type? = null

    enum class Type {
        String, Integer, Float, Boolean, JsonMap, JsonArray, Long
    }
}

package com.vsimtone.rirm2.backend.services

import com.querydsl.jpa.impl.JPAQueryFactory
import com.vsimtone.rirm2.backend.schedule.ScheduledItem
import com.vsimtone.rirm2.backend.schedule.ScheduledService
import com.vsimtone.rirm2.backend.service.BaseCrudService
import com.vsimtone.rirm2.backend.service.RedisService
import com.vsimtone.rirm2.backend.service.SysService
import com.vsimtone.rirm2.backend.utils.JSONUtils
import com.vsimtone.rirm2.backend.utils.Log
import com.vsimtone.rirm2.backend.utils.RedisUtils
import javax.annotation.PostConstruct
import javax.persistence.EntityManager
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.cache.CacheManager
import org.springframework.context.ApplicationContext
import org.springframework.context.ApplicationContextAware
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.stereotype.Service
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.TransactionDefinition
import org.springframework.transaction.TransactionStatus
import org.springframework.transaction.support.*
import java.util.concurrent.Executor


abstract class BaseService {

    protected open var logger = Log[this.javaClass];

    @Autowired
    protected open lateinit var cacheManager: CacheManager

    @Autowired
    protected open lateinit var entityManager: EntityManager

    @Autowired
    protected open lateinit var platformTransactionManager: PlatformTransactionManager

    @Autowired
    protected open lateinit var scheduledService: ScheduledService

    @Qualifier("taskExecutor")
    @Autowired
    protected open lateinit var taskExecutor: Executor

    open var ready = false

    open var readyInvokeOrder = READY_INVOKE_NORMAL

    @PostConstruct
    fun onBeanInit() {
        if (AllBaseServiceClass.contains(this.javaClass))
            throw RuntimeException("Service repeat ${this.javaClass}")
        AllBaseServiceClass.add(this.javaClass)
        runInTran {
            onInit()
        }
    }

    companion object {
        const val READY_INVOKE_HIGH = 1;
        const val READY_INVOKE_NORMAL = 2;
        const val READY_INVOKE_LOW = 3;
        var shutdowning = false
        lateinit var appCtx: ApplicationContext
        lateinit var queryFactory: JPAQueryFactory
        var AllBaseServiceClass = mutableListOf<Class<in BaseService>>()
        var AppReady = false

        @Service
        @Order(Ordered.LOWEST_PRECEDENCE)
        public class BaseReadyInvokeService : ApplicationContextAware {

            var logger = Log.get(this.javaClass)

            @Autowired
            lateinit var entityManager: EntityManager

            @Autowired
            lateinit var sysService: SysService

            @Autowired
            lateinit var redisService: RedisService

            override fun setApplicationContext(applicationContext: ApplicationContext) {
                appCtx = applicationContext
                queryFactory = JPAQueryFactory(entityManager)
                AllBaseServiceClass.forEach { _it ->
                    val it = appCtx.getBean(_it) as BaseService
                    if (it is BaseCrudService<*, *>) {
                        it.initCrud(appCtx)
                    }
                }
                doAppReady()
            }

            fun doAppReady() {
                sysService.runInTran { tx ->
                    val locked = redisService.runInLock("app.ready", 60L) {
                        AllBaseServiceClass.map { appCtx.getBean(it) as BaseService }.sortedBy { it.readyInvokeOrder }.forEachIndexed { idx, it ->
                            it.onReady()
                            logger.info("Service ready ${idx + 1}/${AllBaseServiceClass.size}: ${it.javaClass.simpleName} order ${it.readyInvokeOrder}")
                        }
                    }
                    if (!locked) {
                        throw RuntimeException("App ready lock fail.")
                    }
                }
                AllBaseServiceClass.forEach { _it ->
                    val it = appCtx.getBean(_it) as BaseService
                    it.ready = true
                }
                AppReady = true;
                logger.info("System info ${sysService.thisNodeInfo}")
                logger.info("All service ready")
            }
        }
    }

    open fun <T> getByCache(name: String, key: String, cls: Class<T>, doGet: () -> T?): T? {
        val c = cacheManager.getCache("cache:$name") ?: return doGet()
        val cv = c.get(key)
        if (cv == null) {
            val value = doGet()
            runInAfterTranCommitted {
                if (value != null) {
                    val sv = RedisUtils.autoCodec(cls).valueEncoder.encode(value).toString(Charsets.UTF_8)
                    c.put(key, sv);
                } else {
                    c.put(key, "");
                }
//                logger.info("Get by cache miss, put to cache ${name} ${key} ${value}")
            }
            return value;
        }
        val sv = cv.get() as String? ?: return null
        if (sv.isEmpty()) return null
        return JSONUtils.toObject(sv, cls)
    }

    open fun putToCache(name: String, key: String, value: String): Boolean {
        val c = cacheManager.getCache("cache:$name") ?: return false
        runInAfterTranCommitted {
            c.put(key, value)
//            logger.info("Put to cache ${name} ${key}")
        }
        return true
    }

    open fun removeCache(name: String, vararg keys: String) {
        val c = cacheManager.getCache("cache:$name") ?: return
        runInAfterTranCommitted {
            if (keys.isEmpty())
                c.clear()
            else
                keys.forEach {
                    c.evict(it)
                }
//        logger.info("Remove cache ${name} ${keys.joinToString(",")}")
        }
    }

    @Suppress("UNCHECKED_CAST")
    open fun <T> runInTran(
        propagation: Int = TransactionDefinition.PROPAGATION_REQUIRES_NEW,
        isolation: Int = TransactionDefinition.ISOLATION_DEFAULT,
        readOnly: Boolean = false,
        cb: (status: TransactionStatus) -> T
    ): T {

        val tranDefine = DefaultTransactionDefinition()
        tranDefine.propagationBehavior = propagation
        tranDefine.isolationLevel = isolation
        tranDefine.isReadOnly = readOnly

        val transactionTemplate = TransactionTemplate(
            platformTransactionManager,
            tranDefine
        )
        var r: T? = null;
        transactionTemplate.execute(
            object : TransactionCallbackWithoutResult() {
                override fun doInTransactionWithoutResult(status: TransactionStatus) {
                    if (status.isNewTransaction)
                        r = cb(status)
                    else
                        throw RuntimeException("Transaction is not new.")
                }
            }
        );
        return r as T
    }

    open fun <T> runInReadOnlyTran(cb: (status: TransactionStatus) -> T): T {
        return runInTran(TransactionDefinition.PROPAGATION_REQUIRES_NEW, TransactionDefinition.ISOLATION_DEFAULT, true, cb)
    }

    open fun <T> runInReadOnlyAndUnlockTran(cb: (status: TransactionStatus) -> T): T {
        return runInTran(TransactionDefinition.PROPAGATION_REQUIRES_NEW, TransactionDefinition.ISOLATION_READ_UNCOMMITTED, true, cb)
    }

    open fun runInAfterTranCommitted(cb: () -> Unit) {
        if (TransactionSynchronizationManager.isSynchronizationActive())
            TransactionSynchronizationManager.registerSynchronization(object : TransactionSynchronization {
                override fun afterCompletion(status: Int) {
                    if (TransactionSynchronization.STATUS_COMMITTED == status)
                        taskExecutor.execute { cb() }
                }
            })
        else cb();
    }

    open fun schedule(key: String, delay: Long, cb: () -> Unit): ScheduledItem {
        val s = object : ScheduledItem() {
            override fun scheduled() {
                cb()
            }
        }
        s.setId(key)
        s.fixDelay = delay
        scheduledService.addItem(s);
        return s;
    }

    open fun onInit() {

    }

    open fun onReady() {
    }
}
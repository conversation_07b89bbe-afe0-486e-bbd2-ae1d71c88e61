package com.vsimtone.rirm2.backend.service

import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.vsimtone.rirm2.backend.utils.JSONUtils
import javax.servlet.http.HttpSession
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.redis.core.StringRedisTemplate
import org.springframework.stereotype.Service
import java.io.Serializable
import java.util.*

@Service
open class CaptchaService {


    @Autowired
    private lateinit var stringRedisTemplate: StringRedisTemplate


    private val logger = LoggerFactory.getLogger("captchaService")

    @JsonTypeInfo(use = JsonTypeInfo.Id.CLASS)
    open class CaptchaData() : Serializable {
        var code: String = ""
        var time: Long = System.currentTimeMillis()
        var type: Type = Type.Image
        var to: String = ""
        var reason: String = ""
        var timeout = 10 * 1000 * 60L
        override fun equals(other: Any?): Boolean {
            if (other !is CaptchaData) return false;
            if (!code.equals(other.code, true)) return false;
            if (other.time - time >= timeout) return false;
            if (reason != other.reason) return false;
            if (to != other.to) return false;
            if (type != other.type) return false;
            return true;
        }

        fun sessionKey(): String {
            return SESSION_ATTR_NAME + type.toString().lowercase(Locale.getDefault());
        }

        fun writeToSession(session: HttpSession) {
            session.setAttribute(sessionKey(), JSONUtils.toString(this));
        }

        fun equalsFromSession(session: HttpSession): Boolean {
            var data = session.getAttribute(sessionKey()) as String?
            if (data == null) return false;
            return JSONUtils.toObject(data, CaptchaData::class.java) == this;
        }
    }

    companion object {
        enum class Type { Image, Mail, Phone }

        val SMS_SEND_CACHE_TIMEOUT = 1000 * 60L
        val CAPTCHA_STRING_CHARS = "qwertyupasdfghjkzxcvbnmQWERTYUPASDFGHJKLZXCVBNM123456789"
        val CAPTCHA_NUMBER_CHARS = "0123456789"
        val SESSION_ATTR_NAME = "captcha:"

        val MailTemplate = mapOf<String, Map<String, String>>(
            "default" to mapOf(
                "zh_CN" to "您的验证码：#code#\n该验证码10分钟内有效。请勿将验证码透露给他人。\nSincerely,\nKssbit\nhttps://www.kssbit.com",
                "en" to "Your Kssbit verification code is: #code#, this code will expire in 20 minutes."
            ),
            "register" to mapOf(
                "zh_CN" to "您好，\n感谢您注册Kssbit。\n您的验证码是：#code#\n出于安全原因，该验证码将于10分钟后失效。请勿将验证码透露给他人。\nSincerely,\nKssbit团队\nhttps://www.kssbit.com",
                "en" to "Hello,\n" +
                        "\n" +
                        "Your Kssbit verification code is: #code#\n" +
                        "For security reasons, this code will expire in 10 minutes.\n" +
                        "If you did not request this action, freeze your account immediately and contact support.\n" +
                        " \n" +
                        "Sincerely,\n" +
                        "Kssbit Team\n" +
                        "https://www.kssbit.com\n"
            ),
            "reset" to mapOf(
                "zh_CN" to "您好，\n您的Kssbit账号正在请求重置密码。\n您的验证码: #code#\n出于安全原因，该验证码将于10分钟后失效。请勿将验证码透露给他人。\n如果您没有进行该操作，请立即修改登录密码。\n\nKssbit团队\nhttps://www.kssbit.com",
                "en" to "Hello,\n" +
                        "\n" +
                        "As you requested, your Kssbit password has now been reset. Your verification code is: #code#. For security reasons, this code will expire in 10 minutes.\n" +
                        "If you did not request this action, please change your password immediately or freeze your account.\n" +
                        " \n" +
                        "Sincerely,\n" +
                        "Kssbit Team\n" +
                        "https://www.kssbit.com\n"
            )
        )
    }

    fun getCode(isNum: Boolean, length: Int): String {
        var code = ""
        var rnd = Random(System.currentTimeMillis())
        for (i in 1..length) {
            if (isNum)
                code += CAPTCHA_NUMBER_CHARS[(rnd.nextInt(CAPTCHA_NUMBER_CHARS.length)).toInt()]
            else
                code += CAPTCHA_STRING_CHARS[(rnd.nextInt(CAPTCHA_STRING_CHARS.length)).toInt()]
        }
        return code
    }

    fun generateImage(session: HttpSession): String {
        var code = getCode(false, 4)
        var data = CaptchaData()
        data.code = code
        data.writeToSession(session)
        return code
    }

    fun validateImage(session: HttpSession, code: String): Boolean {
        var data = CaptchaData()
        data.code = code
        return data.equalsFromSession(session);
    }


    fun validateMail(session: HttpSession, to: String, code: String): Boolean {
        var data = CaptchaData()
        data.code = code
        data.type = Type.Mail
        data.to = to
        return data.equalsFromSession(session);
    }


    fun validatePhone(session: HttpSession, country: String, to: String, code: String): Boolean {
        var data = CaptchaData()
        data.code = code.trim()
        data.type = Type.Phone
        data.to = country + to
        data.timeout = SMS_SEND_CACHE_TIMEOUT
        return data.equalsFromSession(session);
    }


}
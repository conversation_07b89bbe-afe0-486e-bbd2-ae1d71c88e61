package com.vsimtone.rirm2.backend.service

import com.querydsl.core.BooleanBuilder
import com.querydsl.core.types.dsl.BooleanExpression
import com.vsimtone.rirm2.backend.entity.*
import com.vsimtone.rirm2.backend.repository.ChangeFormItemRepository
import com.vsimtone.rirm2.backend.services.BaseService
import com.vsimtone.rirm2.backend.services.BaseService.Companion.shutdowning
import com.vsimtone.rirm2.backend.utils.JSONUtils
import com.vsimtone.rirm2.backend.utils.TimeStats
import javax.persistence.EntityManager
import org.apache.commons.lang3.StringUtils
import org.redisson.api.RQueue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import java.util.*
import java.util.concurrent.LinkedBlockingQueue
import kotlin.concurrent.thread

@Service
class ChangeFormItemService : BaseCrudService<ChangeFormItem, ChangeFormItemRepository>(QChangeFormItem.a) {

    lateinit var matchQueue: Queue<String>

    @Autowired
    lateinit var excelService: ExcelImportService

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    @Lazy
    lateinit var changeFormHostService: ChangeFormHostService

    @Autowired
    lateinit var changeFormCommandService: ChangeFormCommandService

    @Autowired
    lateinit var itxCommandService: ITXCommandService

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var sysService: SysService

    @Autowired
    lateinit var perfMonService: PerfMonService

    @Autowired
    lateinit var changeFormItemStatsService: ChangeFormItemStatsService

    lateinit var needUpdateStatsQueue: Queue<Long>

    @Autowired
    lateinit var changeFormCommandMappingService: ChangeFormCommandMappingService

    companion object {
        val CNF_NAME_AUTO_CONFIRM_LEVELS = "core.alert.auto_confirm_levels";
        const val CNF_NAME_WHITELIST = "core.whitelist";
        const val CNF_KEY_TYPE_CHECK_CHANGE_TYPE_NEED_CONFIRMS = "core.changeform.changetype.need_confirms";
        const val CNF_KEY_TYPE_CHECK_CHANGE_TYPE2_NEED_CONFIRMS = "core.changeform.changetype2.need_confirms";
        const val CNF_KEY_TYPE_CHECK_CUSTOM_NORMAL_SYMBOLS = "core.changeform.custom_normal_customs";
    }

    override fun onReady() {
        appConfigService.init(CNF_NAME_WHITELIST, "[]", APPConfig.Type.JsonArray);
        appConfigService.init(
            CNF_NAME_AUTO_CONFIRM_LEVELS, JSONUtils.toString(listOf(CommandRule.AlertLevel.Low)), APPConfig.Type.JsonArray
        );
        appConfigService.init(
            CNF_KEY_TYPE_CHECK_CHANGE_TYPE_NEED_CONFIRMS, JSONUtils.toString(listOf("例行变更", "普通变更", "重要变更")), APPConfig.Type.JsonArray
        )
        appConfigService.init(
            CNF_KEY_TYPE_CHECK_CHANGE_TYPE2_NEED_CONFIRMS, JSONUtils.toString(listOf("维护类", "日常类")), APPConfig.Type.JsonArray
        )
        appConfigService.init(
            CNF_KEY_TYPE_CHECK_CUSTOM_NORMAL_SYMBOLS, JSONUtils.toString(listOf<String>()), APPConfig.Type.JsonArray
        )
        matchQueue = LinkedBlockingQueue(Int.MAX_VALUE);
        needUpdateStatsQueue = LinkedBlockingQueue(Int.MAX_VALUE);

        val define = excelService.define(
            "变更单-列表", 0, ChangeFormItem::class, listOf(
                ExcelImportService.Companion.ColumnDefine("变更名称", ChangeFormItem::name, true),
                ExcelImportService.Companion.ColumnDefine("变更类型1", ChangeFormItem::changeType, true),
                ExcelImportService.Companion.ColumnDefine("变更类型2", ChangeFormItem::changeType2, false),
                ExcelImportService.Companion.ColumnDefine("变更编号", ChangeFormItem::formId, true),
                ExcelImportService.Companion.ColumnDefine("受理团队", ChangeFormItem::team, true),
                ExcelImportService.Companion.ColumnDefine("计划开始日期", ChangeFormItem::planBeginTime, true),
                ExcelImportService.Companion.ColumnDefine("计划结束日期", ChangeFormItem::planEndTime, true),
                ExcelImportService.Companion.ColumnDefine("状态说明", ChangeFormItem::statusText, false),
                ExcelImportService.Companion.ColumnDefine("变更原因", ChangeFormItem::reasonText, false),
                ExcelImportService.Companion.ColumnDefine("说明", ChangeFormItem::descText, false),
            )
        )
        define.onData = { ctx: ExcelImportService.Companion.ImportContext<ChangeFormItem>, data: ChangeFormItem?, row: Int ->
            this.onImportData(ctx, data, row)
        }
        define.countImported = {
            repo.count(QChangeFormItem.a.fromImport.eq(it))
        }
        schedule("add_lost_to_match", 60 * 1000) {
            excelService.importLockRun("changeFormItemService@add_lost_to_match", 10) {
                if (matchQueue.isNotEmpty()) return@importLockRun
                repo.findAll(QChangeFormItem.a.matchTime.isNull).forEach {
                    addToMatchQueue(it.formId)
                }
            }
        }
        schedule("add_lost_to_stats", 60 * 1000) {
            if (needUpdateStatsQueue.isNotEmpty()) return@schedule
            repo.findAll(QChangeFormItem.a.matchTime.isNotNull.and(QChangeFormItem.a.statsTime.isNull)).forEach {
                lazyUpdateStats(it.id!!)
            }
        }
        thread {
            doMatch()
        }
        thread {
            this.logger.info("update stats thread start.")
            while (!shutdowning) {
                try {
                    val s = mutableSetOf<Long>()
                    while (true) {
                        s.add(needUpdateStatsQueue.poll() ?: break)
                        if (s.size >= 1000) break
                    }
                    s.forEachIndexed { idx, id ->
                        runInTran {
                            val startedAt = System.currentTimeMillis()
                            val data = findById(id)
                            updateStats(data);
                            this.repo.save(data);
                            this.logger.info("lazy update stats ${idx}/${s.size}/${needUpdateStatsQueue.size}, id=${id}, formId=${data.formId}, use=${System.currentTimeMillis() - startedAt}ms")
                        }
                    };
                } catch (e: Exception) {
                    if (shutdowning) return@thread;
                    if (e is InterruptedException) return@thread;
                    logger.error("update stats error", e)
                }
                Thread.sleep(1000 * 30);
            }
        }
    }

    fun lazyUpdateStats(item: Long) {
        if (needUpdateStatsQueue.contains(item)) {
            return
        }
        needUpdateStatsQueue.add(item)
    }

    fun getStats(item: ChangeFormItem, keys: List<String>): Map<String, Long> {
        val stats = mutableMapOf<String, Long>()
        for (key in keys) {
            stats[key] = 0;
        }
        return stats;
    }

    fun saveStats(item: ChangeFormItem, statsKey: String, statsVal: Long) {
        if (statsVal <= 0) return;
        val s = ChangeFormItemStats()
        s.changeFormItem = item;
        s.statsKey = statsKey;
        s.statsVal = statsVal;
        this.changeFormItemStatsService.save(s)
    }

    fun getStatsKey(l: CommandRule.AlertLevel?, m: ITXCommand.Companion.MatchResult?, a: ITXCommand.Companion.AuditResult?, p: ITXCommand.Companion.ProcessResult?): String {
        val keys = mutableListOf<String>()
        if (l != null) keys.add("L_${l}")
        if (m != null) keys.add("M_${m}")
        if (a != null) keys.add("A_${a}")
        if (p != null) keys.add("P_${p}")
        return keys.joinToString("&")
    }

    fun updateStats(item: ChangeFormItem) {
        item.hostCount = changeFormHostService.repo.countAllByChangeFormItem(item) ?: 0L;
        item.cmdCount = changeFormCommandService.repo.countAllByChangeFormItem(item) ?: 0L;
        item.itxCmdTotalCount = itxCommandService.repo.countAllByChangeFormItem(item) ?: 0L;
        this.changeFormItemStatsService.batchDelete(QChangeFormItemStats.a.changeFormItem.eq(item))
        fun customCount(vararg queriesList: List<Any>) {
            val flatQueries = queriesList.fold(listOf(listOf<Any>())) { acc, list ->
                acc.flatMap { currentCombination ->
                    list.map { element ->
                        currentCombination + element
                    }
                }
            }
            flatQueries.forEach { queries ->
                var q = BooleanBuilder()
                q = q.and(QITXCommand.a.changeFormItem.id.eq(item.id!!))
                val keys = mutableListOf<String>()
                queries.forEach { enumVal ->
                    if (enumVal is CommandRule.AlertLevel) {
                        q = q.and(QITXCommand.a.rule.alertLevel.eq(enumVal))
                        keys.add("L_${enumVal.name}")
                    } else if (enumVal is ITXCommand.Companion.MatchResult) {
                        q = q.and(QITXCommand.a.matchResult.eq(enumVal))
                        keys.add("M_${enumVal.name}")
                    } else if (enumVal is ITXCommand.Companion.AuditResult) {
                        q = q.and(QITXCommand.a.auditResult.eq(enumVal))
                        keys.add("A_${enumVal.name}")
                    } else if (enumVal is ITXCommand.Companion.ProcessResult) {
                        q = q.and(QITXCommand.a.processResult.eq(enumVal))
                        keys.add("P_${enumVal.name}")
                    } else {
                        throw IllegalArgumentException("unknown value type ${enumVal.javaClass}")
                    }
                }
                saveStats(item, keys.joinToString("&"), this.itxCommandService.count(q));
            }
        }

        fun <T> getAllCombinations(vararg elements: T): List<List<T>> {
            val n = elements.size
            return (1 until (1 shl n)).map { mask ->
                elements.filterIndexed { index, _ ->
                    (mask and (1 shl index)) != 0
                }
            }
        }
        getAllCombinations(CommandRule.AlertLevel.entries, ITXCommand.Companion.MatchResult.entries, ITXCommand.Companion.AuditResult.entries, ITXCommand.Companion.ProcessResult.entries).forEach {
            customCount(*it.toTypedArray())
        }
        item.statsTime = Date()
    }

    fun onImportData(
        ctx: ExcelImportService.Companion.ImportContext<ChangeFormItem>, _data: ChangeFormItem?, row: Int
    ): String? {
        val form = _data!!
        if (StringUtils.isEmpty(form.formId)) return "变更编号不能为空"
//        根据变更编号来判断数据重复
        val data = repo.findByFormId(form.formId) ?: ChangeFormItem();
        data.formId = form.formId
        data.name = form.name
        data.descText = form.descText
        data.team = form.team
        data.planBeginTime = form.planBeginTime
        data.planEndTime = form.planEndTime
        data.reasonText = form.reasonText
        data.statusText = form.statusText
        data.changeType = form.changeType
        data.changeType2 = form.changeType2
        data.fromImport = ctx.import
        data.matchTime = null
        repo.save(data)
        repo.flush();
        addToMatchQueue(data.formId)
        return null;
    }

    fun addToMatchQueue(vararg items: String) {
        runInAfterTranCommitted {
            items.forEach { item ->
                if (matchQueue.contains(item)) return@forEach;
                matchQueue.add(item);
            }
        }
    }

    fun cmdClean(cmd: String): String {
        return cmd.replace("[\\s\\t\\r]+".toRegex(), " ").trim().lowercase();
    }

    private fun doMatch(formId: String) {
        var timeStats = TimeStats()
        val item = repo.findByFormId(formId) ?: return
        val hosts = changeFormHostService.repo.findAllByChangeFormId(formId)
        val cmds = changeFormCommandService.repo.findAllByChangeFormId(formId)
        val cmdMappings = changeFormCommandMappingService.findAll(QChangeFormCommandMapping.a.enabled.isTrue).toList()
        timeStats.point("loadData")
        hosts.forEach {
            it.changeFormItem = item
            changeFormHostService.repo.save(it)
        }
        val cmdTexts = StringBuffer()
        cmds.forEach {
            it.changeFormItem = item
            changeFormCommandService.repo.save(it)
            var cmd = it.command
            cmdMappings.forEach { m ->
                cmd = cmd.replace(m.source, " ${m.target} ")
            }
            cmd = cmdClean(cmd);
            cmdMappings.forEach { m ->
                cmd = cmd.replace(cmdClean(m.source), " ${cmdClean(m.target)} ")
            }
            cmd = cmdClean(cmd);
            cmdTexts.append(cmd + "\n");
        }
        timeStats.point("saveData")
        var autoConfirmCount = 0;
        var auditCount = 0;
        val autoConfirmAlertLevels = appConfigService.getList(CNF_NAME_AUTO_CONFIRM_LEVELS)!!.map { CommandRule.AlertLevel.valueOf(it as String) }
        var repeatHostQuery = mutableListOf<String>()
        logger.info("match start: ${item}")
        logger.info("    hosts=${hosts.size}, cmds=${cmds.size}, cmdMappings=${cmdMappings.size}")
        hosts.forEachIndexed { idx, host ->
            if (host.beginTime == null || host.endTime == null) {
                logger.warn("    host ${host} time is null.")
                return@forEachIndexed;
            }
            if (host.idOpsName.isNullOrBlank()) {
                logger.warn("    host ${host} idsOpsName is null.")
                return@forEachIndexed;
            }
            var beginTime = host.beginTime!!
            var endTime = host.endTime!!
            val allUserIds = host.idOpsName!!.split("|").toSet().toList().sorted()
            var queryId = "${beginTime.time}:${endTime.time}:${allUserIds.joinToString("|")}"
            if (StringUtils.isNotEmpty(host.serverIP))
                queryId += ":${host.serverIP}"
            var hostLog = "host=${idx + 1}/${hosts.size},${host.id},${host.idOpsNumber}, users=${allUserIds.joinToString("|")}, start=${BaseEntity.DEFAULT_DATE_FORMAT.format(beginTime)}, end=${BaseEntity.DEFAULT_DATE_FORMAT.format(endTime)}"
            if (repeatHostQuery.contains(queryId)) {
                logger.info("    ${hostLog}, repeat. skip.")
                return@forEachIndexed;
            }
            repeatHostQuery.add(queryId)
            logger.info("    ${hostLog}")

            val queries = BooleanBuilder()
            val a = QITXCommand.a;
            queries.and(a.matchResult.isNull)
            queries.and(a.execTime.goe(beginTime))
            queries.and(a.execTime.loe(endTime))
            if (StringUtils.isNotEmpty(host.serverIP))
                queries.and(a.session.serverIP.eq(host.serverIP))
            if (allUserIds.size == 1)
                queries.and(a.loginUser.eq(allUserIds.first()))
            else
                queries.and(a.loginUser.`in`(allUserIds))
            val itxCommands = itxCommandService.repo.findAll(queries).toList()
            logger.info("        commands=${itxCommands.size}")
            itxCommands.forEach {
                val itxCommand = it
                itxCommand.changeFormItem = item
                itxCommand.changeFormHost = host
                var matchResult: ITXCommand.Companion.MatchResult? = itxCommand.matchResult
                var auditResult: ITXCommand.Companion.AuditResult? = itxCommand.auditResult
                var processResult: ITXCommand.Companion.ProcessResult? = itxCommand.processResult
                var cmdMatched = true;
                if (itxCommand.cmd != null && itxCommand.cmd!!.isNotEmpty()) {
                    cmdMatched = cmdTexts.indexOf(cmdClean(itxCommand.cmd!!)) != -1
                }
                if (cmdMatched) {
                    matchResult = ITXCommand.Companion.MatchResult.Matched
                    if (auditResult == null) auditResult = ITXCommand.Companion.AuditResult.Pass
                    if (processResult == null) processResult = ITXCommand.Companion.ProcessResult.AutoConfirmed
                } else {
                    matchResult = ITXCommand.Companion.MatchResult.Unmatched
                    if (auditResult == null) auditResult = ITXCommand.Companion.AuditResult.Pending
                    if (processResult == null) processResult = ITXCommand.Companion.ProcessResult.UnProcess
                }
                if (itxCommand.rule != null && itxCommand.rule!!.alertLevel in autoConfirmAlertLevels) {
                    auditResult = ITXCommand.Companion.AuditResult.Pass;
                    processResult = ITXCommand.Companion.ProcessResult.AutoConfirmed;
                    autoConfirmCount++;
                }
                itxCommand.matchResult = matchResult
                itxCommand.auditResult = auditResult
                itxCommand.processResult = processResult
                itxCommandService.repo.save(itxCommand)
                logger.debug("        command audit ${itxCommand}")
                auditCount++;
            }
        }
        timeStats.point("match")

        item.hostCount = hosts.size.toLong();
        item.cmdCount = cmds.size.toLong();
        item.itxCmdTotalCount = itxCommandService.repo.countAllByChangeFormItem(item) ?: 0L;

        logger.info("    itxCmdTotalCount=${item.itxCmdTotalCount}, auditCount=${auditCount}, autoConfirmCount=${autoConfirmCount}");

        lazyUpdateStats(item.id!!);
        item.matchTime = Date()
        item.sysNames = hosts.map { it.sysName ?: "" }.filter { it.isNotEmpty() }.toSet().joinToString("|")
        if (item.sysNames!!.length >= 128) item.sysNames = item.sysNames!!.substring(0, 128) + "..."
        item.sponsorUsers = hosts.map { it.sponsorUser ?: "" }.filter { it.isNotEmpty() }.toSet().joinToString("|")
        if (item.sponsorUsers!!.length >= 128) item.sponsorUsers = item.sponsorUsers!!.substring(0, 128) + "..."
        if (item.typeCheck == null || item.typeCheck == ChangeFormItem.Companion.TypeCheck.UnConfirmed) {
            while (true) {
                item.typeCheck = ChangeFormItem.Companion.TypeCheck.Normal;
                if (item.itxCmdTotalCount != 0L) {
                    logger.info("    type check normal. has command.")
                    break;
                }
                val list1 = appConfigService.getList(CNF_KEY_TYPE_CHECK_CHANGE_TYPE_NEED_CONFIRMS) as List<String>?;
                if (list1 != null && list1.indexOf(item.changeType) == -1) {
                    logger.info("    type check normal. change type mismatch.")
                    break;
                }
                val list2 = appConfigService.getList(CNF_KEY_TYPE_CHECK_CHANGE_TYPE2_NEED_CONFIRMS) as List<String>?;
                if (StringUtils.isNotEmpty(item.changeType2) && list2 != null && list2.indexOf(item.changeType2) == -1) {
                    logger.info("    type check normal. change type2 mismatch.")
                    break;
                }
                val list3 = appConfigService.getList(CNF_KEY_TYPE_CHECK_CUSTOM_NORMAL_SYMBOLS) as List<String>?;
                if (list3 != null) {
                    if (list3.find { item.name.indexOf(it) != -1 } != null) {
                        logger.info("    type check normal. custom name match.")
                        break
                    }
                    if (list3.find { cmdTexts.indexOf(it) != -1 } != null) {
                        logger.info("    type check normal. custom cmd match.")
                        break
                    }
                }
                item.typeCheck = ChangeFormItem.Companion.TypeCheck.UnConfirmed;
                logger.info("    type check unconfirmed.")
                break;
            }
        }
        repo.save(item)
        timeStats.point("done")
        logger.info("match done: ${item}. times=[${timeStats}]");
    }

    private fun doMatch() {
        var lockedCount = 0;
        while (!shutdowning) {
            try {
                val id = matchQueue.poll();
                if (id == null) {
                    lockedCount = 0;
                    Thread.sleep(1000 * 60);
                    continue;
                }
                val locked = excelService.importLockRun("changeFormItemService@doMatch", 60) {
                    try {
                        runInTran {
                            doMatch(id);
                        }
                    } catch (e: Exception) {
                        logger.info("Match ${id} error", e);
                    }
                }
                if (!locked) {
                    this.logger.info("match delay. import lock fail.")
                    addToMatchQueue(id)
                } else {
                    lockedCount++;
                    if (lockedCount >= 100) {
                        lockedCount = 0;
                        Thread.sleep(1000 * 3); // 等待3秒释放锁
                    }
                }
            } catch (e: Exception) {
                if (shutdowning) return;
                if (e is InterruptedException) return;
                logger.info("Match error", e);
                Thread.sleep(1000);
            }
        }
    }
}

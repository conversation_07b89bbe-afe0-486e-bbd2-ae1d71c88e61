package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.entity.ITXSession
import com.vsimtone.rirm2.backend.entity.Organization
import com.vsimtone.rirm2.backend.entity.QITXSession
import com.vsimtone.rirm2.backend.entity.QOrganization
import com.vsimtone.rirm2.backend.repository.OrganizationRepository
import com.vsimtone.rirm2.backend.services.BaseService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class OrganizationService : BaseCrudService<Organization,OrganizationRepository>(QOrganization.a) {

    @Autowired
    lateinit var idGeneratorService: IDGeneratorService;


    fun init(name: String) {
        var org: Organization? = repo.findByName(name)
        if (org == null) {
            org = Organization();
            org.parent = null;
            org.name = name;
            org.path = updatePath(org);
            repo.save(org);
        }
    }

    override fun onReady() {
        runInTran {
            init("总行");
        }
    }

    fun updatePath(data: Organization): String {
        if (data.parent == null)
            data.path = data.id.toString() + "|";
        else
            data.path = data.parent!!.path.toString() + data.id.toString() + "|";
        return data.path;
    }

    fun getNames(org: Organization): List<String> {
        val names = mutableListOf<String>()
        var t = org
        while (true) {
            names.add(t.name)
            t = t.parent ?: break
        }
        names.reverse()
        return names;
    }

    fun getParentName(parent: Long?,name: String): Organization{
        var org = repo.findOne(QOrganization.a.parent.id.eq(parent).and(QOrganization.a.name.eq(name))).orElse(null);
        return org;
    }

}
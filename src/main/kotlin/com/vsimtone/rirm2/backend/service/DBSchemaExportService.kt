package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.services.BaseService
import com.vsimtone.rirm2.backend.utils.Log
import com.vsimtone.rirm2.backend.utils.SnowflakeID
import javax.persistence.EntityManager
import org.apache.commons.lang3.StringUtils
import org.hibernate.boot.MetadataSources
import org.hibernate.engine.spi.SessionFactoryImplementor
import org.hibernate.metamodel.model.domain.internal.EntityTypeImpl
import org.hibernate.tool.hbm2ddl.SchemaUpdate
import org.hibernate.tool.schema.TargetType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.io.File
import java.util.*


@Service
class DBSchemaExportService {

    protected var logger = Log[this.javaClass];

    @Value("\${app.schema_update_file}")
    var schemaUpdateFile: String? = null

    @Autowired
    lateinit var entityManager: EntityManager

    fun deleteEnumCheck() {
        val ok = entityManager.createNativeQuery("select C_VAL from APPCONFIG where C_KEY = 'core.db_migration.delete_enum_constraints'").resultList.firstOrNull()
        if (ok == null) {
            val result = entityManager.createNativeQuery(
                "SELECT TABLE_NAME, CONSTRAINT_NAME, SEARCH_CONDITION FROM user_constraints\n" +
                    "WHERE LOWER(table_name) in ('alert_record','alert_rule','appconfig','command_rule','excel_import','rirm2_itx_session','rirm2_itx_command','rirm2_itx_event_queue') AND  constraint_type = 'C'"
            ).resultList;
            result.forEach {
                val it2 = it as (Array<Any>)
                val tabName = it2[0].toString()
                val constName = it2[1].toString()
                val condName = it2[2].toString()
                if (
                    condName.lowercase().startsWith("mail_send_status in") ||
                    condName.lowercase().startsWith("alert_level in") ||
                    condName.lowercase().startsWith("type in") ||
                    condName.lowercase().startsWith("alert_level in") ||
                    condName.lowercase().startsWith("status in") ||
                    condName.lowercase().startsWith("audit_result in") ||
                    condName.lowercase().startsWith("process_result in") ||
                    condName.lowercase().startsWith("type between")
                ) {
                    this.logger.info("auto delete oracle constraint check: ${tabName} ${constName} ${condName}")
                    entityManager.createNativeQuery("ALTER TABLE ${tabName} DROP CONSTRAINT ${constName}").executeUpdate();
                }
            }
            entityManager.createNativeQuery("alter table rirm2_itx_session ADD type2 varchar2(255 char)").executeUpdate();
            entityManager.createNativeQuery("update rirm2_itx_session set type2 = 'Linux' where type = 0").executeUpdate();
            entityManager.createNativeQuery("update rirm2_itx_session set type2 = 'BOC' where type = 1").executeUpdate();
            entityManager.createNativeQuery("alter table rirm2_itx_session DROP COLUMN type").executeUpdate();
            entityManager.createNativeQuery("alter table rirm2_itx_session RENAME COLUMN type2 to type").executeUpdate();
            entityManager.createNativeQuery(
                "insert into APPCONFIG(id, created_at, deleted, updated_at, c_key, type, c_val) " +
                    "VALUES (${SnowflakeID(0).nextId()}, SYSTIMESTAMP, 0, SYSTIMESTAMP, 'core.db_migration.delete_enum_constraints', 'String', 'done')"
            ).executeUpdate();
            this.logger.info("core.db_migration.delete_enum_constraints done.");
        }
    }

    @Transactional(propagation = Propagation.REQUIRED)
    fun doGenerate() {
        deleteEnumCheck();
        if (StringUtils.isAllBlank(schemaUpdateFile)) return
        logger.info("Generate schema update to ${schemaUpdateFile} start")
        val file = File(schemaUpdateFile!!)
        if (file.exists()) file.delete()
        val emf = entityManager.entityManagerFactory
        val sessionFactory = emf.unwrap(SessionFactoryImplementor::class.java)
        val serviceRegistry = sessionFactory.serviceRegistry.parentServiceRegistry
        val metadataSources = MetadataSources(serviceRegistry)
        entityManager.metamodel.entities.forEach {
            metadataSources.addAnnotatedClass((it as EntityTypeImpl).javaType)
        }

//        val validate = SchemaValidator()
//        validate.validate(metadataSources.buildMetadata())

//        val export = SchemaExport()
//        export.setOutputFile(schemaUpdateFile)
//        export.createOnly(EnumSet.of(TargetType.SCRIPT), metadataSources.buildMetadata())

        val update = SchemaUpdate()
        update.setOutputFile(schemaUpdateFile)
        update.execute(EnumSet.of(TargetType.SCRIPT), metadataSources.buildMetadata());

        logger.info("Generate schema update success.")
        file.readLines().forEach {
            logger.info("Generate schema update sql: ${it}")
        }
    }
}

package com.vsimtone.rirm2.backend.service

import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.entity.ChangeFormHost
import com.vsimtone.rirm2.backend.entity.ITXSession
import com.vsimtone.rirm2.backend.entity.QChangeFormCommand
import com.vsimtone.rirm2.backend.entity.QChangeFormHost
import com.vsimtone.rirm2.backend.entity.QITXSession
import com.vsimtone.rirm2.backend.repository.ChangeFormHostRepository
import com.vsimtone.rirm2.backend.repository.ITXSessionRepository
import com.vsimtone.rirm2.backend.services.BaseService
import javax.persistence.EntityManager
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.BeanUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service

@Service
class ChangeFormHostService : BaseCrudService<ChangeFormHost, ChangeFormHostRepository>(QChangeFormHost.a) {

    @Autowired
    @Lazy
    lateinit var changeFormItemService: ChangeFormItemService

    @Autowired
    lateinit var excelService: ExcelImportService

    companion object {
        val UserIDRegex = "[a-zA-Z0-9_\\-]+".toRegex()
    }

    fun getUserIds(idStr: String): List<String> {
        return UserIDRegex.findAll(idStr).map { it.value }.toSet().toList()
    }

    override fun onReady() {
        val define = excelService.define(
            "变更单-控制室日志",
            1,
            ChangeFormHost::class,
            listOf(
                ExcelImportService.Companion.ColumnDefine("编号", ChangeFormHost::lineIndex, true),
                ExcelImportService.Companion.ColumnDefine("申请单号", ChangeFormHost::applyId, true),
                ExcelImportService.Companion.ColumnDefine("日期", ChangeFormHost::sponsorTime, true),
                ExcelImportService.Companion.ColumnDefine("申请人", ChangeFormHost::sponsorUser, false),
                ExcelImportService.Companion.ColumnDefine("复核人", ChangeFormHost::checkUser, false),
                ExcelImportService.Companion.ColumnDefine("授权人", ChangeFormHost::auditUser, false),
                ExcelImportService.Companion.ColumnDefine("所属团队", ChangeFormHost::team, false),
                ExcelImportService.Companion.ColumnDefine("工作类型", ChangeFormHost::workType, false),
                ExcelImportService.Companion.ColumnDefine("所在地", ChangeFormHost::address, false),
                ExcelImportService.Companion.ColumnDefine("变更编号", ChangeFormHost::changeFormId, true),
                ExcelImportService.Companion.ColumnDefine("工作记录", ChangeFormHost::workRecord, false),
                ExcelImportService.Companion.ColumnDefine("系统名称", ChangeFormHost::sysName, false),
                ExcelImportService.Companion.ColumnDefine("ID/OPS名称", ChangeFormHost::idOpsName, true),
                ExcelImportService.Companion.ColumnDefine("ID/OPS编号", ChangeFormHost::idOpsNumber, true),
                ExcelImportService.Companion.ColumnDefine("环境类型", ChangeFormHost::envType, false),
                ExcelImportService.Companion.ColumnDefine("开始时间", ChangeFormHost::beginTime, true),
                ExcelImportService.Companion.ColumnDefine("结束时间", ChangeFormHost::endTime, true),
                ExcelImportService.Companion.ColumnDefine("使用时间", ChangeFormHost::useTime, false),
                ExcelImportService.Companion.ColumnDefine("发放人", ChangeFormHost::sendUser, false),
                ExcelImportService.Companion.ColumnDefine("回收人", ChangeFormHost::recycleUser, false),
                ExcelImportService.Companion.ColumnDefine("服务器IP", ChangeFormHost::serverIP, false)
            )
        )
        define.onData = { ctx: ExcelImportService.Companion.ImportContext<ChangeFormHost>, data: ChangeFormHost?, row: Int ->
            this.onImportData(ctx, data, row)
        }
        define.countImported = {
            repo.count(QChangeFormHost.a.fromImport.eq(it))
        }
    }

    fun onImportData(ctx: ExcelImportService.Companion.ImportContext<ChangeFormHost>, _data: ChangeFormHost?, row: Int): String? {
        val form = _data!!
        val a = QChangeFormHost.a;
        form.idOpsName = getUserIds(form.idOpsName!!).joinToString("|")
        form.changeFormId.split(Regex("[\\r\\s\\n\\t,，;；]+")).forEach {
            if (StringUtils.isEmpty(it)) return@forEach
            val q = BooleanBuilder()
//            重复数据不会导入两条；判别标识为：变更编号、ID/OPS编号
            q.and(a.changeFormId.eq(it))
            q.and(a.idOpsNumber.eq(form.idOpsNumber))
            val data = repo.findAll(q).firstOrNull() ?: ChangeFormHost()
            form.id = data.id;
            form.createdAt = data.createdAt
            form.updatedAt = data.updatedAt
            BeanUtils.copyProperties(form, data);
            data.changeFormId = it;
            repo.save(data);
            repo.flush();
            data.fromImport = ctx.import;
            changeFormItemService.addToMatchQueue(data.changeFormId);
        }
        return null;
    }
}

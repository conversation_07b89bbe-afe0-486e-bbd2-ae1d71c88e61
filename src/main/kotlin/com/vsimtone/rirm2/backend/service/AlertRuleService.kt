package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.entity.*
import com.vsimtone.rirm2.backend.repository.AlertRuleRepository
import com.vsimtone.rirm2.backend.services.BaseService
import com.vsimtone.rirm2.backend.utils.AppUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.mail.SimpleMailMessage
import org.springframework.mail.javamail.JavaMailSender
import org.springframework.mail.javamail.JavaMailSenderImpl
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

@Service
class AlertRuleService : BaseCrudService<AlertRule, AlertRuleRepository>(QAlertRule.a) {

    @Autowired
    lateinit var alertRecordService: AlertRecordService

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    lateinit var appConfigService: APPConfigService

    companion object {
        const val CNF_NAME_MAIL_HOST = "core.mail.host";
        const val CNF_NAME_MAIL_PORT = "core.mail.port";
        const val CNF_NAME_MAIL_USERNAME = "core.mail.username";
        const val CNF_NAME_MAIL_PASSWORD = "core.mail.password";
        const val CNF_NAME_MAIL_PROTOCOL = "core.mail.protocol";
        const val CNF_NAME_MAIL_CRYPT_PROTOCOL = "core.mail.crypt_protocol";
    }

    override fun onReady() {
        appConfigService.init(CNF_NAME_MAIL_HOST, "", APPConfig.Type.String)
        appConfigService.init(CNF_NAME_MAIL_PORT, 465, APPConfig.Type.Integer)
        appConfigService.init(CNF_NAME_MAIL_USERNAME, "", APPConfig.Type.String)
        appConfigService.init(CNF_NAME_MAIL_PASSWORD, "", APPConfig.Type.String)
        appConfigService.init(CNF_NAME_MAIL_PROTOCOL, "smtp", APPConfig.Type.String)
        appConfigService.init(CNF_NAME_MAIL_CRYPT_PROTOCOL, "ssl", APPConfig.Type.String)
        schedule("alert_rule:send_mail", 60 * 1000) {
            sendMail();
        }
    }

    fun doAlert(cmd: ITXCommand, alertRule: AlertRule) {
        if (cmd.rule == null) return;
        if (StringUtils.isNotEmpty(alertRule.serverIP) && cmd.session.serverIP != alertRule.serverIP) { // 服务器IP不匹配
            return;
        }
        if (alertRule.commandRule != null) { // 告警规则
            if (cmd.rule != alertRule.commandRule) { // 匹配告警规则
                return;
            }
        } else {
            if (alertRule.commandCategory == null && alertRule.alertLevel == null) return; // 无匹配规则
            if (alertRule.alertLevel != null && cmd.rule!!.alertLevel!!.ordinal < alertRule.alertLevel!!.ordinal) { // 匹配告警等级
                return;
            }
            if (alertRule.commandCategory != null && cmd.rule!!.commandCategory != alertRule.commandCategory) { // 匹配命令分类
                return;
            }
        }
        val record = AlertRecord()
        record.alertRule = alertRule;
        record.cmd = cmd;
        record.sess = cmd.session;
        record.cmdRule = cmd.rule;
        record.cmdCategory = cmd.rule?.commandCategory;
        record.mailSendStatus = AlertRecord.MailSendStatus.Waiting;
        alertRecordService.repo.save(record);
        logger.info("Alert hit ${cmd} : ${alertRule}");
    }

    fun getJavaMailSender(): JavaMailSender {
        val mailSender = JavaMailSenderImpl();
        mailSender.host = appConfigService.getString(CNF_NAME_MAIL_HOST);
        mailSender.port = appConfigService.getInteger(CNF_NAME_MAIL_PORT)!!;
        mailSender.username = appConfigService.getString(CNF_NAME_MAIL_USERNAME);
        mailSender.password = appConfigService.getString(CNF_NAME_MAIL_PASSWORD);
        val props = mailSender.javaMailProperties
        props["mail.transport.protocol"] = appConfigService.getString(CNF_NAME_MAIL_PROTOCOL);
        props["mail.smtp.auth"] = "true"
        props["mail.debug"] = "true"
        val ep = appConfigService.getString(CNF_NAME_MAIL_CRYPT_PROTOCOL);
        if (ep == "starttls")
            props["mail.smtp.starttls.enable"] = "true";
        if (ep == "ssl") {
            props["mail.smtp.ssl.enable"] = "true";
        }
        return mailSender;
    }

    private fun sendMail() {
        var mailSender = this.getJavaMailSender()
        var mails = alertRecordService.repo.findAllByMailSendStatus(AlertRecord.MailSendStatus.Waiting).groupBy { it.alertRule }
        mails.forEach { rule, records ->
            var success = true;
            val msg = SimpleMailMessage()
            val receivers = (rule.mailReceivers ?: "").split(Regex("[\r\n\t\\s,|，]+")).filter { StringUtils.isEmpty(it) == false }.filter { AppUtils.MAIL_REGEXP_FULL.matches(it) }.toTypedArray()
            if (receivers.isEmpty()) return@forEach
            msg.setTo(*receivers)
            msg.subject = rule.alertTitle
            msg.from = appConfigService.getString(CNF_NAME_MAIL_USERNAME);
            var alertLevelCount = mutableMapOf<String, Int>()
            records.forEach {
                if (!alertLevelCount.containsKey(it.alertRule.alertLevel.toString()))
                    alertLevelCount[it.alertRule.alertLevel.toString()] = 0
                alertLevelCount[it.alertRule.alertLevel.toString()] = alertLevelCount[it.alertRule.alertLevel.toString()]!! + 1
            }
            val alertLevelCountStr = alertLevelCount.toList().map { it.first + "=" + it.second }.joinToString(", ")
            var text = "本次告警命中共${records.size}条，${alertLevelCountStr}\n"
            text += "------------------------\n"
            records.forEach {
                val recordLine = mutableListOf<String>()
                recordLine.add("会话ID: " + it.cmd.session.sessionId)
                recordLine.add("会话用户: " + it.cmd.session.loginUser)
                recordLine.add("真实用户: " + it.cmd.session.realUser)
                recordLine.add("用户IP: " + it.cmd.session.clientIP)
                recordLine.add("服务器IP: " + it.cmd.session.serverIP)
                recordLine.add("执行时间: " + BaseEntity.DEFAULT_DATE_FORMAT.format(it.cmd.execTime))
                recordLine.add("执行命令：" + it.cmd.cmd)
                recordLine.add("命令分类：" + it.cmd.rule?.commandCategory?.name ?: "-")
                recordLine.add("命令规则：" + it.cmd.rule?.name ?: "-")
                recordLine.add("告警等级：" + it.cmd.rule?.alertLevel ?: "-")
                text += "命令[${recordLine.joinToString(", ")}]\n"
            }
            text += "------------------------\n"
            msg.text = text;
            try {
                mailSender.send(msg);
            } catch (e: Exception) {
                this.logger.error("Send alert mail error ${rule}", e);
                success = false;
            }
            records.forEach {
                if (success)
                    it.mailSendStatus = AlertRecord.MailSendStatus.Success
                else
                    it.mailSendStatus = AlertRecord.MailSendStatus.Fail
                alertRecordService.repo.save(it)
            }
        }
    }
}

package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.services.BaseService
import com.vsimtone.rirm2.backend.services.BaseService.Companion.shutdowning
import org.redisson.Redisson
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.TransactionStatus
import java.util.concurrent.TimeUnit

/**
 * Created by zhangkun on 2015/4/25.
 */
@Service
class RedisService : BaseService() {

    @Autowired
    lateinit var redisson: Redisson

    @Throws(Exception::class)
    fun runInLockAndTransaction(
        key: String,
        waitLockSec: Long,
        cb: (status: TransactionStatus) -> Unit
    ): Bo<PERSON>an {
        return runInLock(key, waitLockSec) {
            runInTran {
                cb(it)
            }
        }
    }

    fun runInLock(key: String, waitLockSec: Long = 0, cb: () -> Unit): Bo<PERSON>an {
        val lock = redisson.getLock("lock:$key") // 使用可重入锁，公平锁在特殊情况下会导致等待时间爆炸长
        if (lock.tryLock(waitLockSec, -1, TimeUnit.SECONDS)) {
            try {
                cb();
                return true;
            } catch (e: InterruptedException) {
                if (shutdowning) return false;
                else throw e;
            } finally {
                lock.unlock()
            }
        }
        return false
    }

}

package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.entity.FileFolder
import com.vsimtone.rirm2.backend.entity.Organization
import com.vsimtone.rirm2.backend.repository.FileFolderRepository
import com.vsimtone.rirm2.backend.services.BaseService
import org.hibernate.Session
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class FileFolderService : BaseService() {

    @Autowired
    lateinit var idGeneratorService: IDGeneratorService

    @Autowired
    @Lazy
    lateinit var fileService: FileInfoService

    @Autowired
    lateinit var repo: FileFolderRepository

    @Transactional
    fun delFolder(folder: FileFolder) {
        fileService.repo.updateFolderByFolder(getRoot().id!!, folder.id!!)
        repo.findByParentId(folder.id!!).forEach {
            delFolder(it);
        }
        repo.delete(folder);
    }

    override fun onReady() {
        var c = repo.count()
        if (c == 0L) {
            var root = FileFolder()
            root.name = "根"
            save(root);
            fileService.repo.updateFolderByNoFolder(root.id!!);
        }
    }

    fun updatePath(data: FileFolder) {
        if (data.parent == null)
            data.path = data.id.toString() + "|";
        else
            data.path = data.parent!!.path.toString() + data.id.toString() + "|";
    }

    fun save(data: FileFolder) {
        val isNew = data.id == null
        if (isNew)
            data.id = idGeneratorService.snowflakeID.nextId();
        updatePath(data);
        if (isNew)
            entityManager.unwrap(Session::class.java).save(data);
        else
            entityManager.unwrap(Session::class.java).update(data);
    }

    fun isParent(parent: FileFolder, org: FileFolder): Boolean {
        return (org.path.startsWith(parent.path))
    }

    fun getRoot(): FileFolder {
        return repo.findByParentIsNull()!!
    }

    fun autoCreateOrGet(path: String): FileFolder {
        if (path.indexOf("/") != 0) throw IllegalArgumentException("path must be start with '/'");
        var curr = getRoot();
        path.substring(1).split("/").forEach {
            var e = repo.findFirstByParentIdAndName(curr.id!!, it) ?: FileFolder();
            if (e.id == null) {
                e.parent = curr;
                e.name = it;
                save(e);
            }
            curr = e;
        }
        return curr;
    }

    fun getNames(org: FileFolder): List<String> {
        val names = mutableListOf<String>()
        var t = org
        while (true) {
            names.add(t.name)
            t = t.parent ?: break
        }
        names.reverse()
        return names;
    }

}
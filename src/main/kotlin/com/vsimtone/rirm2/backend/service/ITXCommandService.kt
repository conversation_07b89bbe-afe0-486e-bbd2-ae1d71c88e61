package com.vsimtone.rirm2.backend.service

import com.querydsl.core.BooleanBuilder
import com.querydsl.core.group.GroupBy.groupBy
import com.querydsl.core.types.Expression
import com.querydsl.core.types.dsl.BooleanExpression
import com.vsimtone.rirm2.backend.entity.*
import com.vsimtone.rirm2.backend.entity.ITXCommand.Companion.AuditResult
import com.vsimtone.rirm2.backend.entity.ITXCommand.Companion.ProcessResult
import com.vsimtone.rirm2.backend.repository.ITXCommandRepository
import com.vsimtone.rirm2.backend.utils.CryptUtils
import org.springframework.stereotype.Service
import java.text.SimpleDateFormat
import java.util.Date

@Service
class ITXCommandService : BaseCrudService<ITXCommand, ITXCommandRepository>(QITXCommand.a) {

    fun alertLevelCount(startTime: String?, endTime: String?): List<Array<Any>> {
        val list = entityManager.createQuery(
            "select TO_CHAR(ard.createdAt,'yyyy-mm') ,cr.alertLevel,count(*) from AlertRecord ard left join CommandRule cr on ard.cmdRule.id = cr.id  " +
                "where ard.createdAt >= TO_DATE(:startTime,'yyyy-mm-dd') and ard.createdAt < TO_DATE(:endTime,'yyyy-mm-dd') " +
                "group by TO_CHAR(ard.createdAt,'yyyy-mm'),cr.alertLevel "
        ).setParameter("startTime", startTime).setParameter("endTime", endTime).resultList;

        return list as List<Array<Any>>;
    }

    fun auditResultCount(startTime: String?, endTime: String?): List<Array<Any>> {
        val list = entityManager.createQuery(
            "select TO_CHAR(execTime,'yyyy-mm') as time, count(CASE WHEN auditResult= 'Pending' then 1 ELSE NULL END) AS Pending, " +
                "count(CASE WHEN auditResult= 'Pass' then 1 ELSE NULL END) AS Pass, "+
                "count(CASE WHEN auditResult= 'NoPass' AND processResult = 'Confirmed' then 1 ELSE NULL END) AS NoPass "+
                "from ITXCommand " +
                "where execTime >= TO_DATE(:startTime,'yyyy-mm-dd') and execTime < TO_DATE(:endTime,'yyyy-mm-dd') " +
                "group by TO_CHAR(execTime,'yyyy-mm')"
        ).setParameter("startTime", startTime).setParameter("endTime", endTime).resultList;

        return list as List<Array<Any>>;
    }


    fun severityCategoryLevel(_baseQuery: BooleanBuilder, startTime: String?, endTime: String?): List<Map<String, Any>> {
        val a = QAlertRecord.a
        val stats = mutableMapOf<String, Any>()

        val _timeQuery = BooleanBuilder()
        if (startTime != null && endTime != null)
            _timeQuery.and(a.createdAt.between(SimpleDateFormat("yyyy-MM-dd").parse(startTime), SimpleDateFormat("yyyy-MM-dd").parse(endTime)))

        val timeQuery = fun(): BooleanBuilder { return _timeQuery.clone() }
        val baseQuery = fun(): BooleanBuilder { return _baseQuery.clone() }

        val list = queryFactory.from(a)
            .select(a.cmdCategory.name, a.cmdRule.alertLevel, a.id.count())
            .where(baseQuery(), timeQuery())
            .groupBy(a.cmdCategory.name, a.cmdRule.alertLevel)
            .fetch().map {
                mapOf(
                    "categoryName" to it.get(a.cmdCategory.name),
                    "alertLevel" to it.get(a.cmdRule.alertLevel),
                    "count" to it.get(a.id.count())
                )
            }


        return list as List<Map<String, Any>>;
    }

    fun combineStrArr(s: String, vararg arrays: List<String>): List<String> {
        if (arrays.isEmpty()) return listOf()
        return arrays.fold(listOf("")) { acc, array ->
            acc.flatMap { prefix ->
                array.map { element ->
                    if (prefix.isEmpty()) element else "${prefix}${s}${element}"
                }
            }
        }
    }

    fun teamAuditCount(alertLevel: CommandRule.AlertLevel,startTime: Date?, endTime: Date?): List<Any> {
        val a = QITXCommand.a;
        val data = mutableListOf<MutableMap<String, Any?>>()
        val useAlertLevels = arrayOf(alertLevel)
        val useAuditResults = arrayOf(AuditResult.Pass, AuditResult.NoPass)
        val useProcessResults = arrayOf(ProcessResult.Confirmed)
        val keyJoinStr = "_"
        val baseFilter = fun(e: BooleanExpression?): BooleanBuilder {
            var q = BooleanBuilder()
            if (e != null) q = q.and(e)
            return q.and(a.execTime.between(startTime, endTime))
        }
        val teams = mutableMapOf<String, MutableMap<String, Any?>>()
        val countByGroup = fun(wheres: BooleanBuilder, groups: Array<Expression<*>>) {
            query().select(a.changeFormItem.team, a.count(), *groups).where(wheres).groupBy(a.changeFormItem.team, *groups).fetch().forEach {
                val team = it.get(a.changeFormItem.team).toString()
                var key = groups.map { g ->
                    it.get(g)
                }.joinToString(keyJoinStr)
                if (teams[team] == null)
                    teams[team] = mutableMapOf()
                if(key==alertLevel.name){
                    key="totalCommand"
                }
                teams[team]!![key] = it.get(a.count())
            }
        }
        countByGroup(
            baseFilter(a.rule.alertLevel.`in`(alertLevel).and(a.processResult.isNotNull)),
            arrayOf(a.rule.alertLevel)
        )
        countByGroup(
            baseFilter(a.rule.alertLevel.`in`(alertLevel).and(a.processResult.`in`(ITXCommand.Companion.ProcessResult.AutoConfirmed))),
            arrayOf(a.processResult)
        )
        countByGroup(
            baseFilter(a.rule.alertLevel.`in`(alertLevel).and(a.auditResult.`in`(ITXCommand.Companion.AuditResult.Pending))),
            arrayOf(a.auditResult)
        )
        countByGroup(
            baseFilter(a.rule.alertLevel.`in`(alertLevel).and(a.auditResult.`in`(*useAuditResults)).and(a.processResult.`in`(ITXCommand.Companion.ProcessResult.Confirmed))),
            arrayOf(a.auditResult,a.processResult)
        )

        val allKeys = mutableListOf<String>()
        allKeys.addAll(combineStrArr(keyJoinStr,  useAlertLevels.map { it.name }))
        allKeys.addAll(combineStrArr(keyJoinStr, listOf(ProcessResult.AutoConfirmed.name)))
        allKeys.addAll(combineStrArr(keyJoinStr, listOf(AuditResult.Pending.name)))
        allKeys.addAll(combineStrArr(keyJoinStr,  useAuditResults.map { it.name }, useProcessResults.map { it.name }))
        allKeys.forEach { k1 ->
            teams.forEach {
                if (it.value[k1] == null) it.value[k1] = 0;
            }
        }
        teams.forEach {
            val nit = mutableMapOf<String, Any?>(
                "team" to it.key.toString()
            )
            nit.putAll(it.value)
            data.add(nit)
        }
        return data;
    }


    fun commandCategoryProcess(beginTime: String,endTime: String): List<Array<Any>> {
        val list = entityManager.createQuery(
            "select cc.name, " +
                "COUNT(*) AS commandTotal, " +
                "COUNT( CASE WHEN ric.processResult= 'AutoConfirmed' then 1 ELSE NULL END) AS AutoConfirmed, " +
                "COUNT( CASE WHEN ric.processResult= 'UnProcess' then 1 ELSE NULL END) AS UnProcess, " +
                "COUNT( CASE WHEN ric.processResult= 'Confirmed' then 1 ELSE NULL END) AS Confirmed " +
                "from ITXCommand ric,CommandRule cr,CommandCategory cc " +
                "where ric.rule.id = cr.id and cr.commandCategory.id = cc.id and ric.processResult is not null and " +
                "ric.execTime >=TO_DATE(:beginTime,'yyyy-mm-dd HH24:MI:SS') and ric.execTime <=TO_DATE(:endTime,'yyyy-mm-dd HH24:MI:SS') " +
                "group by cc.name order by UnProcess"
        ).setParameter("beginTime", beginTime).setParameter("endTime", endTime).resultList;
        return list as List<Array<Any>>;
    }

    fun getCmdId(cmd: ITXCommand): String {
        return CryptUtils.sha256hex("${cmd.session.type}:${cmd.session.sessionId}:${cmd.execTime!!.time}:${cmd.cmd}:${cmd.cmdDesc}:${cmd.ruleCode}:${cmd.replayInfo}").substring(0, 32)
    }

}

package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.entity.CommandRule
import com.vsimtone.rirm2.backend.entity.QCommandRule
import com.vsimtone.rirm2.backend.repository.CommandRuleRepository
import com.vsimtone.rirm2.backend.services.BaseService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class CommandRuleService : BaseCrudService<CommandRule, CommandRuleRepository>(QCommandRule.a) {

    fun init(ruleCode: String, desc: String) {
        var rule: CommandRule? = repo.findByRuleCode(ruleCode)
        if (rule == null) {
            rule = CommandRule();
            rule.name = ruleCode;
            rule.ruleCode = ruleCode;
            rule.description = desc;
            repo.save(rule)
        }
    }

    override fun onReady() {
        init("R_C_01_a_01", "用户通过Security菜单更改RACF用户设置");
        init("R_C_01_a_02", "用户通过Security菜单更改RACF权限设置");
        init("R_D_01_a_03", "用户通过ISPF菜单发布RACF命令");
        init("R_D_01_a_04", "用户通过ISPF菜单revoke UserID");
        init("R_A_01_a_05", "用户通过Security菜单更改RACF审计设置");
        init("R_A_02_a_01", "用户通过HCD菜单更改I/O资源");
        init("R_A_02_a_02", "用户通过WLM菜单更改WLM Policy");
        init("R_C_02_a_03", "用户通过ISPF菜单更改系统数据集");
        init("R_A_02_a_04", "用户通过SDSF菜单对磁盘，磁带机进行ONLINE,OFFLINE操作");
        init("R_C_02_a_05", "用户通过ISMF菜单操作磁盘资源");
        init("R_D_02_a_06", "用户通过ISMF菜单操作带库资源");
        init("R_A_02_a_07", "用户通过ISPF/PDF菜单方式日常操作维护数据集");
        init("R_A_02_a_08", "用户通过ISPF/PDF菜单访问运维作业库数据集");
        init("R_D_03_c_01", "用户通过SDSF更改一般CICS资源");
        init("R_A_03_c_02", "用户通过SDSF更改敏感CICS资源");
        init("R_A_03_c_03", "用户通过DITTO菜单操作CICS VSAM文件");
        init("R_D_04_a_01", "用户通过GDPS/SA菜单访问/修改PPRC非敏感资源");
        init("R_A_04_a_02", "用户通过GDPS/SA菜单访问/修改PPRC敏感资源");
        init("R_A_05_a_01", "用户通过SDSF菜单启停MQ");
        init("R_C_05_a_02", "用户通过MQ菜单查看信息");
        init("R_C_05_a_03", "用户通过MQ菜单删除Queue");
        init("R_D_06_b_01", "用户通过BMC Catalog Manager方式访问数据库");
        init("R_A_06_b_02", "用户通过DB2 Command方式操作数据库");
        init("R_A_06_b_03", "用户通过SDSF菜单修改数据库参数");
        init("R_A_06_b_04", "用户通过SDSF菜单进行数据库启停操作");
        init("R_D_06_b_05", "用户通过SDSF操作数据库");
        init("R_A_10_a_01", "非法IP登录");
        init("R_A_12_a_01", "无变更单操作");
        init("R_A_07_b_02", "用户登出");
        init("R_A_09_a_01", "作业提交");
        init("R_A_07_b_01", "用户登录");
        init("R_A_09_b_01", "提交作业规则与变更单实施方案匹配. 如果发现未申请的提交作业行为则报警.");
        init("R_A_08_a_01", "命令类");
        init("R_A_08_b_01", "命令规则与变更单实施方案及高危命令列表匹配. 如果发现未申请的高危命令则报警");
        init("R_A_11_a_01", "用户通过SDSF菜单访问共享磁盘");
    }

}

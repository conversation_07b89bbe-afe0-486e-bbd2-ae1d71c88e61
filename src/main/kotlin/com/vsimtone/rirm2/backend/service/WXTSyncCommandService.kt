package com.vsimtone.rirm2.backend.service

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonKey
import com.querydsl.core.util.MathUtils.result
import com.vsimtone.rirm2.backend.entity.*
import com.vsimtone.rirm2.backend.services.BaseService
import com.vsimtone.rirm2.backend.utils.DateUtil
import com.vsimtone.rirm2.backend.utils.HTTPHelper
import com.vsimtone.rirm2.backend.utils.JSONUtils
import com.vsimtone.rirm2.backend.utils.TimeStats
import io.reactivex.rxjava3.internal.util.QueueDrainHelper.request
import okhttp3.Request
import okhttp3.RequestBody
import okhttp3.Response
import okhttp3.internal.closeQuietly
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.text.SimpleDateFormat
import java.time.Instant
import java.util.*
import kotlin.jvm.java
import kotlin.jvm.optionals.getOrNull

@Service
class WXTSyncCommandService : BaseService() {

    companion object {
        const val CNF_KEY_API_URL_BASE = "core.wxt_command_sync.api_url_base";
        const val CNF_KEY_API_USER = "core.wxt_command_sync.api_user";
        const val CNF_KEY_API_PASS = "core.wxt_command_sync.api_pass";
        const val CNF_KEY_API_DELAY = "core.wxt_command_sync.api_delay";
        const val CNF_KEY_SYNC_CRON = "core.wxt_command_sync.sync_cron";
        const val CNF_KEY_SYNC_FORCE_START_TIME = "core.wxt_command_sync.force_start_time";
        const val CNF_KEY_SYNC_FORCE_SYNC_NOW = "core.wxt_command_sync.force_sync_now";
        const val CNF_KEY_SYNC_SESSION_MAX_COMMANDS = "core.wxt_command_sync.session_max_commands";
        const val CNF_KEY_SYNC_SESSION_SEGMENT_HOURS = "core.wxt_command_sync.session_segment_hours";


        class ApiReqParams {
            var token: String = ""
            var endAfter: Date? = null
            var endBefore: Date? = null
            var id: String? = null
            var typeIs: String? = null

            @JsonProperty("protocolIN")
            var protocolIn: String? = null

            var page: Int? = null
            var size: Int? = null
            var sort: String? = null
        }

        open class ApiRespPage<T> {
            var totalPages: Int = 0;
            var totalElements: Int = 0;
            var size: Int = 0;
            var number: Int = 0;
            var page: Int = 0;
            var content: List<T>? = null;
        }

        class ApiSessionAuditListItem {

            @JsonProperty("id")
            var sessionId: String? = null

            var type: String? = null

            var status: Int? = null

            var begin: Long? = null

            var end: Long? = null

            @JsonProperty("c_ip")
            var clientIP: String? = null;

            @JsonProperty("t_ip")
            var serverIP: String? = null;

            @JsonProperty("account")
            var loginUser: String? = null;

            @JsonProperty("user")
            var realUser: String? = null;

            @JsonProperty("prot")
            var protocol: String? = null

            @JsonProperty("sys_type")
            var sysType: String? = null

            override fun toString(): String {
                return "ApiSessionAuditListItem(${JSONUtils.toString(this)})"
            }
        }

        class ApiSessionAuditListData : ApiRespPage<ApiSessionAuditListItem>() {}

        class ApiCommandAuditListItem {
            @JsonProperty("sess_id")
            lateinit var session: String

            @JsonProperty("exec_ts")
            var execTime: Long? = null

            var cmd: String? = null

            @JsonProperty("location")
            var location: String? = null

            override fun toString(): String {
                return "ApiCommandAuditListItem(${JSONUtils.toString(this)})"
            }
        }

        class ApiCommandAuditListData : ApiRespPage<ApiCommandAuditListItem>() {}
    }


    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var wXTSyncCommandRecordService: WXTSyncCommandRecordService

    @Autowired
    lateinit var itxSessionService: ITXSessionService

    @Autowired
    lateinit var itxCommandService: ITXCommandService

    @Autowired
    lateinit var excelImportService: ExcelImportService

    @Autowired
    lateinit var itxEventQueueService: ITXEventQueueService

    val c = HTTPHelper()
    var lastRequestTime: Long = 0L;

    override fun onReady() {
        appConfigService.init(CNF_KEY_API_URL_BASE, "", APPConfig.Type.String);
        appConfigService.init(CNF_KEY_API_USER, "", APPConfig.Type.String);
        appConfigService.init(CNF_KEY_API_PASS, "", APPConfig.Type.String);
        appConfigService.init(CNF_KEY_SYNC_CRON, "", APPConfig.Type.String);
        appConfigService.init(CNF_KEY_SYNC_FORCE_SYNC_NOW, "false", APPConfig.Type.Boolean);
        appConfigService.init(CNF_KEY_SYNC_FORCE_START_TIME, "", APPConfig.Type.String);
        appConfigService.init(CNF_KEY_API_DELAY, "1", APPConfig.Type.Long);
        appConfigService.init(CNF_KEY_SYNC_SESSION_MAX_COMMANDS, "10000", APPConfig.Type.Long);
        appConfigService.init(CNF_KEY_SYNC_SESSION_SEGMENT_HOURS, "1", APPConfig.Type.Long);
        schedule("wxt_session_command_sync.sync", 60 * 1000) {
            this.doSync()
        }
    }

    private fun getUrl(base: String, append: String): String {
        if (base.endsWith("/")) return base + append
        return base + "/" + append
    }

    private fun fetchApiData(_params: ApiReqParams, record: WXTSyncCommandRecord, apiUrl: String): String {
        var retryCount = 0
        while (true) {
            val delay = (appConfigService.getLong(CNF_KEY_API_DELAY) ?: 0) - (System.currentTimeMillis() - lastRequestTime)
            if (delay > 0) {
                this.logger.debug("request $apiUrl need delay $delay")
                Thread.sleep(delay)
            }
            lastRequestTime = System.currentTimeMillis()
            val params = HashMap<String, Any>()
            if (_params.endBefore != null) {
                params.put("endBefore", SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(_params.endBefore))
            }
            if (_params.endAfter != null) {
                params.put("endAfter", SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(_params.endAfter))
            }
            if (_params.id != null && _params.id!!.length > 0) {
                params.put("id", _params.id!!)
            }
            if (_params.page != null) {
                params.put("page", _params.page!!)
            }
            if (_params.size != null) {
                params.put("size", _params.size!!)
            }
            if (_params.sort != null) {
                params.put("sort", _params.sort.toString())
            }
            if (_params.typeIs != null) {
                params.put("typeIs", _params.typeIs.toString())
            }
            if (_params.protocolIn != null) {
                params.put("protocolIN", _params.protocolIn.toString())
            }
            val opts = HTTPHelper.Companion.Options()
            opts.header("cookie", "ST_AUTH_TOKEN=" + _params.token)
            this.logger.info("do request ${apiUrl} params ${JSONUtils.toString(params)}")
            val started = System.currentTimeMillis()
            var resp: HTTPHelper.Companion.RespBytes? = null
            try {
                resp = c.get(apiUrl, params, HTTPHelper.Companion.RespBytes::class.java, opts)
                if (resp.response.code == 429) {
                    retryCount++
                    logger.info("request '${apiUrl}' too many requests. retry in ${retryCount}s")
                    if (retryCount >= 10)
                        Thread.sleep(10 * 1000L)
                    else
                        Thread.sleep(retryCount * 1000L)
                    continue
                }
                val data = resp.body.toString()
                if (data.trim().startsWith("{")) {
                    val d = JSONUtils.toObject(data, Map::class.java)
                    if (d.containsKey("errorMessage") && d.keys.size == 1) {
                        throw RuntimeException("fetch ${apiUrl} error: ${resp}")
                    }
                }
                record.apiUseTime += System.currentTimeMillis() - started
                record.apiDownBytes += data.toByteArray().size
                record.apiSuccRequestCount++
                return data;
            } catch (e: Exception) {
                record.apiFailRequestCount++
                throw e;
            } finally {
                resp?.response?.closeQuietly();
            }
        }
    }

    private inline fun <reified T : ApiRespPage<*>, T2> fetchAllListData(params: ApiReqParams, record: WXTSyncCommandRecord, name: String, apiUrl: String, delayMs: Long = 0, maxRecords: Long = -1): MutableList<T2?> {
        params.page = 0;
        params.size = 1000;
        val data = mutableListOf<T2?>()
        var retry = 0;
        while (true) {
            var raw = "";
            try {
                raw = fetchApiData(params, record, apiUrl)
            } catch (e: Exception) {
                retry++;
                if (retry >= 5) throw e;
                this.logger.warn("fetch data error ${retry}. retry in 10s. message=${e.message}")
                Thread.sleep(10)
                continue;
            }
            retry = 0;
            if (raw.length <= 200)
                this.logger.debug("response: ${raw}")
            else
                this.logger.debug("response: ${raw.substring(0, 200)}")

            val p = JSONUtils.toObject(raw, T::class.java) as ApiRespPage<*>
            record.apiListDataCount += p.content!!.size
            p.content!!.forEach {
                if (it != null) data.add(it as T2)
            }
            if (maxRecords >= 0 && p.totalElements >= maxRecords && ((params.page!! + 1) * params.size!!) >= maxRecords) {
                logger.info("fetch ${name} over max records. return.")
                data.add(null)
                return data
            }
            logger.info("fetch ${name} page data: page=${params.page!!}/${p.totalPages}, size=${data.size}/${p.totalElements}")
            if (data.size >= p.totalElements || p.content!!.size < params.size!! || params.page!! >= p.totalPages - 1) break
            params.page = params.page!! + 1;
            Thread.sleep(delayMs);
        }
        return data;
    }

    fun doGetToken(newRecord: WXTSyncCommandRecord, urlBase: String, username: String?, password: String?): String {
        val resp = c.postJSON(getUrl(urlBase, "authenticate"), mutableMapOf("username" to username, "password" to password), HTTPHelper.Companion.RespBytes::class.java)
        if (resp.response.code != 200) {
            throw RuntimeException("get token fail. response code is ${resp.response.code}")
        }

        try {
            val result = JSONUtils.toObject(resp.body.toString(), Map::class.java)
            val token = result["ST_AUTH_TOKEN"]?.toString() ?: ""
            if (StringUtils.isEmpty(token)) throw RuntimeException("authenticate fail: " + JSONUtils.toString(result))
            logger.info("got auth token: $token")
            return token
        } catch (e: Exception) {
            throw RuntimeException("get token fail. response is ${resp.body.toString()}")
        }
    }

    fun doSyncSession(record: WXTSyncCommandRecord, apiBase: String, token: String): List<ApiSessionAuditListItem> {
        val items = mutableListOf<ApiSessionAuditListItem>()
        val calendar = Calendar.getInstance()
        calendar.time = record.syncTimeBegin
        val endTime = record.syncTimeEnd!!
        var segmentHours = (appConfigService.getLong(CNF_KEY_SYNC_SESSION_SEGMENT_HOURS) ?: 1).toInt()
        if (segmentHours < 1) segmentHours = 1

        while (calendar.time.before(endTime)) {
            val segmentStart = Date(calendar.timeInMillis)
            calendar.add(Calendar.HOUR_OF_DAY, segmentHours)
            val segmentEnd = if (calendar.time.after(endTime)) endTime else Date(calendar.timeInMillis)
            val beforeSize = items.size;
            this.logger.info("get time range sessions start. begin=${BaseEntity.DEFAULT_DATE_FORMAT.format(segmentStart)}, end=${BaseEntity.DEFAULT_DATE_FORMAT.format(segmentEnd)}")
            doSyncSession(record, apiBase, token, items, segmentStart, segmentEnd)
            this.logger.info("get time range session done. sessions=${items.size - beforeSize}")
        }
        this.logger.info("get all sessions done. sessions=${items.size}")
        return items
    }

    fun doSyncSession(record: WXTSyncCommandRecord, apiBase: String, token: String, items: MutableList<ApiSessionAuditListItem>, beginTime: Date, endTime: Date) {
        val params = ApiReqParams()
        params.endAfter = beginTime
        params.endBefore = endTime
        params.typeIs = "tui"
        params.protocolIn = "ssh,telnet"
        params.token = token
        val data = fetchAllListData<ApiSessionAuditListData, ApiSessionAuditListItem>(params, record, "session", getUrl(apiBase, "sesslog/tui"))
        data.forEach {
            if (it == null) return@forEach
            if (it.status != 2 && it.status != 3) {
                throw RuntimeException("session status error ${it.sessionId} ${it.status}")
            }
            if (StringUtils.isAllBlank(it.sessionId)) {
                record.ignoreSessionCount++;
                this.logger.warn("sessionId empty. ignore ${it}")
                return@forEach
            }
            if (StringUtils.isAllBlank(it.clientIP)) {
                record.ignoreSessionCount++;
                this.logger.warn("clientIP empty. ignore ${it}")
                return@forEach
            }
            if (StringUtils.isAllBlank(it.serverIP)) {
                record.ignoreSessionCount++;
                this.logger.warn("serverIP empty. ignore ${it}")
                return@forEach
            }
            if (StringUtils.isAllBlank(it.loginUser)) {
                record.ignoreSessionCount++;
                this.logger.warn("loginUser empty. ignore ${it}")
                return@forEach
            }
            if (it.protocol != "ssh" && it.protocol != "telnet") {
                record.ignoreSessionCount++;
                this.logger.warn("protocol error. ignore ${it}")
                return@forEach
            }
            if (it.sysType != null && it.sysType!!.lowercase().indexOf("linux") != -1) {
                it.sysType = "linux"
            }
            if (it.sysType != null && (it.sysType!!.lowercase().indexOf("aix") != -1 || it.sysType!!.lowercase().indexOf("hp ux") != -1)) {
                it.sysType = "aix"
            }
            if (it.sysType?.lowercase() != "linux" && it.sysType?.lowercase() != "aix") {
                record.ignoreSessionCount++;
                this.logger.warn("sys type error. ignore ${it}")
                return@forEach
            }
            if (it.begin == null) {
                record.ignoreSessionCount++;
                this.logger.warn("begin empty. ignore ${it}")
                return@forEach
            }
            if (it.end == null) {
                record.ignoreSessionCount++;
                this.logger.warn("end empty. ignore ${it}")
                return@forEach
            }
            val sess = itxSessionService.repo.findOne(QITXSession.a.sessionId.eq(it.sessionId)).getOrNull() ?: ITXSession()
            if (sess.id != null) {
                record.ignoreSessionCount++;
                this.logger.warn("itx session repeat by db: ${sess}");
                return@forEach
            }
            if (items.find { it2 -> it.sessionId == it2.sessionId } != null) {
                record.ignoreSessionCount++;
                this.logger.warn("itx session repeat by api: ${sess}");
                return@forEach
            }
            items.add(it)
        }
    }

    fun getEvtQueue(sess: ApiSessionAuditListItem, time: Long?, type: ITXEventQueue.Companion.Type): ITXEventQueue {
        val evtQueue = ITXEventQueue()
        evtQueue.type = type;
        evtQueue.sessionId = sess.sessionId!!;
        evtQueue.auditTime = time;
        evtQueue.clientIP = sess.clientIP;
        evtQueue.serverIP = sess.serverIP;
        if (sess.sysType?.lowercase() == "linux") evtQueue.serverType = ITXSession.Type.Linux;
        if (sess.sysType?.lowercase() == "aix") evtQueue.serverType = ITXSession.Type.AIX;
        evtQueue.loginUser = sess.loginUser + ">|<" + sess.realUser;
        return evtQueue;
    }

    fun doSyncCommand(record: WXTSyncCommandRecord, apiBase: String, token: String, sesses: List<ApiSessionAuditListItem>, maxCmds: Long): List<ITXEventQueue> {
        val queues = mutableListOf<ITXEventQueue>()
        sesses.forEachIndexed { sessIdx, sess ->
            this.logger.info("get session commands: ${sessIdx}/${sesses.size}")
            val params = ApiReqParams()
            params.token = token
            val data = fetchAllListData<ApiCommandAuditListData, ApiCommandAuditListItem>(params, record, "command:${sess.sessionId}", getUrl(apiBase, "tuidetail/sessid/${sess.sessionId}"), 10, maxCmds)
            if (data.isEmpty()) {
                this.logger.warn("session '${sess.sessionId}' command list empty. ignore.")
                return@forEachIndexed
            }
            if (data.last() == null) {
                data.removeLast()
                record.cmdTooManySessions!!.add(sess.sessionId!!)
            }
            this.logger.info("session '${sess.sessionId}' got ${data.size} commands")

            val startEvt = getEvtQueue(sess, sess.begin!!, ITXEventQueue.Companion.Type.SessionStart);
            queues.add(startEvt);
            data.forEach {
                if (it == null) return@forEach
                val cmdEvt = getEvtQueue(sess, it.execTime, ITXEventQueue.Companion.Type.UnixCommand);
//                if (!StringUtils.isAllEmpty(it.location)) cmdEvt.replayInfo = "wxt_location:" + it.location
                cmdEvt.body = it.cmd
                queues.add(cmdEvt);
            }
            val endEvt = getEvtQueue(sess, sess.end!!, ITXEventQueue.Companion.Type.SessionEnd);
            queues.add(endEvt);
            record.addSessionCount++;
            record.addCommandCount += data.size;
        }
        return queues
    }

    private fun doSync() {
        val urlBase = appConfigService.getString(CNF_KEY_API_URL_BASE) ?: ""
        val username = appConfigService.getString(CNF_KEY_API_USER) ?: ""
        val password = appConfigService.getString(CNF_KEY_API_PASS) ?: ""
        val cron = appConfigService.getString(CNF_KEY_SYNC_CRON) ?: ""
        val forceStartTime = appConfigService.getString(CNF_KEY_SYNC_FORCE_START_TIME) ?: ""
        val forceSyncNow = appConfigService.getBoolean(CNF_KEY_SYNC_FORCE_SYNC_NOW) == true
        if (StringUtils.isEmpty(urlBase) || StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            return
        }
        var lastExecuteTime = 0L
        val lastRecord = queryFactory.from(QWXTSyncCommandRecord.a).where(QWXTSyncCommandRecord.a.success.isTrue).limit(1).orderBy(QWXTSyncCommandRecord.a.createdAt.desc()).fetch().firstOrNull() as WXTSyncCommandRecord?
        if (lastRecord != null) {
            lastExecuteTime = lastRecord.createdAt!!.toInstant().toEpochMilli()
        }
        val newRecord = WXTSyncCommandRecord()
        newRecord.createdAt = Date()
        newRecord.cmdTooManySessions = mutableListOf<String>()
        newRecord.syncTimeEnd = Date(System.currentTimeMillis() - 60 * 1000) // 减去一分钟
        if (lastRecord != null) {
            newRecord.syncTimeBegin = lastRecord.syncTimeEnd
        } else {
            newRecord.syncTimeBegin = Date(System.currentTimeMillis() - 24 * 3600 * 1000) // 减去24小时
        }
        if (!StringUtils.isAllEmpty(forceStartTime) && forceStartTime != "0") {
            newRecord.syncTimeBegin = ExcelImportService.parseDate(forceStartTime)
        }
        if (!forceSyncNow) {
            val nextExecTime = DateUtil.getCronNextTime(cron, lastExecuteTime)
            if (nextExecTime == -1L) {
                logger.error("Cron is invalid: $cron")
            }
            logger.debug("sync cron info: cron=${cron}, last=${lastExecuteTime}, next=$nextExecTime")
            if (nextExecTime <= 0 || nextExecTime > Date().toInstant().toEpochMilli()) {
                return
            }
        } else {
            appConfigService.setConfig(CNF_KEY_SYNC_FORCE_SYNC_NOW, false);
        }
        var maxCmds = appConfigService.getLong(CNF_KEY_SYNC_SESSION_MAX_COMMANDS) ?: 0
        var timeStats = TimeStats()
        try {
            this.logger.info("sync started: ${BaseEntity.DEFAULT_DATE_FORMAT.format(newRecord.syncTimeBegin)} ${BaseEntity.DEFAULT_DATE_FORMAT.format(newRecord.syncTimeEnd)}")
            val token = doGetToken(newRecord, urlBase, username, password)
            timeStats.point("get token")
            val items = doSyncSession(newRecord, urlBase, token)
            this.logger.info("get ${items.size} sessions")
            timeStats.point("get sessions")
            val queues = doSyncCommand(newRecord, urlBase, token, items, maxCmds)
            this.logger.info("get ${queues.size} queues")
            timeStats.point("get commands")
            val locked = excelImportService.importLockRun("wxtSyncCommand@syncSave", 24 * 3600) {
                timeStats.point("get lock")
                this.logger.info("save start")
                runInTran {
                    var lastLogTime = 0L;
                    queues.forEachIndexed { idx, it ->
                        val idx = idx + 1;
                        this.itxEventQueueService.repo.save(it)
                        if (System.currentTimeMillis() - lastLogTime >= 10000 || idx == queues.size - 1) {
                            this.logger.info("save queue progress: ${idx + 1}/${queues.size} ${((idx * 10000.0 / queues.size).toLong() / 100)}%")
                            lastLogTime = System.currentTimeMillis()
                        }
                    }
                }
                this.logger.info("save done")
                timeStats.point("save queues")
                newRecord.success = true;
            }
            if (!locked) throw java.lang.RuntimeException("import lock fail.")

            if (!StringUtils.isAllEmpty(forceStartTime) && forceStartTime != "0") {
                appConfigService.setConfig(CNF_KEY_SYNC_FORCE_START_TIME, "")
            }
            this.logger.info("sync succ. ${newRecord}. time stats: ${timeStats}")
        } catch (e: Exception) {
            newRecord.success = false;
            newRecord.errmsg = e.message
            if (newRecord.errmsg != null && newRecord.errmsg!!.length >= 200) {
                newRecord.errmsg = newRecord.errmsg!!.substring(0, 200)
            }
            this.logger.error("sync fail. ${newRecord}. time stats: ${timeStats}", e)
        }
        runInTran {
            newRecord.updatedAt = Date()
            this.wXTSyncCommandRecordService.save(newRecord)
        }
    }
}
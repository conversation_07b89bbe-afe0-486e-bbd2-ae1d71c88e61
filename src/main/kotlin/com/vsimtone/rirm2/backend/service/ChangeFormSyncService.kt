package com.vsimtone.rirm2.backend.service

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonKey
import com.google.protobuf.Api
import com.hankcs.hanlp.corpus.tag.Nature.i
import com.querydsl.core.types.Order
import com.querydsl.core.types.OrderSpecifier
import com.vsimtone.rirm2.backend.entity.*
import com.vsimtone.rirm2.backend.service.WXTSyncCommandService.Companion.CNF_KEY_API_DELAY
import com.vsimtone.rirm2.backend.services.BaseService
import com.vsimtone.rirm2.backend.utils.DateUtil
import com.vsimtone.rirm2.backend.utils.HTTPHelper
import com.vsimtone.rirm2.backend.utils.JSONUtils
import com.vsimtone.rirm2.backend.utils.RestUtil
import com.vsimtone.rirm2.backend.utils.TimeStats
import okhttp3.internal.closeQuietly
import org.apache.commons.lang3.StringUtils
import org.apache.http.client.utils.URIBuilder
import org.apache.poi.ss.formula.functions.T
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestParam
import java.net.URL
import java.net.URLDecoder
import java.text.SimpleDateFormat
import java.time.Instant
import java.util.*
import kotlin.collections.HashMap
import kotlin.jvm.optionals.getOrNull

@Service
class ChangeFormSyncService : BaseService() {
    companion object {
        const val CNF_KEY_API_CONFIG = "core.changeform.sync.api_config";
        const val CNF_KEY_SYNC_CRON = "core.changeform.sync_cron";
        const val CNF_KEY_SYNC_FORCE_START_TIME = "core.changeform.sync.force_start_time";
        const val CNF_KEY_SYNC_FORCE_SYNC_NOW = "core.changeform.sync.force_sync_now";

        val ChangeTypes = mapOf(
            0 to "标准变更",
            1 to "紧急变更",
            2 to "例行变更",
            3 to "普通变更",
            4 to "重要变更",
            5 to "特急变更"
        )

        val RequestTypes = mapOf(
            0 to "变更申请",
            1 to "实施申请"
        )

        val ChangeRequestStatus = mapOf(
            0 to "申请",
            1 to "变更审批",
            2 to "更新排期",
            3 to "筹备中",
            4 to "实施申请",
            5 to "实施审批",
            6 to "已排期",
            7 to "实施中",
            8 to "待决",
            9 to "已拒绝",
            10 to "已完成",
            11 to "已关闭",
            12 to "已取消"
        )

        val IDUsages = mapOf(
            0 to "维护变更用户",
            1 to "查询用户",
            2 to "运行维护用户（废弃）",
            3 to "数据库用户",
            4 to "FTP用户",
            5 to "日常运维类用户",
            6 to "中间件用户"
        )

        val PwdTypes = mapOf(
            0 to "工具改密",
            1 to "人工改密",
            2 to "Etoken动态密码令牌"
        )

        val IDManageModes = mapOf(
            0 to "普通模式",
            1 to "一体化独占模式（已下线）",
            2 to "一体化共用模式（已下线）",
            3 to "流程纳管模式",
            4 to "集中管理独占模式",
            5 to "集中管理公用模式"
        )

        val PermissionTypes = mapOf(
            0 to "只读",
            1 to "读写"
        )

        val OracleUserRoles = mapOf(
            0 to "SYSOPER",
            1 to "SYSDBA",
            2 to "Normal"
        )

        val OraConManners = mapOf(
            0 to "Server name",
            1 to "SID",
            2 to "TNS"
        )

        val IfAvailables = mapOf(
            0 to "是",
            1 to "否"
        )

        val IfSensitivity = mapOf(
            0 to "是",
            1 to "否"
        )

        val CRadioStatuss = mapOf(
            0 to "始终不可申请",
            1 to "可申请",
            2 to "申请中",
            3 to "审批中",
            4 to "发放中",
            5 to "领取中",
            6 to "使用中",
            7 to "已失效",
            8 to "已删除",
            9 to "临时不可申请",
            10 to "待纳管"
        )

        class API_CONFIG {
            var itemUrl: String = "";
            var cmdUrl: String = "";
            var host1Url: String = "";
            var host2Url: String = "";
            var host3Url: String = "";
            var host4Url: String = "";
            var delay: Long = 0;
            var itemStatusTimeout = 0;
        }

        class ApiReqParams {
            var startTime: Date? = null;
            var endTime: Date? = null;

            @JsonProperty("TicketID")
            var ticketID: String? = null;

            @JsonProperty("ProduceNum")
            var produceNum: String? = null;

            @JsonProperty("IDRecordSN")
            var idRecordSN: String? = null;

            @JsonProperty("IDGroupNo")
            var idGroupNo: String? = null;

            var current: Long? = null;
            var size: Long? = null;
        }

        open class ApiRespPage<T> {
            var total: Int = 0;
            var num: Int = 0;
            var size: Int = 0;
            var current: Int = 0;
            var records: List<T>? = null;
        }

        open class ApiResp {
            var success: Boolean? = null
            var code: Int? = null
            var message: String? = null
            var data: Any? = null
        }

        class ApiChangeFormItemListItem {
            @JsonProperty("Description")
            var description: String? = null

            @JsonProperty("ChangeType")
            var changeType: Int? = null

            @JsonProperty("ChangeType2")
            var changeType2: String? = null

            @JsonProperty("TicketID")
            var tickID: String? = null

            @JsonProperty("RequestType")
            var requestType: Int? = null

            @JsonProperty("ASGRP")
            var asgrp: String? = null

            @JsonProperty("ChangeCooperateDepartment")
            var changeCooperateDepartment: String? = null

            @JsonProperty("Scheduled_Start_Date")
            var scheduledStartDate: String? = null

            @JsonProperty("Scheduled_End_Date")
            var scheduledEndDate: String? = null

            @JsonProperty("Change_Request_Status")
            var changeRequestStatus: Int? = null


            @JsonProperty("StatusDescription")
            var statusDescription: String? = null


            @JsonProperty("real_start_date")
            var realStartDate: String? = null

            @JsonProperty("real_end_date")
            var realEndDate: String? = null

            @JsonProperty("ChangeResult")
            var changeResult: String? = null

            @JsonProperty("SubmitTime")
            var submitTime: String? = null

            @JsonProperty("Approve_Date")
            var approveDate: String? = null

            override fun toString(): String {
                return "ApiChangeFormItemListItem(${JSONUtils.toString(this)})"
            }
        }

        class ApiChangeFormItemListData : ApiRespPage<ApiChangeFormItemListItem>() {}

        class ApiChangeFormCommandListItem {

            @JsonProperty("TicketID")
            var ticketID: String? = null

            @JsonProperty("Description")
            var description: String? = null

            @JsonProperty("Step")
            var step: String? = null

            @JsonProperty("Task")
            var task: String? = null

            @JsonProperty("ChgItem")
            var chgItem: String? = null

            @JsonProperty("StartDate")
            var startDate: String? = null

            @JsonProperty("EndDate")
            var endDate: String? = null

            @JsonProperty("leadTeam_department")
            var leadTeamDepartment: String? = null

            @JsonProperty("leadTeam_executor")
            var leadTeamExecutor: String? = null

            @JsonProperty("leadTeam_checker")
            var leadTeamChecker: String? = null

            @JsonProperty("CoopTeam_department")
            var coopTeamDepartment: String? = null

            @JsonProperty("CoopTeam_executor")
            var coopTeamExecutor: String? = null

            @JsonProperty("CoopTeam_checker")
            var coopTeamChecker: String? = null

            @JsonProperty("OuterTeam")
            var outerTeam: String? = null

            @JsonProperty("Phase")
            var phase: String? = null

            @JsonProperty("Phase2")
            var phase2: String? = null

            @JsonProperty("PhaseRemark")
            var phaseRemark: String? = null

            override fun toString(): String {
                return "ApiChangeFormCommandListItem(${JSONUtils.toString(this)})"
            }
        }

        class ApiChangeFormCommandListData : ApiRespPage<ApiChangeFormCommandListItem>() {}

        class ApiChangeFormHost1ListItem {
            @JsonProperty("TicketID")
            var ticketID: String = "" // ID申请单编号

            @JsonProperty("ApplyTime")
            var applyTime: String = "" // 申请时间

            @JsonProperty("ApplierName")
            var applierName: String = "" // 申请人

            @JsonProperty("StartTime")
            var startTime: String = "" // 开始时间（计划）

            @JsonProperty("EndTime")
            var endTime: String = "" // 结束时间（计划）

            @JsonProperty("ProduceNum")
            var produceNum: String = "" // 变更单号

            @JsonProperty("IDRecordSN")
            var idRecordSN: String = "" // ID编号

            @JsonProperty("IDName")
            var idName: String = "" // ID名称

            @JsonProperty("SystemName")
            var systemName: String = "" // 系统名称

            @JsonProperty("SysEngName")
            var sysEngName: String = "" // 英文简称

            @JsonProperty("ZhuJi_Name")
            var zhuJiName: String = "" // 主机名

            @JsonProperty("IpAddress")
            var ipAddress: String = "" // IP地址

            @JsonProperty("UserName")
            var userName: String = "" // 用户名

            override fun toString(): String {
                return "ApiChangeFormHost1ListItem(${JSONUtils.toString(this)})"
            }
        }

        class ApiChangeFormHost1ListData : ApiRespPage<ApiChangeFormHost1ListItem>() {

        }

        class ApiChangeFormHost2ListItem {
            @JsonProperty("TicketID")
            var ticketID: String = "" // 申请单编号

            @JsonProperty("ApplyTime")
            var applyTime: String = "" // 申请时间

            @JsonProperty("ApplierName")
            var applierName: String = "" // 申请人

            @JsonProperty("StartTime")
            var startTime: String = "" // 开始时间（计划）

            @JsonProperty("EndTime")
            var endTime: String = "" // 结束时间（计划）

            @JsonProperty("ProduceNum")
            var produceNum: String = "" // 变更单号

            @JsonProperty("OpsRecordSN")
            var opsRecordSN: String = "" // Ops群组编号

            @JsonProperty("OpsName")
            var opsName: String = "" // Ops群组名称

            override fun toString(): String {
                return "ApiChangeFormHost2ListItem(${JSONUtils.toString(this)})"
            }
        }

        class ApiChangeFormHost2ListData : ApiRespPage<ApiChangeFormHost2ListItem>() {

        }

        class ApiChangeFormHost3ListItem {
            @JsonProperty("SN")
            var sn: String = "" // 申请单编号

            @JsonProperty("ApplyTime")
            var applyTime: String = "" // 申请时间

            @JsonProperty("ApplierName")
            var applierName: String = "" // 申请人

            @JsonProperty("StartTime")
            var startTime: String = "" // 开始时间（计划）

            @JsonProperty("EndTime")
            var endTime: String = "" // 结束时间（计划）

            @JsonProperty("ProduceNum")
            var produceNum: String = "" // 变更单号

            @JsonProperty("IDGroupIndex")
            var idGroupIndex: String = "" // ID群组编号

            override fun toString(): String {
                return "ApiChangeFormHost3ListItem(${JSONUtils.toString(this)})"
            }
        }

        class ApiChangeFormHost3ListData : ApiRespPage<ApiChangeFormHost3ListItem>() {

        }

        class ApiChangeFormHost4ListItem {
            @JsonProperty("IDGroupNo")
            var idGroupNo: String = "" // ID群组编号

            @JsonProperty("IDRecordSN")
            var idRecordSN: String = "" // ID编号

            @JsonProperty("IDSystem")
            var idSystem: String = "" // 系统名称（中文）

            @JsonProperty("IDPlatform")
            var idPlatform: String = "" // 平台名称

            @JsonProperty("IDUsage")
            var idUsage: Int = -1 // ID用途

            @JsonProperty("PwdType")
            var pwdType: Int = -1 // 密码类型

            @JsonProperty("EnvironmentType")
            var environmentType: String = "" // 环境类型

            @JsonProperty("HostName")
            var hostName: String = "" // 主机名

            @JsonProperty("IDUser")
            var idUser: String = "" // 用户名

            @JsonProperty("IpAddress")
            var ipAddress: String = "" // IP地址

            @JsonProperty("IDManageMode")
            var idManageMode: Int = -1 // ID管理模式

            @JsonProperty("SubPlatForm")
            var subPlatform: String = "" // 子平台名称

            @JsonProperty("PermissionType")
            var permissionType: Int = -1 // 权限类型

            @JsonProperty("URL")
            var url: String = "" // URL

            @JsonProperty("SysEngName")
            var sysEngName: String = "" // 系统名称（英文）

            @JsonProperty("AccessProtocol")
            var accessProtocol: String = "" // 访问协议或方式

            @JsonProperty("AccessPort")
            var accessPort: String = "" // 端口

            @JsonProperty("WinDomain")
            var winDomain: String = "" // 域名

            @JsonProperty("DBName")
            var dbName: String = "" // 数据库名称

            @JsonProperty("OracleUserRole")
            var oracleUserRole: Int = -1 // Oracle用户角色

            @JsonProperty("OraConManner")
            var oraConManner: Int = -1 // Oracle连接方式

            @JsonProperty("TNSDesc")
            var tnsDesc: String = "" // TNS连接串

            @JsonProperty("DBVersion")
            var dbVersion: String = "" // 数据库版本

            @JsonProperty("IfAvailable")
            var ifAvailable: Int = -1 // 是否使用

            @JsonProperty("If_Sensitivity")
            var ifSensitivity: Int = -1 // 是否特权账号

            @JsonProperty("cRadio_Status")
            var cRadioStatus: Int = -1 // ID状态

            override fun toString(): String {
                return "ApiChangeFormHost4ListItem(${JSONUtils.toString(this)})"
            }
        }

        class ApiChangeFormHost4ListData : ApiRespPage<ApiChangeFormHost4ListItem>() {

        }

        class ApiChangeFormHostListItem {
            var item1: ApiChangeFormHost1ListItem? = null
            var item2: ApiChangeFormHost2ListItem? = null
            var item3: ApiChangeFormHost3ListItem? = null
            var item4: ApiChangeFormHost4ListItem? = null
        }

    }

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var changeFormSyncRecordService: ChangeFormSyncRecordService

    @Autowired
    lateinit var changeFormItemService: ChangeFormItemService

    @Autowired
    lateinit var changeFormCommandService: ChangeFormCommandService

    @Autowired
    lateinit var changeFormHostService: ChangeFormHostService

    @Autowired
    lateinit var excelImportService: ExcelImportService

    override fun onReady() {
        appConfigService.init(CNF_KEY_API_CONFIG, "{}", APPConfig.Type.JsonMap);
        appConfigService.init(CNF_KEY_SYNC_CRON, "", APPConfig.Type.String);
        appConfigService.init(CNF_KEY_SYNC_FORCE_SYNC_NOW, "false", APPConfig.Type.Boolean);
        appConfigService.init(CNF_KEY_SYNC_FORCE_START_TIME, "", APPConfig.Type.String);
        schedule("change_form_sync.sync", 60 * 1000) {
            this.doSync()
        }
    }

    fun getEnumStr(name: String, data: Map<Int, String>, idx: Int): String {
        return data[idx] ?: throw IllegalArgumentException("未知的枚举值：$name=$idx")
    }

    var lastRequestTime = 0L;

    private fun fetchApiData(_params: ApiReqParams, record: ChangeFormSyncRecord, apiUrl: String, delayCnf: Long): String {
        var retryCount = 0
        while (true) {
            val delay = delayCnf - (System.currentTimeMillis() - lastRequestTime)
            if (delay > 0) {
                this.logger.debug("request $apiUrl need delay $delay")
                Thread.sleep(delay)
            }
            lastRequestTime = System.currentTimeMillis()
            val c = HTTPHelper()
            val u = URIBuilder(apiUrl)
            val token = u.queryParams.find { it.name == "token" }?.value
            u.queryParams.removeIf { it.name == "token" }
            val requestUrl = u.toString()
            val params = HashMap<String, String>()
            if (_params.startTime != null) {
                params.put("startTime", SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(_params.startTime))
            }
            if (_params.endTime != null) {
                params.put("endTime", SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(_params.endTime))
            }
            if (_params.ticketID != null) {
                params.put("TicketID", _params.ticketID!!)
            }
            if (_params.produceNum != null) {
                params.put("ProduceNum", _params.produceNum!!)
            }
            if (_params.idRecordSN != null) {
                params.put("IdRecordSN", _params.idRecordSN!!)
            }
            if (_params.idGroupNo != null) {
                params.put("IDGroupNo", _params.idGroupNo!!)
            }
            if (_params.current != null) {
                params.put("current", _params.current.toString())
            }
            if (_params.size != null) {
                params.put("size", _params.size.toString())
            }
            val opts = HTTPHelper.Companion.Options()
            if (token != null) {
                opts.header("token", token)
            }
            this.logger.info("do request ${requestUrl} params ${JSONUtils.toString(params)}")
            val started = System.currentTimeMillis()
            val resp = c.get(requestUrl, params, HTTPHelper.Companion.RespBytes::class.java, opts)
            try {

                val body = resp.body.toString()
                record.apiUseTime += System.currentTimeMillis() - started
                record.apiDownBytes += body.toByteArray().size
                val data = JSONUtils.toObject(body, ApiResp::class.java)
                if (resp.response.code == 429 || (data.success == false && data.code == 5006)) {
                    retryCount += 10
                    logger.info("request '${requestUrl}' too many requests. retry in ${retryCount}s. code=${data.code}, msg=${data.message}")
                    if (retryCount >= 10)
                        Thread.sleep(10 * 1000L)
                    else
                        Thread.sleep(retryCount * 1000L)
                    continue
                }
                if (data.success == true && data.data != null) {
                    record.apiSuccRequestCount++
                } else {
                    record.apiFailRequestCount++
                    data.data = null
                    throw RuntimeException("fetch ${requestUrl} error: ${JSONUtils.toString(data)}")
                }
                return JSONUtils.toString(data.data!!)
            } finally {
                resp.response.closeQuietly();
            }
        }
    }

    private inline fun <reified T : ApiRespPage<*>, T2> fetchAllListData(name: String, params: ApiReqParams, record: ChangeFormSyncRecord, apiUrl: String, delayCnf: Long): MutableList<T2> {
        params.current = 1;
        params.size = 500;
        val data = mutableListOf<T2>()
        var retry = 0;
        while (true) {
            var raw = "";
            try {
                raw = fetchApiData(params, record, apiUrl, delayCnf)
            } catch (e: Exception) {
                retry++;
                if (retry >= 5) throw e;
                this.logger.warn("fetch data error ${retry}. retry in 10s. message=${e.message}")
                Thread.sleep(10)
                continue;
            }
            retry = 0;
            if (raw.length <= 200)
                this.logger.debug("response: ${raw}")
            else
                this.logger.debug("response: ${raw.substring(0, 200)}")
            val p = JSONUtils.toObject(raw, T::class.java) as ApiRespPage<*>
            record.apiListDataCount += p.records!!.size
            p.records!!.forEach {
                data.add(it as T2)
            }
            logger.info("fetch ${name} page data: page=${p.current}, total=${p.total}, size=${p.size}, num=${p.num}")
            if (data.size >= p.total || p.records!!.size < params.size!!) break
            params.current = params.current!! + 1;
        }
        return data;
    }

    fun doSyncChangeForm(record: ChangeFormSyncRecord, apiConfig: API_CONFIG): List<ChangeFormItem> {
        val params = ApiReqParams()
        params.startTime = record.syncTimeBegin
        params.endTime = record.syncTimeEnd
        val data = fetchAllListData<ApiChangeFormItemListData, ApiChangeFormItemListItem>("items1", params, record, apiConfig.itemUrl, apiConfig.delay)
        if (apiConfig.itemStatusTimeout > 0) {
            val needReSyncStatus = arrayOf(
                ChangeRequestStatus[0],
                ChangeRequestStatus[1],
                ChangeRequestStatus[2],
                ChangeRequestStatus[3],
                ChangeRequestStatus[4],
                ChangeRequestStatus[5],
                ChangeRequestStatus[6],
                ChangeRequestStatus[7],
                ChangeRequestStatus[8],
                ChangeRequestStatus[9]
            )
            var needReSyncChangeFormItems = this.changeFormItemService.findAll(
                QChangeFormItem.a.statusText.`in`(*needReSyncStatus)
                    .and(QChangeFormItem.a.createdAt.gt(Date(Date().time - apiConfig.itemStatusTimeout)))
            ).toList()
            needReSyncChangeFormItems = needReSyncChangeFormItems.filter { it1 -> data.find { it2 -> it2.tickID == it1.formId } == null }
            needReSyncChangeFormItems.forEachIndexed { idx, it ->
                this.logger.info("resync changeform_item ${idx}/${needReSyncChangeFormItems.size}: ${it}")
                params.startTime = Date(it.planBeginTime!!.time - 600 * 1000)
                params.endTime = Date(it.planBeginTime!!.time + 600 * 1000)
                params.ticketID = it.formId
                val data2 = fetchAllListData<ApiChangeFormItemListData, ApiChangeFormItemListItem>("items2", params, record, apiConfig.itemUrl, apiConfig.delay)
                val found = data2.find { it2 -> it2.tickID == it.formId }
                if (found != null) {
                    data.add(found)
                } else {
                    this.logger.warn("resync result not found change form item.")
                }
            }
        }
        val items = mutableListOf<ChangeFormItem>()
        data.forEach {
            val item = ChangeFormItem()
            if (StringUtils.isAllBlank(it.tickID)) {
                record.ignoreChangeFormCount++;
                this.logger.warn("tickId empty. ignore ${it}")
                return@forEach
            }
            if (StringUtils.isAllBlank(it.scheduledStartDate)) {
                record.ignoreChangeFormCount++;
                this.logger.warn("scheduledStartDate empty. ignore ${it}")
                return@forEach
            }
            if (StringUtils.isAllBlank(it.scheduledEndDate)) {
                record.ignoreChangeFormCount++;
                this.logger.warn("scheduledEndDate empty. ignore ${it}")
                return@forEach
            }
            if (StringUtils.isAllBlank(it.description)) {
                record.ignoreChangeFormCount++;
                this.logger.warn("description empty. ignore ${it}")
                return@forEach
            }
            item.formId = it.tickID!!
            item.planBeginTime = ExcelImportService.parseDate(it.scheduledStartDate!!)
            item.planEndTime = ExcelImportService.parseDate(it.scheduledEndDate!!)
            item.name = it.description!!
            item.changeType = getEnumStr("item.changeType", ChangeTypes, it.changeType ?: -1)
            item.changeType2 = it.changeType2
            item.team = it.asgrp
            item.descText = it.statusDescription
            item.statusText = getEnumStr("item.changeRequestStatus", ChangeRequestStatus, it.changeRequestStatus ?: -1)
            items.add(item)
        }
        return items
    }

    fun doSyncChangeFormHost(record: ChangeFormSyncRecord, apiConfig: API_CONFIG, items: List<ChangeFormItem>): List<ChangeFormHost> {
        val hosts = mutableMapOf<String, ChangeFormHost>()
        items.forEach { formItem ->
            var hostLineIdx = 0;
            val params = ApiReqParams()
            params.produceNum = formItem.formId
            val hosts1 = fetchAllListData<ApiChangeFormHost1ListData, ApiChangeFormHost1ListItem>("host1", params, record, apiConfig.host1Url, apiConfig.delay)

            hosts1.forEach { host ->
                if (hosts.containsKey(formItem.formId + ":" + host.idRecordSN)) {
                    this.logger.warn("host1 repeat. ignore ${formItem.formId} ${host}")
                    return@forEach
                }
                if (StringUtils.isAllBlank(host.idRecordSN)) {
                    this.logger.info("host1.idRecordSN is empty: ${host}")
                    record.ignoreChangeFormHostCount++;
                    return@forEach
                }
                if (StringUtils.isAllBlank(host.produceNum)) {
                    this.logger.info("host1.produceNum is empty: ${host}")
                    record.ignoreChangeFormHostCount++;
                    return@forEach
                }
                if (StringUtils.isAllBlank(host.idName)) {
                    this.logger.info("host1.idName is empty: ${host}")
                    record.ignoreChangeFormHostCount++;
                    return@forEach
                }
                if (StringUtils.isAllBlank(host.ticketID)) {
                    this.logger.info("host1.ticketID is empty: ${host}")
                    record.ignoreChangeFormHostCount++;
                    return@forEach
                }
                if (host.produceNum.indexOf(formItem.formId) == -1) {
                    this.logger.warn("host1.produceNum error. ignore ${formItem.formId} ${host}")
                    record.ignoreChangeFormHostCount++;
                    return@forEach
                }

                val data = ChangeFormHost()
                data.lineIndex = (hostLineIdx++).toString();
                data.applyId = host.ticketID
                data.sponsorTime = ExcelImportService.parseDate(host.applyTime)
                data.sponsorUser = host.userName
                data.checkUser = ""
                data.auditUser = ""
                data.team = ""
                data.workType = ""
                data.address = ""
                data.changeFormId = formItem.formId
                data.workRecord = ""
                data.sysName = ""
                data.idOpsName = host.idName
                data.idOpsNumber = host.idRecordSN
                data.envType = ""
                data.beginTime = ExcelImportService.parseDate(host.startTime)
                data.endTime = ExcelImportService.parseDate(host.endTime)
                data.useTime = ""
                data.sendUser = ""
                data.recycleUser = ""
                data.serverIP = host.ipAddress
                hosts[formItem.formId + ":" + data.idOpsNumber] = data
            }
            val hosts2 = fetchAllListData<ApiChangeFormHost2ListData, ApiChangeFormHost2ListItem>("host2", params, record, apiConfig.host2Url, apiConfig.delay)
            hosts2.forEach { host ->
                if (hosts.containsKey(formItem.formId + ":" + host.opsRecordSN)) {
                    this.logger.warn("host2 repeat. ignore ${formItem.formId} ${host}")
                    return@forEach
                }
                if (StringUtils.isAllBlank(host.opsRecordSN)) {
                    this.logger.info("host2.opsRecordSN is empty: ${host}")
                    record.ignoreChangeFormHostCount++;
                    return@forEach
                }
                if (StringUtils.isAllBlank(host.produceNum)) {
                    this.logger.info("host2.produceNum is empty: ${host}")
                    record.ignoreChangeFormHostCount++;
                    return@forEach
                }
                if (StringUtils.isAllBlank(host.opsName)) {
                    this.logger.info("host2.opsName is empty: ${host}")
                    record.ignoreChangeFormHostCount++;
                    return@forEach
                }
                if (StringUtils.isAllBlank(host.ticketID)) {
                    this.logger.info("host2.ticketID is empty: ${host}")
                    record.ignoreChangeFormHostCount++;
                    return@forEach
                }
                if (host.produceNum.indexOf(formItem.formId) == -1) {
                    this.logger.warn("host2.produceNum error. ignore ${formItem.formId} ${host}")
                    record.ignoreChangeFormHostCount++;
                    return@forEach
                }

                val data = ChangeFormHost()
                data.lineIndex = (hostLineIdx++).toString();
                data.applyId = host.ticketID
                data.sponsorTime = ExcelImportService.parseDate(host.applyTime)
                data.sponsorUser = host.applierName
                data.checkUser = ""
                data.auditUser = ""
                data.team = ""
                data.workType = ""
                data.address = ""
                data.changeFormId = formItem.formId
                data.workRecord = ""
                data.sysName = ""
                data.idOpsName = host.opsName
                data.idOpsNumber = host.opsRecordSN
                data.envType = ""
                data.beginTime = ExcelImportService.parseDate(host.startTime)
                data.endTime = ExcelImportService.parseDate(host.endTime)
                data.useTime = ""
                data.sendUser = ""
                data.recycleUser = ""
                hosts[formItem.formId + ":" + data.idOpsNumber] = data
            }
            val hosts3 = fetchAllListData<ApiChangeFormHost3ListData, ApiChangeFormHost3ListItem>("host3", params, record, apiConfig.host3Url, apiConfig.delay)
            hosts3.forEach { host3 ->
                if (StringUtils.isAllBlank(host3.sn)) {
                    this.logger.info("host3.sn is empty: ${host3}")
                    record.ignoreChangeFormHostCount++;
                    return@forEach
                }
                if (StringUtils.isAllBlank(host3.produceNum)) {
                    this.logger.info("host3.produceNum is empty: ${host3}")
                    record.ignoreChangeFormHostCount++;
                    return@forEach
                }
                if (StringUtils.isAllBlank(host3.idGroupIndex)) {
                    this.logger.info("host3.idGroupIndex is empty: ${host3}")
                    record.ignoreChangeFormHostCount++;
                    return@forEach
                }
                if (host3.produceNum.indexOf(formItem.formId) == -1) {
                    this.logger.warn("host3.produceNum error. ignore ${formItem.formId} ${host3}")
                    record.ignoreChangeFormHostCount++;
                    return@forEach
                }
                var params4 = ApiReqParams()
                params4.idGroupNo = host3.idGroupIndex
                val hosts4 = fetchAllListData<ApiChangeFormHost4ListData, ApiChangeFormHost4ListItem>("host4", params4, record, apiConfig.host4Url, apiConfig.delay)
                hosts4.forEach { host ->
                    if (hosts.containsKey(formItem.formId + ":" + host.idRecordSN)) {
                        this.logger.warn("host4 repeat. ignore ${formItem.formId} ${host}")
                        return@forEach
                    }
                    if (StringUtils.isAllBlank(host.idRecordSN)) {
                        this.logger.info("host4.idRecordSN is empty: ${host}")
                        record.ignoreChangeFormHostCount++;
                        return@forEach
                    }
                    if (StringUtils.isAllBlank(host.idUser)) {
                        this.logger.info("host4.idUser is empty: ${host}")
                        record.ignoreChangeFormHostCount++;
                        return@forEach
                    }
                    val data = ChangeFormHost()
                    data.lineIndex = (hostLineIdx++).toString();
                    data.applyId = host3.sn
                    data.sponsorTime = ExcelImportService.parseDate(host3.applyTime)
                    data.sponsorUser = host3.applierName
                    data.checkUser = ""
                    data.auditUser = ""
                    data.team = ""
                    data.workType = ""
                    data.address = ""
                    data.changeFormId = formItem.formId
                    data.workRecord = ""
                    data.sysName = host.idSystem
                    data.idOpsName = host.idUser
                    data.idOpsNumber = host.idRecordSN
                    data.envType = ""
                    data.beginTime = ExcelImportService.parseDate(host3.startTime)
                    data.endTime = ExcelImportService.parseDate(host3.endTime)
                    data.useTime = ""
                    data.sendUser = ""
                    data.recycleUser = ""
                    data.serverIP = host.ipAddress
                    hosts[formItem.formId + ":" + data.idOpsNumber] = data
                }
            }
        }
        return hosts.values.toList();
    }

    fun doSyncChangeFormCommand(record: ChangeFormSyncRecord, apiConfig: API_CONFIG, items: List<ChangeFormItem>): List<ChangeFormCommand> {
        val cmds = mutableListOf<ChangeFormCommand>()
        items.forEach { formItem ->
            val params = ApiReqParams()
            params.ticketID = formItem.formId
            val cmd = ChangeFormCommand()
            cmd.changeFormId = formItem.formId
            val data = fetchAllListData<ApiChangeFormCommandListData, ApiChangeFormCommandListItem>("commands", params, record, apiConfig.cmdUrl, apiConfig.delay)
            val cmdList = mutableListOf<String>()
            var minBeginTime: Date? = null
            var maxEndTime: Date? = null
            data.forEach { cmdForm ->
                if (cmdForm.startDate == null || cmdForm.endDate == null) {
                    this.logger.info("command startDate or endDate is null: ${cmdForm}")
                    record.ignoreChangeFormCommandCount++;
                    return@forEach
                }
                val b = ExcelImportService.parseDate(cmdForm.startDate!!)
                val e = ExcelImportService.parseDate(cmdForm.endDate!!)
                if (minBeginTime == null || b.time < (minBeginTime as Date).time) {
                    minBeginTime = b
                }
                if (maxEndTime == null || e.time > (maxEndTime as Date).time) {
                    maxEndTime = e
                }
                if (!StringUtils.isAllBlank(cmdForm.task)) cmdList.add(cmdForm.task!!)
                if (!StringUtils.isAllBlank(cmdForm.chgItem)) cmdList.add(cmdForm.chgItem!!)
                if (cmdList.size == 0) {
                    this.logger.warn("command task or chgItem is null: ${cmdForm}")
                    record.ignoreChangeFormCommandCount++;
                    return@forEach
                }
            }
            if (cmdList.size == 0) {
                this.logger.warn("changeform ${formItem} no command.");
                return@forEach
            }
            cmd.beginTime = minBeginTime
            cmd.endTime = maxEndTime
            cmd.command = cmdList.joinToString("\n")
            cmds.add(cmd)
        }
        return cmds
    }

    private fun doSync() {
        val apiConfig = JSONUtils.toObject(JSONUtils.toString(appConfigService.getMap(CNF_KEY_API_CONFIG)!!), API_CONFIG::class.java)
        val cron = appConfigService.getString(CNF_KEY_SYNC_CRON) ?: ""
        val forceStartTime = appConfigService.getString(CNF_KEY_SYNC_FORCE_START_TIME) ?: ""
        val forceSyncNow = appConfigService.getBoolean(CNF_KEY_SYNC_FORCE_SYNC_NOW) == true
        if (StringUtils.isEmpty(apiConfig.itemUrl) ||
            StringUtils.isEmpty(apiConfig.cmdUrl) ||
            StringUtils.isEmpty(apiConfig.host1Url) ||
            StringUtils.isEmpty(apiConfig.host2Url) ||
            StringUtils.isEmpty(apiConfig.host3Url) ||
            StringUtils.isEmpty(apiConfig.host4Url)
        ) {
            return
        }
        var lastExecuteTime = 0L
        val lastRecord = queryFactory.from(QChangeFormSyncRecord.a).where(QChangeFormSyncRecord.a.success.isTrue).limit(1).orderBy(OrderSpecifier(Order.DESC, QChangeFormSyncRecord.a.createdAt)).fetch().firstOrNull() as ChangeFormSyncRecord?
        if (lastRecord != null) {
            lastExecuteTime = lastRecord.createdAt!!.toInstant().toEpochMilli()
        }
        val newRecord = ChangeFormSyncRecord()
        newRecord.createdAt = Date()
        newRecord.syncTimeEnd = Date(System.currentTimeMillis() - 60 * 1000) // 减去一分钟
        if (lastRecord != null) {
            newRecord.syncTimeBegin = lastRecord.syncTimeEnd
        } else {
            newRecord.syncTimeBegin = Date(System.currentTimeMillis() - 24 * 3600 * 1000) // 减去24小时
        }
        if (!StringUtils.isAllEmpty(forceStartTime) && forceStartTime != "0") {
            newRecord.syncTimeBegin = ExcelImportService.parseDate(forceStartTime)
        }
        if (!forceSyncNow) {
            val nextExecTime = DateUtil.getCronNextTime(cron, lastExecuteTime)
            logger.debug("sync cron info: cron=${cron}, last=${lastExecuteTime}, next=$nextExecTime")
            if (nextExecTime == -1L) {
                logger.error("Cron is invalid: $cron")
            }
            if (nextExecTime <= 0 || nextExecTime > Date().toInstant().toEpochMilli()) {
                return
            }
        } else {
            appConfigService.setConfig(CNF_KEY_SYNC_FORCE_SYNC_NOW, false);
        }
        var timeStats = TimeStats()
        try {
            this.logger.info("sync started: ${BaseEntity.DEFAULT_DATE_FORMAT.format(newRecord.syncTimeBegin)} ${BaseEntity.DEFAULT_DATE_FORMAT.format(newRecord.syncTimeEnd)}")
            val items = doSyncChangeForm(newRecord, apiConfig)
            this.logger.info("get ${items.size} items")
            timeStats.point("get items")
            val hosts = doSyncChangeFormHost(newRecord, apiConfig, items)
            this.logger.info("get ${hosts.size} hosts")
            timeStats.point("get hosts")
            val cmds = doSyncChangeFormCommand(newRecord, apiConfig, items)
            this.logger.info("get ${cmds.size} cmds")
            timeStats.point("get commands")
            val locked = excelImportService.importLockRun("changeFormSyncService@syncSave", 24 * 3600) {
                this.logger.info("save start")
                timeStats.point("get lock")
                val newItems = mutableListOf<ChangeFormItem>()
                val excludeFields = arrayOf("id", "createdAt", "updatedAt", "bundle")
                runInTran {
                    var lastLogTime = 0L;
                    items.forEachIndexed { idx, formItem ->
                        val idx = idx + 1;
                        var item = this.changeFormItemService.findAll(QChangeFormItem.a.formId.eq(formItem.formId)).firstOrNull() ?: ChangeFormItem()
                        RestUtil.copyBean(formItem, item, excludeFields)
                        item.matchTime = null
                        item.statsTime = null
                        if (item.id == null) {
                            newRecord.addChangeFormCount++
                        } else {
                            newRecord.modifyChangeFormCount++
                        }
                        this.changeFormItemService.save(item)
                        newItems.add(item)
                        if (System.currentTimeMillis() - lastLogTime >= 10000 || idx == items.size - 1) {
                            this.logger.info("save items progress: ${idx}/${items.size} ${((idx * 10000.0 / items.size).toLong() / 100)}%")
                            lastLogTime = System.currentTimeMillis()
                        }
                    }
                    lastLogTime = 0L;
                    val hostsCache = mutableMapOf<String, MutableList<ChangeFormHost>>()
                    hosts.forEachIndexed { idx, formHost ->
                        val idx = idx + 1;
                        formHost.idOpsName = this.changeFormHostService.getUserIds(formHost.idOpsName!!).joinToString("|")
                        val itemHosts = hostsCache[formHost.changeFormId] ?: this.changeFormHostService.findAll(QChangeFormHost.a.changeFormId.eq(formHost.changeFormId)).toMutableList();
                        var host = itemHosts.find { it.idOpsNumber == formHost.idOpsNumber } ?: ChangeFormHost()
                        RestUtil.copyBean(formHost, host, excludeFields)
                        if (host.id == null) {
                            newRecord.addChangeFormHostCount++
                            itemHosts.add(host)
                        } else {
                            newRecord.modifyChangeFormHostCount++
                        }
                        this.changeFormHostService.save(host)
                        if (System.currentTimeMillis() - lastLogTime >= 10000 || idx == hosts.size - 1) {
                            this.logger.info("save hosts progress: ${idx}/${hosts.size} ${((idx * 10000.0 / hosts.size).toLong() / 100)}%")
                            lastLogTime = System.currentTimeMillis()
                        }
                        hostsCache[formHost.changeFormId] = itemHosts;
                    }
                    lastLogTime = 0L;
                    cmds.forEachIndexed { idx, formCmd ->
                        val idx = idx + 1;
                        var cmd = this.changeFormCommandService.findAll(QChangeFormCommand.a.changeFormId.eq(formCmd.changeFormId)).firstOrNull() ?: ChangeFormCommand()
                        RestUtil.copyBean(formCmd, cmd, excludeFields)
                        if (cmd.id == null) {
                            newRecord.addChangeFormCommandCount++
                        } else {
                            newRecord.modifyChangeFormCommandCount++
                        }
                        this.changeFormCommandService.save(cmd)
                        if (System.currentTimeMillis() - lastLogTime >= 10000 || idx == cmds.size - 1) {
                            this.logger.info("save cmds progress: ${idx}/${cmds.size} ${((idx * 10000.0 / cmds.size).toLong() / 100)}%")
                            lastLogTime = System.currentTimeMillis()
                        }
                    }
                    this.changeFormItemService.addToMatchQueue(*(newItems.map { it.formId }.toTypedArray()))
                }
                this.logger.info("save done")
                timeStats.point("save data")
                newItems.forEach {
                    this.changeFormItemService.lazyUpdateStats(it.id!!);
                }
                newRecord.success = true;
            }
            if (!locked) throw java.lang.RuntimeException("import lock fail.")

            if (!StringUtils.isAllEmpty(forceStartTime) && forceStartTime != "0") {
                appConfigService.setConfig(CNF_KEY_SYNC_FORCE_START_TIME, "")
            }
            this.logger.info("sync succ. ${newRecord}. time stats: ${timeStats}")
        } catch (e: Exception) {
            newRecord.success = false;
            newRecord.errmsg = e.message
            if (newRecord.errmsg != null && newRecord.errmsg!!.length >= 200) {
                newRecord.errmsg = newRecord.errmsg!!.substring(0, 200)
            }
            this.logger.error("sync fail. ${newRecord}. time stats: ${timeStats}", e)
        }
        runInTran {
            newRecord.updatedAt = Date()
            this.changeFormSyncRecordService.save(newRecord)
        }
    }
}

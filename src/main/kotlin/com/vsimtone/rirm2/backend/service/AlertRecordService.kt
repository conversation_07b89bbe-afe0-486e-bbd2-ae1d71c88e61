package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.entity.AlertRecord
import com.vsimtone.rirm2.backend.entity.QAlertRecord
import com.vsimtone.rirm2.backend.repository.AlertRecordRepository
import com.vsimtone.rirm2.backend.services.BaseService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class AlertRecordService : BaseCrudService<AlertRecord, AlertRecordRepository>(QAlertRecord.a) {
}

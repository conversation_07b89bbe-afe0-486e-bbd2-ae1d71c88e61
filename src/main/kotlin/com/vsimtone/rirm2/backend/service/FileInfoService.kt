package com.vsimtone.rirm2.backend.service


import com.github.vfss3.S3FileSystemConfigBuilder
import com.vsimtone.rirm2.backend.entity.APPConfig
import com.vsimtone.rirm2.backend.entity.FileInfo
import com.vsimtone.rirm2.backend.entity.QFileInfo
import com.vsimtone.rirm2.backend.repository.FileInfoRepository
import com.vsimtone.rirm2.backend.utils.AppUtils
import com.vsimtone.rirm2.backend.utils.CryptUtils
import jcifs.CIFSContext
import jcifs.config.PropertyConfiguration
import net.idauto.oss.jcifsng.vfs2.provider.SmbFileSystemConfigBuilder
import okhttp3.internal.closeQuietly
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.io.FilenameUtils
import org.apache.commons.io.IOUtils
import org.apache.commons.net.util.TrustManagerUtils
import org.apache.commons.vfs2.FileObject
import org.apache.commons.vfs2.FileSystemManager
import org.apache.commons.vfs2.FileSystemOptions
import org.apache.commons.vfs2.auth.StaticUserAuthenticator
import org.apache.commons.vfs2.impl.DefaultFileSystemConfigBuilder
import org.apache.commons.vfs2.impl.StandardFileSystemManager
import org.apache.commons.vfs2.provider.ftp.FtpFileSystemConfigBuilder
import org.apache.commons.vfs2.provider.ftps.FtpsFileSystemConfigBuilder
import org.apache.commons.vfs2.provider.sftp.SftpFileSystemConfigBuilder
import org.apache.commons.vfs2.provider.zip.ZipFileSystemConfigBuilder
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import java.io.ByteArrayInputStream
import java.io.File
import java.io.InputStream
import java.io.OutputStream
import java.net.URI
import java.nio.charset.Charset
import java.text.SimpleDateFormat
import java.time.Duration
import java.util.*
import java.util.zip.ZipFile

@Service
class FileInfoService : BaseCrudService<FileInfo, FileInfoRepository>(QFileInfo.a) {

    companion object {
        val FOLDER_PATH_DATE_FORMAT get() = SimpleDateFormat("YYYY/MM/dd")
        const val CNF_FILE_DOWNLOAD_SPEED = "core.file.download.speed"
    }

    @Value("\${fs.vfs_base_url}")
    lateinit var vfsBaseUrl: String

    lateinit var vfsBaseFile: FileObject

    lateinit var vfsFileSystemOptions: FileSystemOptions

    @Autowired
    lateinit var folderService: FileFolderService

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var redisService: RedisService

    lateinit var vfsManager: FileSystemManager

    init {
        readyInvokeOrder = READY_INVOKE_HIGH;
    }

    fun getDefaultVFSOptions(url: String): FileSystemOptions {
        val options = FileSystemOptions();

        if (url.startsWith("s3://", true)) {
            S3FileSystemConfigBuilder.getInstance().setUseHttps(options, false);
        }
        if (url.startsWith("zip:file://", true)) {
            val tryNames = mutableListOf(
                Charsets.UTF_8,
                Charset.forName("GB2312"),
                Charset.forName("GBK"),
                Charsets.ISO_8859_1,
                Charsets.UTF_16
            )
            val path = url.substring(11).replace(Regex("\\!.*$"), "");
            if (File(path).exists()) {
                for (name in tryNames) {
                    try {
                        val f = ZipFile(path, name)
                        f.use {
                            val entries = f.entries()
                            while (entries.hasMoreElements()) {
                                val entry = entries.nextElement()
                                if (AppUtils.messyCodeRate(entry.name) > 0.4)
                                    throw RuntimeException("Entry name is wrong: ${entry.name}")
                            }
                        }
                        ZipFileSystemConfigBuilder.getInstance().setCharset(options, name);
                        return options;
                    } catch (e: Exception) {
                        this.logger.info("${path} is not ${name}: " + e.message)
                    }
                }
            }
        }
        if (url.startsWith("ftp://", true) || url.startsWith("ftps://", true)) {
            FtpFileSystemConfigBuilder.getInstance().setUserDirIsRoot(options, false);
            FtpFileSystemConfigBuilder.getInstance().setConnectTimeout(options, Duration.ofSeconds(30));
            FtpFileSystemConfigBuilder.getInstance().setAutodetectUtf8(options, true);
            FtpFileSystemConfigBuilder.getInstance().setPassiveMode(options, true);
        }
        if (url.startsWith("ftps://", true)) {
            FtpsFileSystemConfigBuilder.getInstance()
                .setTrustManager(options, TrustManagerUtils.getAcceptAllTrustManager());
        }
        if (url.startsWith("sftp://", true)) {
            SftpFileSystemConfigBuilder.getInstance().setUserDirIsRoot(options, false);
            SftpFileSystemConfigBuilder.getInstance().setConnectTimeout(options, Duration.ofSeconds(30));
        }
        if (url.startsWith("smb://", true)) {
            val uri = URI.create(url)
            val userInfos = uri.userInfo.split(":")

            // authentication
            val auth = StaticUserAuthenticator(null, userInfos.first(), userInfos.last())

            // jcifs configuration
            val jcifsProperties = Properties()
            val jcifsContext: CIFSContext = jcifs.context.BaseContext(PropertyConfiguration(jcifsProperties))

            // pass in both to VFS
            DefaultFileSystemConfigBuilder.getInstance().setUserAuthenticator(options, auth)
            SmbFileSystemConfigBuilder.getInstance().setCIFSContext(options, jcifsContext)
        }

        return options;

    }

    override fun onReady() {
        appConfigService.init(CNF_FILE_DOWNLOAD_SPEED, 1024 * 1024 * 100, APPConfig.Type.Long)
        vfsFileSystemOptions = this.getDefaultVFSOptions(vfsBaseUrl);

        vfsManager = StandardFileSystemManager();
        (vfsManager as StandardFileSystemManager).init();
        if (!vfsBaseUrl.endsWith("/"))
            vfsBaseUrl += "/"
        vfsBaseFile = vfsManager.resolveFile(vfsBaseUrl, vfsFileSystemOptions);
        if (!vfsBaseFile.exists())
            vfsBaseFile.createFolder()
        if (!vfsBaseFile.isFolder)
            throw RuntimeException("${vfsBaseFile.fileSystem} is not folder");
        this.logger.info("Use ${vfsBaseFile.fileSystem.javaClass.simpleName} ${vfsBaseFile.publicURIString}")
    }

    fun getVFSFile(_path: String): FileObject {
        val path = _path;
        return vfsManager.resolveFile(vfsBaseFile, path);
    }

    fun getTokenPrefix(): String {
        var year = (SimpleDateFormat("YYYY").format(Date()).toInt() - 2022).toString(16);
        if (year.length == 1)
            year = "0$year"
        return year + SimpleDateFormat("MMdd").format(Date())
    }

    @Synchronized
    @Transactional
    fun upload(name: String, type: String, size: Long, _folder: Long? = null, getIS: () -> InputStream): FileInfo {
        val date = Date()
        var ext = FilenameUtils.getExtension(name)
        if (ext.isEmpty()) ext = "bin"
        val sha256sum = getIS().use { DigestUtils.sha256Hex(it) }
        val exists = findOne(QFileInfo.a.sha256sum.eq(sha256sum))
        if (exists != null) {
            val realFile = getVFSFile(exists.path);
            if (!realFile.exists()) {
                logger.info("File info exists, but s3 file lost, retry save");
                getIS().use {
                    val inputIS = it;
                    realFile.use {
                        IOUtils.copy(inputIS, it.content.outputStream)
                    }
                }
            }
            if (_folder != null) {
                exists.folder = folderService.repo.findById(_folder).get()
                repo.save(exists)
            }
            return exists;
        }

        val token = getTokenPrefix() + CryptUtils.sha256hex(UUID.randomUUID().toString()) + "." + ext;
        val folder = FOLDER_PATH_DATE_FORMAT.format(date)
        val path = "$folder/$token"

        val pathArr = path.split("/")

        pathArr.forEachIndexed { i: Int, s: String ->
            if (i < pathArr.size - 1) {
                val d = pathArr.subList(0, i).joinToString("/")
                val dirObj = getVFSFile(d)
                if (!dirObj.exists())
                    dirObj.createFolder();
            }
        }

        val fileInfo = FileInfo()
        fileInfo.sha256sum = sha256sum
        fileInfo.name = name
        fileInfo.type = type
        fileInfo.size = size
        fileInfo.path = path
        fileInfo.token = token
        if (_folder != null) {
            fileInfo.folder = folderService.repo.findById(_folder).get()
        }
        repo.save(fileInfo)
        getIS().use {
            val inputIS = it;
            getVFSFile(path).use {
                IOUtils.copy(inputIS, it.content.outputStream)
            }
        }
        return fileInfo
    }

    fun upload(source: MultipartFile, folder: Long? = null): FileInfo {
        return upload(source.originalFilename!!, source.contentType!!, source.size, folder) { source.inputStream }
    }

    fun upload(name: String, type: String, data: ByteArray, folder: Long? = null): FileInfo {
        return upload(name, type, data.size.toLong(), folder) { ByteArrayInputStream(data) }
    }


    fun getInputStream(path: String): InputStream? {
        val vfsFile = getVFSFile(path)
        if (vfsFile.exists()) {
            val vfsContent = vfsFile.content
            val oriIs = vfsContent.inputStream;
            return object : InputStream() {
                override fun read(): Int {
                    return oriIs.read()
                }

                override fun close() {
                    oriIs.closeQuietly()
                    vfsContent.closeQuietly()
                    vfsFile.closeQuietly()
                }

                override fun skip(n: Long): Long {
                    return oriIs.skip(n)
                }

                override fun available(): Int {
                    return oriIs.available()
                }

                override fun mark(readlimit: Int) {
                    return oriIs.mark(readlimit)
                }

                override fun reset() {
                    return oriIs.reset()
                }

                override fun markSupported(): Boolean {
                    return oriIs.markSupported()
                }

                override fun read(b: ByteArray): Int {
                    return oriIs.read(b)
                }

                override fun read(b: ByteArray, off: Int, len: Int): Int {
                    return oriIs.read(b, off, len)
                }

                fun readAllBytes(): ByteArray {
                    return IOUtils.toByteArray(oriIs)
                }

                fun readNBytes(len: Int): ByteArray {
                    val buffer = ByteArray(len)
                    val bytesRead = IOUtils.read(oriIs, buffer, 0, len)
                    return if (bytesRead == len) buffer else buffer.copyOf(bytesRead)
                }

                fun readNBytes(b: ByteArray?, off: Int, len: Int): Int {
                    return IOUtils.read(oriIs, b, off, len)
                }

                fun transferTo(out: OutputStream?): Long {
                    return IOUtils.copyLarge(oriIs, out)
                }
            }
        }
        return null;
    }

    fun findByToken(token: String): FileInfo? {
        return repo.findByToken(token)
    }

    @Transactional
    override fun delete(f: FileInfo) {
        repo.delete(f)
        getVFSFile(f.path).delete()
        logger.info("Delete ${f}")
    }

}
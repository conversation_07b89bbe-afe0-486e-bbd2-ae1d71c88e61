package com.vsimtone.rirm2.backend.service

import com.querydsl.core.BooleanBuilder
import com.querydsl.core.types.dsl.BooleanExpression
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.entity.*
import com.vsimtone.rirm2.backend.repository.ITXEventQueueRepository
import com.vsimtone.rirm2.backend.services.BaseService
import com.vsimtone.rirm2.backend.utils.DateUtil
import com.vsimtone.rirm2.backend.utils.JSONUtils
import com.vsimtone.rirm2.backend.utils.TimeStats
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.*
import kotlin.text.RegexOption

@Service
class ITXEventQueueService : BaseService() {

    companion object {
        const val CNF_KEY_UNIX_SCREEN_PS1_REGEX = "core.unix_screen.ps1_regex";
        const val CNF_INTELLINX_UNIX_SCREEN_WIDTH = "core.intellinx.unix_screen.width"
        const val CNF_EVENT_QUEUE_DELETE_BAD_SESSION_TIMEOUT = "core.event_queue.auto_delete_bad_session_time"
    }

    @Autowired
    lateinit var repo: ITXEventQueueRepository

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var itxSessionService: ITXSessionService

    @Autowired
    lateinit var itxCommandService: ITXCommandService

    @Autowired
    lateinit var commandRuleService: CommandRuleService

    @Autowired
    lateinit var alertRuleService: AlertRuleService

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    lateinit var excelImportService: ExcelImportService

    @Autowired
    lateinit var changeFormCommandService: ChangeFormCommandService

    @Autowired
    lateinit var changeFormItemService: ChangeFormItemService

    @Autowired
    lateinit var changeFormHostService: ChangeFormHostService


    override fun onReady() {
        appConfigService.init(CNF_KEY_UNIX_SCREEN_PS1_REGEX, JSONUtils.toString(listOf("\\[(.*?)@(.*?)\\s(.*?)\\][\\\$\\#]\\s")), APPConfig.Type.JsonArray);
        appConfigService.init(CNF_INTELLINX_UNIX_SCREEN_WIDTH, "80", APPConfig.Type.Integer);
        appConfigService.init(CNF_EVENT_QUEUE_DELETE_BAD_SESSION_TIMEOUT, DateUtil.day(30), APPConfig.Type.Long);
        schedule("process_itx_event_queue", 10 * 1000) {
            processSchedule();
        }
        val started = System.currentTimeMillis()
        schedule("process_itx_event_queue_timeout", 24 * 3600 * 1000) { // 每天一次
            if (System.currentTimeMillis() - started >= 3600 * 1000) { // 启动1小时内不清理
                processTimeoutSchedule();
            }
        }
    }

    fun getUnixShellPs1Regex(): List<Regex> {
        return appConfigService.getList(CNF_KEY_UNIX_SCREEN_PS1_REGEX)!!.map { Regex(it as String) }
    }


    fun arrEquals(a: List<String>, b: List<String>): Boolean {
        if (a.size != b.size) return false
        a.forEachIndexed { i, v ->
            if (v != b[i]) return false
        }
        return true;
    }

    fun regexMatch(_cmd: String, flags: List<String>, _regex: String, ignoreException: Boolean = true): String? {
        try {
            var regex = _regex
            var regexOpts = mutableSetOf<RegexOption>()
            if (_cmd.trim().isEmpty()) return null
            if (flags.contains(CommandRule.RegexFlag.MATCH_FULL.name) || flags.contains(CommandRule.RegexFlag.MATCH_BEGIN.name)) {
                if (!regex.startsWith("^")) regex = "^${regex}"
            }
            if (flags.contains(CommandRule.RegexFlag.MATCH_FULL.name) || flags.contains(CommandRule.RegexFlag.MATCH_END.name)) {
                if (!regex.endsWith("$")) regex = "${regex}$"
            }
            if (flags.contains(CommandRule.RegexFlag.IGNORE_CASE.name)) {
                regexOpts.add(RegexOption.IGNORE_CASE)
            }
            var cmdArr = _cmd.split(Regex("\\s+"), 2);
            var cmd = mutableListOf<String>()
            if (flags.contains(CommandRule.RegexFlag.AREA_NAME.name)) {
                cmd.add(cmdArr[0])
            }
            if (flags.contains(CommandRule.RegexFlag.AREA_ARGS.name) && cmdArr.size == 2) {
                cmd.add(cmdArr[1])
            }
            if (cmd.isEmpty()) cmd.addAll(cmdArr)
            var r = Regex(regex, regexOpts)
            var matchValue = r.find(cmd.joinToString(" "))?.value
            return matchValue
        } catch (e: Exception) {
            if (!ignoreException) throw e;
            this.logger.error("regex match exception. cmd=${_cmd}, flags=${flags.joinToString(",")}, regex=${_regex}", e)
            return null
        }
    }

    fun newCommands(commandRules: List<CommandRule>, alertRules: List<AlertRule>, cmds: List<ITXCommand>) {
        cmds.forEach {
            val cmd = it;
            if (cmd.rule != null) {
                itxCommandService.repo.save(it);
                return@forEach;
            }
            var hitRule: CommandRule? = null;
            var hitDetails = mutableMapOf<String, String>()
            commandRules.forEach { rule ->
                rule.regex.forEach { r ->
                    if (hitRule == rule) return@forEach
                    val matchValue = regexMatch(cmd.cmd ?: "", rule.regexFlags, r)
                    if (matchValue != null) {
                        if ((hitRule == null || rule.alertLevel!!.ordinal > hitRule!!.alertLevel!!.ordinal)) {
                            hitRule = rule;
                            hitDetails["match_regex"] = r;
                            hitDetails["match_flags"] = rule.regexFlags.joinToString(",");
                            hitDetails["match_value"] = matchValue;
                        }
                    }
                }
            }
            cmd.rule = hitRule;
            cmd.ruleMatchInfo = hitDetails;
            itxCommandService.repo.save(it);
        }
        cmds.forEach {
            val cmd = it;
            alertRules.forEach {
                alertRuleService.doAlert(cmd, it)
            }
        }

        if (!cmds.isEmpty()) {
            val minTime = cmds.minBy { it.execTime!!.time }.execTime
            val maxTime = cmds.maxBy { it.execTime!!.time }.execTime
            val q = BooleanBuilder()
                .and(QITXCommand.a.changeFormItem.isNotNull)
                .andAnyOf(
                    QChangeFormHost.a.beginTime.lt(minTime).and(QChangeFormHost.a.endTime.gt(minTime)),
                    QChangeFormHost.a.beginTime.lt(maxTime).and(QChangeFormHost.a.endTime.gt(maxTime))
                )
            val p = this.changeFormHostService.findAll(q, PageAttr(PageAttr.FIRST_NUM, 1000))
            if (p.content.isEmpty()) return
            val formIds = p.content.filter { host ->
                cmds.find { it.execTime!!.time >= host.beginTime!!.time && it.execTime!!.time <= host.endTime!!.time } != null
            }.map { it.changeFormId }.toSet()
            this.logger.info("repeat match. hosts=${p.content.size}, formIds=${formIds.size}, query=[${q}]")
            this.changeFormItemService.addToMatchQueue(*formIds.toTypedArray())
        }
    }

    fun processUnixScreen(current: ITXEventQueue, prev: List<String>?, curr: List<String>): List<String> {
        try {
            val unixScreenWidth = appConfigService.getInteger(CNF_INTELLINX_UNIX_SCREEN_WIDTH)!!
            val unixScreenPs1Regex = getUnixShellPs1Regex()
            var body = curr;
            if (curr.isEmpty()) return listOf();
            if (prev != null && prev.isNotEmpty()) {
                val hitIndex = mutableListOf<Int>()
                prev.forEachIndexed { idx, it ->
                    if (it == curr.first()) hitIndex.add(idx)
                }
                val i = hitIndex.find {
                    (prev.size - it) <= curr.size && arrEquals(prev.subList(it, prev.size), curr.subList(0, prev.size - it))
                } ?: -1
                if (i != -1) {
                    if (prev.size - i >= curr.size) return emptyList();
                    body = curr.subList(prev.size - i, curr.size)
                }
            }
            val newBody = mutableListOf<String>()
            var lineBuf = StringBuffer();
            var i = 0;
            while (i < body.size) {
                var line = body[i]
                var newLine = true;
                if (line.endsWith("\\")) {
                    line = line.substring(0, line.length - 1)
                    newLine = false;
                } else if (line.length == unixScreenWidth) {
                    newLine = false;
                }
                lineBuf.append(line);
                if (newLine || i == body.size - 1) {
                    newBody.add(lineBuf.toString())
                    lineBuf = StringBuffer();
                }
                i++;
            }
            val cmds = mutableListOf<String>()
            newBody.forEach {
                val text = it
                unixScreenPs1Regex.forEach {
                    val match = it.findAll(text).lastOrNull();
                    if (match != null) {
                        val idx = match.range.last + 1
                        if (idx < text.length - 1) {
                            cmds.add(text.substring(idx))
                        }
                    }
                }
            }
            return cmds;
        } catch (e: Exception) {
            throw RuntimeException("${current} process unix screen", e)
        }
    }

    fun getEventQueues(sessionId: String): MutableList<ITXEventQueue> {
        return repo.findAll(QITXEventQueue.a.sessionId.eq(sessionId), PageAttr(PageAttr.FIRST_NUM, 1000, arrayListOf("asc_id")).get()).content.toMutableList()
    }

    fun processQueue(commandRules: List<CommandRule>, alertRules: List<AlertRule>, sessionId: String): List<ITXCommand> {
        var timeStats = TimeStats();
        val itxSession = itxSessionService.repo.findAll(QITXSession.a.sessionId.eq(sessionId)).firstOrNull() ?: ITXSession()
        if (itxSession.id != null) {
            var delCnt = 0;
            while (true) {
                val evtQueues = getEventQueues(sessionId)
                if (evtQueues.isEmpty()) break;
                evtQueues.forEach { evt ->
                    repo.delete(evt);
                    delCnt++;
                }
                repo.flush()
            }
            logger.warn("ignore repeat itx session: ${itxSession.id} ${itxSession.sessionId}, delete ${delCnt} command.")
            return emptyList();
        }
        itxSession.sessionId = sessionId
        timeStats.point("t0");
        val cmdList = mutableListOf<ITXCommand>()
        try {
            var prev: ITXEventQueue? = null;
            var queueCount = 0;
            while (true) {
                timeStats.reset();
                val evtQueues = getEventQueues(sessionId)
                if (evtQueues.isEmpty()) break;
                timeStats.point("t1");
                while (true) {
                    if (evtQueues.isEmpty()) break;

                    timeStats.reset();
                    val current = evtQueues.first();
                    evtQueues.removeFirst();
                    repo.delete(current);
                    queueCount++;
                    timeStats.point("t2");

                    if (StringUtils.isEmpty(itxSession.clientIP) && !StringUtils.isEmpty(current.clientIP)) itxSession.clientIP = current.clientIP

                    if (StringUtils.isEmpty(itxSession.serverIP) && !StringUtils.isEmpty(current.serverIP)) itxSession.serverIP = current.serverIP

                    if (itxSession.type == null && current.serverType != null) itxSession.type = current.serverType

                    if (StringUtils.isEmpty(itxSession.loginUser) && !StringUtils.isEmpty(current.loginUser)) {
                        val ss = current.loginUser!!.split(">|<")
                        if (ss.size == 2) {
                            itxSession.loginUser = ss[0]
                            itxSession.realUser = ss[1]
                        } else {
                            itxSession.loginUser = ss[0]
                        }
                    }

                    if (current.type == ITXEventQueue.Companion.Type.SessionEnd) {
                        itxSession.endTime = Date(current.auditTime!!.toLong());
                        continue;
                    }
                    if (current.type == ITXEventQueue.Companion.Type.SessionStart) {
                        itxSession.beginTime = Date(current.auditTime?.toLong() ?: 0);
                        itxSessionService.repo.save(itxSession);
                        continue;
                    }
                    timeStats.point("t3");
                    if (current.type == ITXEventQueue.Companion.Type.UnixScreen) {
                        if (itxSession.type == null) itxSession.type = ITXSession.Type.Linux
                        processUnixScreen(current, prev?.body?.split("\n"), current.body!!.split("\n")).forEach {
                            val auditTime = Date(current.auditTime!!.toLong())
                            val cmd = ITXCommand()
                            cmd.cmd = it
                            if (current.replayInfo != null) cmd.replayInfo = current.replayInfo
                            else if (current.eventSEQ > 0) cmd.replayInfo = "intellinx_event_seq:" + current.eventSEQ
                            if (itxSession.replayInfo == null && current.replayInfo != null) itxSession.replayInfo = current.replayInfo

                            cmd.session = itxSession
                            cmd.loginUser = itxSession.loginUser
                            cmd.execTime = auditTime
                            val cmdId = this.itxCommandService.getCmdId(cmd)
                            if (cmdList.find { it.cmdId == cmdId } != null) {
                                logger.warn("repeat unix screen command: ${itxSession.sessionId}, time=${auditTime.time}, cmd=${it}, cmd_id=${cmdId}")
                                return@forEach;
                            }
                            cmd.cmdId = cmdId
                            cmdList.add(cmd);
                        }
                    }
                    timeStats.point("t4");

                    if (current.type == ITXEventQueue.Companion.Type.UnixCommand) {
                        if (itxSession.type == null) itxSession.type = ITXSession.Type.Linux
                        val auditTime = Date(current.auditTime!!.toLong())
                        val cmd = ITXCommand()
                        cmd.cmd = current.body
                        if (current.replayInfo != null) cmd.replayInfo = current.replayInfo
                        else if (current.eventSEQ > 0) cmd.replayInfo = "intellinx_event_seq:" + current.eventSEQ
                        if (itxSession.replayInfo == null && cmd.replayInfo != null) itxSession.replayInfo = cmd.replayInfo

                        cmd.session = itxSession
                        cmd.loginUser = itxSession.loginUser
                        cmd.execTime = auditTime
                        val cmdId = this.itxCommandService.getCmdId(cmd)
                        if (cmdList.find { it.cmdId == cmdId } != null) {
                            logger.warn("repeat unix command: ${itxSession.sessionId}, time=${auditTime.time}, cmd=${current.body}, cmd_id=${cmdId}")
                            continue;
                        }
                        cmd.cmdId = cmdId
                        cmdList.add(cmd);
                    }
                    if (current.type == ITXEventQueue.Companion.Type.BOCCommand) {
                        if (itxSession.type == null) itxSession.type = ITXSession.Type.BOC
                        val data = current.body!!.split(Regex("\\|"), 3);
                        if (data.size < 3) throw RuntimeException("${current} body is invalid.");
                        val ruleId = data[0];
                        val name = data[1];
                        val cmdText = data[2];
                        val auditTime = Date(current.auditTime!!.toLong())
                        val cmd = ITXCommand();
                        cmd.cmdDesc = name;
                        cmd.ruleCode = ruleId;
                        cmd.rule = commandRuleService.repo.findByRuleCode(ruleId);
                        cmd.cmd = cmdText;
                        cmd.replayInfo = "intellinx_event_seq:" + current.eventSEQ;
                        if (itxSession.replayInfo == null && cmd.replayInfo != null) itxSession.replayInfo = cmd.replayInfo
                        cmd.session = itxSession
                        cmd.loginUser = itxSession.loginUser
                        cmd.execTime = Date(current.auditTime!!.toLong())
                        val cmdId = this.itxCommandService.getCmdId(cmd)
                        if (cmdList.find { it.cmdId == cmdId } != null) {
                            logger.warn("repeat boc command: ${itxSession.sessionId}, time=${auditTime.time}, cmd=${cmdText}, cmd_id=${cmdId}")
                            continue;
                        }
                        cmd.cmdId = cmdId
                        cmdList.add(cmd);
                    }
                    prev = current;
                    timeStats.point("t5");
                }
            }
            timeStats.reset();
            itxSession.commandCount = cmdList.size
            itxSessionService.repo.save(itxSession);
            newCommands(commandRules, alertRules, cmdList);
            timeStats.point("t6");
            logger.info("Process itx session '${sessionId}' done, ${queueCount} queue, ${cmdList.size} command. times: ${timeStats}")
        } catch (e: Exception) {
            throw RuntimeException("Process itx session '${sessionId}' error", e);
        }
        return cmdList;
    }

    private fun processSchedule() {
        val c1 = repo.count()
        while (!shutdowning) {
            val items = repo.findAll(QITXEventQueue.a.type.eq(ITXEventQueue.Companion.Type.SessionEnd), PageAttr(PageAttr.FIRST_NUM, 100, null).get());
            if (items.isEmpty) break;
            val locked = excelImportService.importLockRun("itxEventQueue@process", 60) {
                runInTran {
                    val commandRules = commandRuleService.repo.findAll(QCommandRule.a.enabled.isTrue).toList();
                    val alertRules = alertRuleService.repo.findAll(QAlertRule.a.enabled.isTrue).toList();
                    items.forEach {
                        processQueue(commandRules, alertRules, it.sessionId);
                    }
                }
            }
            if (!locked)
                this.logger.info("process delay. import lock fail.")
            else {
                Thread.sleep(1000L * 3) // 等待3秒释放锁
            }
        }
        val c2 = repo.count()
        if (c1 != c2) {
            this.logger.info("event queue free count ${c1} -> ${c2}")
        }
    }

    private fun processTimeoutSchedule() {
        runInTran {
            val timeout = this.appConfigService.getLong(CNF_EVENT_QUEUE_DELETE_BAD_SESSION_TIMEOUT)!!
            while (true) {
                val filter = QITXEventQueue.a.type.ne(ITXEventQueue.Companion.Type.SessionEnd).and(QITXEventQueue.a.auditTime.lt(DateUtil.now(0 - timeout)))
                val evt = repo.findAll(filter, PageAttr(PageAttr.FIRST_NUM, 1).get()).firstOrNull() ?: break;
                repo.findAll(QITXEventQueue.a.sessionId.eq(evt.sessionId)).forEach {
                    repo.delete(it);
                    logger.warn("delete bad itx event queue: ${evt}.");
                }
                repo.flush();
            }
        }
    }


}
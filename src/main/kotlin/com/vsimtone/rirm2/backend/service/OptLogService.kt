package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.entity.BaseEntity
import com.vsimtone.rirm2.backend.entity.OptLog
import com.vsimtone.rirm2.backend.entity.QOptLog
import com.vsimtone.rirm2.backend.repository.OptLogRepository
import com.vsimtone.rirm2.backend.utils.AppUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import java.util.*
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.TimeUnit
import kotlin.collections.forEach
import kotlin.collections.isNotEmpty
import kotlin.collections.joinToString
import kotlin.collections.set

@Service
class OptLogService : BaseCrudService<OptLog, OptLogRepository>(QOptLog.a) {

    var logCache = ArrayBlockingQueue<OptLog>(1024000)

    @Scheduled(fixedDelay = 10 * 1000L)
    fun saveLogCache() {
        if (!ready) return
        var i = 0;
        var logs = mutableListOf<OptLog>()
        while (i++ <= 300) {
            val log = logCache.poll(1, TimeUnit.SECONDS) ?: break
            logs.add(log)
        }
        if (logs.isNotEmpty())
            runInTran {
                logs.forEach {
                    repo.save(it)
                }
            }
    }

    fun addOptLog(
        user: String?,
        type: String,
        moduleName: String,
        objName: String,
        _details: Any? = null
    ) {
        val ra = RequestContextHolder.getRequestAttributes();
        val details = mutableMapOf<String, Any?>()
        if (ra != null) {
            val req = (ra as ServletRequestAttributes).request;
            details.put("请求地址", AppUtils.getURI(req));
            details.put("请求来源IP", AppUtils.getClientIpAddress(req));
            details.put("请求浏览器UA", req.getHeader("user-agent"));
        }
        if (_details != null) {
            val dataIds = mutableListOf<String>()
            val dataMsgs = mutableListOf<String>()
            val msgs = mutableListOf<String>()
            fun processData(data: Any?) {
                if (data == null) return;
                if (data is Map<*, *>) {
                    details.putAll(data as Map<out String, Any?>);
                    return;
                }
                if (data is List<*>) {
                    data.forEach { processData(it) }
                    return;
                }
                if (data is Array<*>) {
                    data.forEach { processData(it) }
                    return;
                }
                if (data !is BaseEntity) {
                    msgs.add(data.toString());
                    return;
                };
                var dataMsg = "";
                dataIds.add(data.id!!.toString());
                if (dataMsg.isNotEmpty())
                    dataMsgs.add(dataMsg)
            }
            processData(_details);
            details["dataIds"] = dataIds
            details["dataMsgs"] = dataMsgs
            if (msgs.isNotEmpty())
                details["msg"] = msgs.joinToString("\n")
        }
        lazyLog(user ?: "-", type, moduleName, objName, details);
    }

    fun lazyLog(log: OptLog) {
        this.logger.info("OptLog: ${log.username} ${log.moduleName}/${log.type} ${log.objName} ${log.details["msg"]} ${log.details["dataIds"]}")
        logCache.add(log)
    }

    fun lazyLog(username: String, type: String, moduleName: String, objName: String, details: Map<String, Any?>) {
        val log = OptLog()
        log.createdAt = Date()
        log.updatedAt = Date()
        log.username = username
        log.type = type
        log.moduleName = moduleName
        log.objName = objName
        log.details = details
        lazyLog(log)
    }
}
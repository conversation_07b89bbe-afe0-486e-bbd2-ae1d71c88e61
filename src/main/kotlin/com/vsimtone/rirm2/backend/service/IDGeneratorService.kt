package com.vsimtone.rirm2.backend.service


import com.vsimtone.rirm2.backend.entity.BaseEntity
import com.vsimtone.rirm2.backend.services.BaseService
import com.vsimtone.rirm2.backend.services.BaseService.Companion.appCtx
import com.vsimtone.rirm2.backend.utils.SnowflakeID
import org.hibernate.HibernateException
import org.hibernate.engine.spi.SharedSessionContractImplementor
import org.hibernate.id.Configurable
import org.hibernate.id.IdentifierGenerator
import org.hibernate.service.ServiceRegistry
import org.hibernate.type.Type
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.io.Serializable
import java.util.*

@Service
class IDGeneratorService {

    lateinit var snowflakeID: SnowflakeID

    companion object {
        const val IDGeneratorImplName = "com.vsimtone.rirm2.backend.service.IDGeneratorService\$Impl"
    }

    fun doInit(nodeId: Long) {
        snowflakeID = SnowflakeID(nodeId)
    }

    class Impl : IdentifierGenerator {

        private lateinit var name: String

        var service: IDGeneratorService? = null

        @Throws(HibernateException::class)
        override fun generate(session: SharedSessionContractImplementor, data: Any): Serializable {
            if (service == null)
                service = BaseService.appCtx.getBean(IDGeneratorService::class.java)
            if (data is BaseEntity) {
                if (data.id != null)
                    return data.id!!
            }
            return service!!.snowflakeID.nextId()
        }

        @Throws(HibernateException::class)
        override fun configure(type: Type, params: Properties, serviceRegistry: ServiceRegistry) {
            name = params.getProperty("target_table")
            if (params.contains("name"))
                name = params.getProperty("name")
        }
    }

}
package com.vsimtone.rirm2.backend.service

import com.hankcs.hanlp.corpus.tag.Nature.i
import com.vsimtone.rirm2.backend.entity.ChangeFormCommand
import com.vsimtone.rirm2.backend.entity.ChangeFormHost
import com.vsimtone.rirm2.backend.entity.QChangeFormCommand
import com.vsimtone.rirm2.backend.entity.QChangeFormHost
import com.vsimtone.rirm2.backend.repository.ChangeFormCommandRepository
import com.vsimtone.rirm2.backend.repository.ChangeFormHostRepository
import com.vsimtone.rirm2.backend.services.BaseService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import java.util.*

@Service
class ChangeFormCommandService : BaseCrudService<ChangeFormCommand, ChangeFormCommandRepository>(QChangeFormCommand.a) {

    @Autowired
    @Lazy
    lateinit var changeFormItemService: ChangeFormItemService

    companion object {
        data class CommandExcel(
            var name: String,
            var formId: String,
            var idx: String,
            var workTask: String,
            var realAction: String,
            var beginTime: String?,
            var endTime: String?,
            var effectServiceTimeM: String?,
            var execCondition: String?,
            var execAddress: String?,
            var cmdStart: String,
            var cmdEnd: String
        )
    }

    @Autowired
    lateinit var excelService: ExcelImportService
    override fun onReady() {
        val define = excelService.define(
            "变更单-实施方案", 1, CommandExcel::class, listOf(
                ExcelImportService.Companion.ColumnDefine("变更名称", CommandExcel::name, true),
                ExcelImportService.Companion.ColumnDefine("变更编号", CommandExcel::formId, true),
                ExcelImportService.Companion.ColumnDefine("步骤", CommandExcel::idx, true),
                ExcelImportService.Companion.ColumnDefine("工作任务", CommandExcel::workTask, true),
                ExcelImportService.Companion.ColumnDefine("具体变更对象及动作", CommandExcel::realAction, true),
                ExcelImportService.Companion.ColumnDefine("开始时间", CommandExcel::beginTime, true),
                ExcelImportService.Companion.ColumnDefine("结束时间", CommandExcel::endTime, true),
                ExcelImportService.Companion.ColumnDefine("影响服务分钟", CommandExcel::effectServiceTimeM, true),
                ExcelImportService.Companion.ColumnDefine("执行条件", CommandExcel::execCondition, true),
                ExcelImportService.Companion.ColumnDefine("执行地点", CommandExcel::execAddress, true),
                ExcelImportService.Companion.ColumnDefine("实施阶段", CommandExcel::cmdStart, true),
                ExcelImportService.Companion.ColumnDefine("实施后验证阶段", CommandExcel::cmdEnd, true)
            )
        )
        define.columnsInOneRow = false
        define.onColumn = { ctx: ExcelImportService.Companion.ImportContext<CommandExcel> ->
            this.onImportColumn(ctx)
        }
        define.onDelete = {
            repo.deleteByFromImport(it)
        }
        define.countImported = {
            repo.count(QChangeFormCommand.a.fromImport.eq(it))
        }
    }

    fun onImportColumn(ctx: ExcelImportService.Companion.ImportContext<CommandExcel>): String? {
        var changeFormId = ExcelImportService.strParseOfKType(ctx.columns[CommandExcel::formId]!!.right().value(), String::class)!!
        if (StringUtils.isAllBlank(changeFormId)) throw ExcelImportService.Companion.ImportException("变更编号不能为空")
        val cmd = mutableListOf<String>()
        var minBeginTime: Date? = null
        var maxEndTime: Date? = null
        ctx.xlsData.forEach {
            try {
                if (StringUtils.isEmpty(it[ctx.columns[CommandExcel::beginTime]!!.col]) || StringUtils.isEmpty(it[ctx.columns[CommandExcel::endTime]!!.col])) {
                    return@forEach
                }
                if (it[ctx.columns[CommandExcel::beginTime]!!.col]!!.indexOf("时间") != -1) return@forEach
                if (it[ctx.columns[CommandExcel::beginTime]!!.col]!!.indexOf("无") != -1) return@forEach

                val workTask = ExcelImportService.strParseOfKType(it[ctx.columns[CommandExcel::workTask]!!.col], String::class)
                val realAction = ExcelImportService.strParseOfKType(it[ctx.columns[CommandExcel::realAction]!!.col], String::class)
                var beginTime: Date? = null;
                var endTime: Date? = null;
                try {
                    beginTime = ExcelImportService.strParseOfKType(it[ctx.columns[CommandExcel::beginTime]!!.col], Date::class)
                    endTime = ExcelImportService.strParseOfKType(it[ctx.columns[CommandExcel::endTime]!!.col], Date::class)
                } catch (e: Exception) {
                    logger.info("unsupported datetime ${e.message}")
                }
                if (beginTime != null && endTime != null) {
                    if (StringUtils.isNotEmpty(workTask)) cmd.add(workTask!!)
                    if (StringUtils.isNotEmpty(realAction)) cmd.add(realAction!!)
                    if (minBeginTime == null || beginTime.time < (minBeginTime as Date).time) {
                        minBeginTime = beginTime
                    }
                    if (maxEndTime == null || endTime.time > (maxEndTime as Date).time) {
                        maxEndTime = endTime
                    }
                }
            } catch (e: Exception) {
                logger.warn("import ${ctx.import.file.name} exception", e)
            }
        }
        val cmdStr = cmd.joinToString("\n");
        if (StringUtils.isAllBlank(cmdStr)) {
            throw ExcelImportService.Companion.ImportException("未解析到命令")
        }
//        根据变更编号来判断数据重复
        val data = repo.findOne(QChangeFormCommand.a.changeFormId.eq(changeFormId)).orElse(ChangeFormCommand())
        data.changeFormId = changeFormId
        data.command = cmdStr
        data.fromImport = ctx.import
        data.beginTime = minBeginTime
        data.endTime = maxEndTime
        repo.save(data);
        changeFormItemService.addToMatchQueue(data.changeFormId)
        return null;
    }
}

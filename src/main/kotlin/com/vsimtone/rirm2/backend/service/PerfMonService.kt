package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.bean.PerfMonStats
import com.vsimtone.rirm2.backend.entity.APPConfig
import com.vsimtone.rirm2.backend.services.BaseService
import com.vsimtone.rirm2.backend.utils.DateUtil
import com.vsimtone.rirm2.backend.utils.LocalCacheQueue
import com.vsimtone.rirm2.backend.utils.RedisUtils
import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics
import org.redisson.api.RBlockingQueue
import org.redisson.api.RList
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.util.*

@Service
class PerfMonService : BaseService() {

    @Autowired
    lateinit var redisService: RedisService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var appConfigService: APPConfigService

    lateinit var pendingPerfDetails: RBlockingQueue<Array<PerfMonDetail>>

    lateinit var localPendingPerfDetails: LocalCacheQueue<PerfMonDetail>

    val perfMonStatsMap = mutableMapOf<String, RList<PerfMonStats>>()

    companion object {
        class PerfMonDetail : java.io.Serializable {
            var target: String? = null
            var name: String? = null
            var attrs: Map<String, String>? = mutableMapOf<String, String>()
            var value = 0.0
            var time = 0L
        }

        const val CNF_PERF_MON_DATA_EXPIRE_TIME = "core.perf_mon.data.expire_time"
    }

    init {
        readyInvokeOrder = READY_INVOKE_HIGH;
    }

    override fun onReady() {
        appConfigService.init(CNF_PERF_MON_DATA_EXPIRE_TIME, DateUtil.day(15), APPConfig.Type.Long);
        pendingPerfDetails = redisService.redisson.getBlockingQueue("perf_mon:pending_details", RedisUtils.autoCodec(Array<PerfMonDetail>::class.java));
        localPendingPerfDetails = LocalCacheQueue("PendingPerfMonDetails", 100000, 100, 10 * 1000) {
            pendingPerfDetails.add(it.toTypedArray());
        }
        schedule("perf_mon:clean_perf_data", DateUtil.hour(1)) { this.cleanPerfData() }
        schedule("perf_mon:pending_details_process", DateUtil.minute(1)) { this.processPendingDetails() }
    }

    @Synchronized
    private fun getPerfMonStatsList(target: String, name: String): RList<PerfMonStats> {
        val key = "${target}:${name}";
        if (!perfMonStatsMap.containsKey(key)) {
            perfMonStatsMap[key] = redisService.redisson.getList("perf_mon:stats:$key", RedisUtils.autoCodec(PerfMonStats::class.java))
        }
        return perfMonStatsMap[key]!!
    }

    private fun cleanPerfData() {
        val keys = redisService.redisson.keys.getKeysByPattern("perf_mon:stats:*")
        val expireTime = appConfigService.getLong(CNF_PERF_MON_DATA_EXPIRE_TIME)!!
        val deletedAt = Date(System.currentTimeMillis() - expireTime).time;
        var deletedIdx = 0;
        keys.forEach {
            val perfMonStatsList = redisService.redisson.getList<PerfMonStats>("perf_mon:stats:$it", RedisUtils.autoCodec(PerfMonStats::class.java))
            while (true) {
                var data = perfMonStatsList.range(0, 100)
                data.forEach f1@{
                    val stats = it;
                    if (stats.time <= deletedAt) {
                        deletedIdx++;
                    } else {
                        return@f1
                    }
                }
                if (deletedIdx == 0 || deletedIdx % 100 != 0) {
                    break
                }
            }
            perfMonStatsList.trim(deletedIdx, -1);
            logger.debug("Deleted $it $deletedIdx perf mon stats. free ${perfMonStatsList.size}");
        }
    }

    private fun processPendingDetails() {
        val startedAt = System.currentTimeMillis()
        val fetchedDetails = mutableListOf<PerfMonDetail>()
        while (true) {
            val tmp = pendingPerfDetails.poll() ?: break
            if (tmp.isEmpty()) break
            fetchedDetails.addAll(tmp)
            if (tmp.last().time > startedAt) break;
        }
        fetchedDetails.groupBy { it.target!! }.forEach { target, pendingDetails ->
            val detailsByName = mutableMapOf<String, MutableList<PerfMonDetail>>()
            val historgrams = mutableMapOf<String, DescriptiveStatistics>()
            pendingDetails.forEach {
                val l = detailsByName[it.name] ?: mutableListOf()
                if (l.isEmpty()) {
                    detailsByName[it.name!!] = l;
                    historgrams[it.name!!] = DescriptiveStatistics()
                }
                l.add(it)
                historgrams[it.name!!]!!.addValue(it.value)
            }
            historgrams.forEach { name, s ->
                val idxStats = PerfMonStats()
                idxStats.max = s.max
                idxStats.min = s.min
                idxStats.p50 = s.getPercentile(50.0)
                idxStats.p90 = s.getPercentile(90.0)
                idxStats.p99 = s.getPercentile(99.0)
                idxStats.first = detailsByName[name]!!.first().value
                idxStats.last = detailsByName[name]!!.last().value
                idxStats.count = detailsByName[name]!!.size.toLong()
                idxStats.sum = s.sum
                idxStats.avg = s.mean
                idxStats.time = detailsByName[name]!!.last().time
                getPerfMonStatsList(target, name).add(idxStats)
            }
        }
    }

    fun percentage(a: Double, b: Double): Double {
        return (b - a) * 100 / b;
    }

    fun addPerfMon(target: String, name: String, attrs: Map<String, String>, value: Double) {
        if (value < 0) return;
        val d = PerfMonDetail()
        d.target = target;
        d.name = name;
        d.attrs = attrs
        d.value = value;
        d.time = System.currentTimeMillis();
        localPendingPerfDetails.add(d);
    }

    fun uploadPerfMon(target: String, perfMonStats: Map<String, PerfMonStats>?, perfMonDetails: List<PerfMonDetail>?) {
        if (perfMonStats == null) return
        perfMonStats.forEach {
            val idxStats = PerfMonStats()
            idxStats.avg = it.value.avg
            idxStats.max = it.value.max
            idxStats.min = it.value.min
            idxStats.sum = it.value.sum
            idxStats.p50 = it.value.p50
            idxStats.p90 = it.value.p90
            idxStats.p99 = it.value.p99
            idxStats.first = it.value.first
            idxStats.last = it.value.last
            idxStats.count = it.value.count
            idxStats.time = it.value.time
            getPerfMonStatsList(target, it.key).add(idxStats)
        }
    }

    fun findAll(target: String, name: String, asc: Boolean, onItem: (item: PerfMonStats) -> Boolean) {
        var idx = 0;
        val batchSize = 1000;
        val perfMonStatsList = getPerfMonStatsList(target, name);
        val len = perfMonStatsList.size
        if (!asc) idx = len;
        val list = mutableListOf<PerfMonStats>()
        val started = System.currentTimeMillis();
        var totalFetchCount = 0;
        var totalFetchSize = 0;
        var fetchUseTime = 0L;
        while (true) {
            list.clear();
            val fetchList: List<PerfMonStats>;
            val fetchStarted = System.currentTimeMillis();
            if (asc) {
                fetchList = perfMonStatsList.range(idx, idx + batchSize - 1);
                idx += fetchList.size;
            } else {
                fetchList = perfMonStatsList.range(idx - batchSize, idx - 1)
                fetchList.reversed();
                idx -= fetchList.size;
            }
            fetchUseTime += System.currentTimeMillis() - fetchStarted;
            fetchList.forEach {
                list.add(it)
            }
            totalFetchCount++
            totalFetchSize += list.size
            var stop = false;
            list.forEach {
                if (stop) return@forEach;
                stop = onItem(it) == false
            }
            if (idx >= len || idx == 0 || list.size < batchSize || stop)
                break;
        }
        if (fetchUseTime >= 100)
            this.logger.info("Find all ${target} ${name} use ${DateUtil.useTimeToHum(System.currentTimeMillis() - started)}, fetch ${DateUtil.useTimeToHum(fetchUseTime)}, fetch ${totalFetchCount} while, ${totalFetchSize}/${len}")
    }

}
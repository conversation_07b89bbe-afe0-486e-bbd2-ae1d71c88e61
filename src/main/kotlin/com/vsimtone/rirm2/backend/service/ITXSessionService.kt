package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.entity.ITXCommand
import com.vsimtone.rirm2.backend.entity.ITXSession
import com.vsimtone.rirm2.backend.entity.QITXCommand
import com.vsimtone.rirm2.backend.entity.QITXSession
import com.vsimtone.rirm2.backend.repository.ITXCommandRepository
import com.vsimtone.rirm2.backend.repository.ITXSessionRepository
import com.vsimtone.rirm2.backend.services.BaseService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class ITXSessionService : BaseCrudService<ITXSession, ITXSessionRepository>(QITXSession.a) {
}

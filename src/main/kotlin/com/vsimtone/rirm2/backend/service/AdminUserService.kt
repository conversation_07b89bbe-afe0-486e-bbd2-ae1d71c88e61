package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.bean.CurrAuthedUser
import com.vsimtone.rirm2.backend.config.security.AdminSecurityConfig
import com.vsimtone.rirm2.backend.config.security.AdminSecurityConfig.AdminLoginAuthToken
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.AdminUser
import com.vsimtone.rirm2.backend.entity.BaseEntity
import com.vsimtone.rirm2.backend.entity.QAdminUser
import com.vsimtone.rirm2.backend.entity.APPConfig
import com.vsimtone.rirm2.backend.repository.AdminUserRepository
import com.vsimtone.rirm2.backend.services.BaseService
import com.vsimtone.rirm2.backend.utils.DateUtil
import org.apache.commons.codec.digest.DigestUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.event.EventListener
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.authentication.event.AuthenticationFailureBadCredentialsEvent
import org.springframework.security.authentication.event.AuthenticationSuccessEvent
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.core.userdetails.UserDetails
import org.springframework.security.core.userdetails.UserDetailsService
import org.springframework.security.core.userdetails.UsernameNotFoundException
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*
import kotlin.concurrent.thread


@Service
class AdminUserService : BaseService() {
    companion object {
        const val CNF_KEY_AUTO_DISABLE_DAYS = "core.admin_user.auto_disable_days"
    }

    @Autowired
    lateinit var repo: AdminUserRepository

    @Autowired
    lateinit var passwordEncoder: PasswordEncoder

    @Autowired
    lateinit var optLogService: OptLogService

    @Autowired
    lateinit var appConfigService: APPConfigService


    @Transactional
    @EventListener
    fun authenticationFailed(event: AuthenticationFailureBadCredentialsEvent) {
        if (event.authentication !is AdminLoginAuthToken) return;
        val ud = event.authentication as AdminLoginAuthToken
        val user = findByUsername(ud.username) ?: return;
        user.loginFailCount++;
        if (user.loginFailCount >= 10) user.locked = true;
        repo.save(user);
        optLogService.addOptLog(ud.username, "登录失败", "用户登录", user.nickname, user);
    }

    @Transactional
    @EventListener
    fun authenticationSuccess(event: AuthenticationSuccessEvent) {
        if (event.authentication !is AdminLoginAuthToken) return;
        val ud = event.authentication as AdminLoginAuthToken
        val username = ud.username
        val user = findByUsername(username)!!
        if (user.loginFailCount != 0) {
            user.loginFailCount = 0;
            user.lastLoginTime = Date();
            repo.save(user);
        }
        optLogService.addOptLog(user.username, "登录成功", "用户登录", user.nickname, user);
    }

    fun currUser(): CurrAuthedUser? {
        val auth = SecurityContextHolder.getContext().authentication ?: return null;
        if (auth is AdminSecurityConfig.AdminLoginAuthToken) {
            return auth.details!! as CurrAuthedUser
        }
        return null;
    }

    fun findByUsername(username: String): AdminUser? {
        return repo.findOneByUsername(username)
    }

    override fun onReady() {
        appConfigService.init(CNF_KEY_AUTO_DISABLE_DAYS, "90", APPConfig.Type.Long)
        schedule("admin_user.auto_disable", 3600 * 1000) {
            val disableDays = appConfigService.getLong(CNF_KEY_AUTO_DISABLE_DAYS) ?: 90
            repo.findAll(QAdminUser.a.lastLoginTime.loe(Date(DateUtil.day(-disableDays)))).forEach {
                logger.info("admin user last login too long. disable it. user=${it.username}, lastLoginTime=${BaseEntity.DEFAULT_DATE_FORMAT.format(it.lastLoginTime)}")
                it.enabled = false;
                repo.save(it);
            }
        }
        thread {
            Thread.sleep(10 * 1000);
            runInTran {
                var c = repo.count()
                if (c == 0L) {
                    var u = AdminUser();
                    u.nickname = "管理员"
                    u.username = "admin"
                    u.locked = false
                    u.enabled = true
                    var pwd = DigestUtils.sha1Hex(System.currentTimeMillis().toString() + "_" + Math.random()).substring(0, 8);
                    u.password = passwordEncoder.encode(pwd)
                    u.permissions.addAll(AdminPermissions.getAll())
                    repo.save(u)
                    logger.info("Created default user : ${u.username} ${pwd}")
                }
            }
        }
    }

    fun checkPassword(user: AdminUser, pass: String): String? {
        if (passwordEncoder.matches(pass, user.password!!)) return null;
        return "密码验证失败"
    }


}
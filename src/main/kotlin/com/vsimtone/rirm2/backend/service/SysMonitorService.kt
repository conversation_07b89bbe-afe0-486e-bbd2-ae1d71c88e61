package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.service.SysAlertService.Companion.SysAlert
import com.vsimtone.rirm2.backend.services.BaseService
import com.vsimtone.rirm2.backend.utils.DateUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

@Service
class SysMonitorService : BaseService() {

    @Autowired
    lateinit var changeFormItemService: ChangeFormItemService

    @Autowired
    lateinit var itxEventQueueService: ITXEventQueueService

    @Autowired
    lateinit var perfMonService: PerfMonService

    override fun onReady() {
        schedule("queue_monitor", DateUtil.minute(1)) {
            queueMonitor()
        }
    }

    fun queueMonitor() {
        if (!ready) return
        var m = fun(name: String, size: Long) {
            perfMonService.addPerfMon("app", "queue.${name}", mapOf(), size.toDouble())
        }
        m("cfItemMatchQueueSize", changeFormItemService.matchQueue.size.toLong())
        m("cfItemStatsQueueSize", changeFormItemService.needUpdateStatsQueue.size.toLong())
        m("itxEvtQueueSize", itxEventQueueService.repo.count())
    }
}

package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.entity.CommandCategory
import com.vsimtone.rirm2.backend.entity.FileFolder
import com.vsimtone.rirm2.backend.entity.QCommandCategory
import com.vsimtone.rirm2.backend.repository.CommandCategoryRepository
import com.vsimtone.rirm2.backend.services.BaseService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class CommandCategoryService : BaseCrudService<CommandCategory,CommandCategoryRepository>(QCommandCategory.a) {

    fun getNames(org: CommandCategory): List<String> {
        val names = mutableListOf<String>()
        var t = org
        while (true) {
            names.add(t.name)
            t = t.parent ?: break
        }
        names.reverse()
        return names;
    }

}

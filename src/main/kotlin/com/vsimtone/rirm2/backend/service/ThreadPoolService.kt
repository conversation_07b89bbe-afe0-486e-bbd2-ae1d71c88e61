package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.entity.APPConfig
import com.vsimtone.rirm2.backend.services.BaseService
import com.vsimtone.rirm2.backend.utils.AppUtils
import org.redisson.api.RBlockingQueue
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
import org.springframework.stereotype.Service
import java.util.concurrent.atomic.AtomicInteger
import kotlin.concurrent.thread

@Service
class ThreadPoolService : BaseService() {

    companion object {
        const val CNF_CORE_POOL_SIZE = "core.thread_pool.core_size"
    }

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var redisService: RedisService

    var executor = ThreadPoolTaskExecutor()

    var pools = mutableListOf<PoolInfo<*>>()

    inner class PoolInfo<T>(_key: String, _weight: Int = 1, _run: (arg: T) -> Unit) : Runnable {

        lateinit var queue: RBlockingQueue<T>

        var key = _key
        var maxSize = 1
        var weight = _weight
        var run = _run
        var running = false

        var errCount = 0L;
        val runningCount = AtomicInteger()

        fun init() {
            queue = redisService.redisson.getBlockingQueue("queue.thread_pool.${key}")
            thread { run() }
        }

        override fun run() {
            if (running) return;
            running = true
            while (true) {
                try {
                    val item = queue.take()
                    while (runningCount.get() >= maxSize) {
                        Thread.sleep(100)
                    }
                    executor.submit {
                        runningCount.addAndGet(1)
                        try {
                            run(item)
                        } finally {
                            runningCount.addAndGet(-1)
                        }
                    }
                    errCount = 0L;
                } catch (e: Exception) {
                    if (shutdowning) return;
                    if (e is InterruptedException) {
                        return;
                    }
                    logger.error("${key} submit to pool fail", e)
                    if (errCount < 30) errCount++;
                    Thread.sleep(1000 * (errCount + 1))
                }
            }
        }

        fun addToQueue(vararg ars: T) {
            queue.addAll(ars)
        }

    }

    override fun onReady() {
        appConfigService.init(CNF_CORE_POOL_SIZE, 100, APPConfig.Type.Integer);
        executor.initialize();
        updatePoolSize();
    }

    @Scheduled(fixedDelay = 1000 * 30)
    fun monitor() {
        val coreSize = appConfigService.getInteger(CNF_CORE_POOL_SIZE)!!
        if (coreSize != executor.corePoolSize) updatePoolSize()
        val totalRunningCount = pools.sumOf { it.runningCount.get() }
        val totalQueueCount = pools.sumOf { it.queue.size }
        if (totalRunningCount == 0) return;
        logger.info("-------- Thread pool monitor --------")
        logger.info("Total running: ${totalRunningCount} / ${coreSize} queue: ${totalQueueCount}")
        pools.forEach {
            logger.info("-> ${it.key} running: ${it.runningCount}/${it.maxSize} queue: ${it.queue.size}")
        }
        logger.info("-------------------------------------")
    }

    fun updatePoolSize() {
        var coreSize = appConfigService.getInteger(CNF_CORE_POOL_SIZE)!!
        if (coreSize < pools.size * 2)
            coreSize = pools.size * 2
        var totalWeight = pools.sumOf { it.weight }
        pools.forEach {
            it.maxSize = it.weight * coreSize / totalWeight;
        }
        executor.corePoolSize = coreSize;
        executor.maxPoolSize = coreSize * (pools.size + 1);
    }

    fun <T> install(_key: String, _weight: Int = 1, _run: (arg: T) -> Unit): PoolInfo<T> {
        var info = PoolInfo<T>(_key, _weight, _run)
        if (pools.find { it.key == _key } != null)
            throw IllegalArgumentException("Repeat define pool");
        pools.add(info);
        updatePoolSize();
        info.init();
        return info;
    }
}

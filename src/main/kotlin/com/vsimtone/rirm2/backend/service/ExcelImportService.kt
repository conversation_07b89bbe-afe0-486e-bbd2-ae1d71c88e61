package com.vsimtone.rirm2.backend.service

import com.vsimtone.rirm2.backend.entity.ExcelImport
import com.vsimtone.rirm2.backend.entity.FileFolder
import com.vsimtone.rirm2.backend.repository.ExcelImportRepository
import com.vsimtone.rirm2.backend.services.BaseService
import com.vsimtone.rirm2.backend.utils.AppUtils
import org.apache.poi.ss.usermodel.DateUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*
import kotlin.reflect.KClass
import kotlin.reflect.KMutableProperty
import kotlin.reflect.full.createInstance
import kotlin.reflect.jvm.javaField

@Service
class ExcelImportService : BaseService() {

    @Autowired
    lateinit var repo: ExcelImportRepository

    companion object {
        class ImportContext<T : Any>() {
            lateinit var columns: MutableMap<KMutableProperty<*>, XlsPos>;
            lateinit var xlsData: List<List<String?>>;
            lateinit var import: ExcelImport;
        }

        data class ColumnDefine(
            var name: String, var field: KMutableProperty<*>, var required: Boolean
        )

        data class ExcelDefine<T : Any>(
            var name: String,
            var index: Int,
            var cls: KClass<T>,
            var columns: List<ColumnDefine>,
            var columnsInOneRow: Boolean = true,
            var onColumn: ((ctx: ImportContext<T>) -> Unit)? = null,
            var onData: ((ctx: ImportContext<T>, data: T?, row: Int) -> String?)? = null,
            var onDone: (() -> Unit)? = null,
            var onDelete: ((file: ExcelImport) -> Long)? = null,
            var countImported: ((file: ExcelImport) -> Long?)? = null
        ) {
        }

        class ImportException(msg: String) : RuntimeException(msg) {}

        data class XlsPos(val ctx: ImportContext<*>, val row: Int, val col: Int) {
            fun top(): XlsPos {
                return XlsPos(ctx, row - 1, col)
            }

            fun left(): XlsPos {
                return XlsPos(ctx, row, col - 1)
            }

            fun right(): XlsPos {
                return XlsPos(ctx, row, col + 1)
            }

            fun bottom(): XlsPos {
                return XlsPos(ctx, row + 1, col)
            }

            fun value(): String? {
                return ctx.xlsData[row][col]
            }
        }


        fun parseDate(v: String): Date {
            val formats = arrayOf("yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd'T'HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy/MM/dd HH:mm", "yyyy-MM-dd", "yyyy/MM/dd", "yyyyMMdd")
            var data = v;
            if (data.matches(Regex("^\\d+(\\.\\d+)?$"))) {
                val t = data.toDouble();
                if (t < 1000000000) {
                    return DateUtil.getJavaDate(t)
                }
                if (t < 1000000000000) {
                    return Date(t.toLong() * 1000)
                }
                return Date(t.toLong())
            }
            for (fmt in formats) {
                val dd = SimpleDateFormat(fmt)
                try {
                    return dd.parse(data.toString())
                } catch (e: ParseException) {
                }
            }
            throw ImportException("无法解析的日期格式:${v}")
        }

        fun <T : Any> strParseOfKType(v: String?, t: KClass<T>): T? {
            if (v == null) return null;
            else if (t == String::class) return v.toString() as T;
            else if (t == Long::class) return v.toLong() as T;
            else if (t == Int::class) return v.toInt() as T;
            else if (t == Date::class) return parseDate(v) as T;
            throw ImportException("Unsupported type $t")
        }

        val COLUMN_CLEAN_REGEX = Regex("([\\r\\s\\t\\n]+|\\(.*?\\)|（.*?）|【.*?】|[.*?])");

        const val CNF_EXCEL_IMPORT_LABELS = "core.excel_import.labels";
    }

    @Autowired
    lateinit var folderService: FileFolderService;

    @Autowired
    lateinit var fileInfoService: FileInfoService;

    @Autowired
    lateinit var appConfigService: APPConfigService;

    @Autowired
    lateinit var redisService: RedisService;


    var excelDefines = mutableListOf<ExcelDefine<*>>()

    override fun onReady() {
    }

    fun <T : Any> define(name: String, index: Int, cls: KClass<T>, columns: List<ColumnDefine>): ExcelDefine<T> {
        val define = ExcelDefine(name, index, cls, columns)
        excelDefines.add(define);
        excelDefines.sortBy { it.index }
        return define;
    }

    fun columnEquals(_a: String, _b: String): Boolean {
        val a = _a.replace(COLUMN_CLEAN_REGEX, "")
        val b = _b.replace(COLUMN_CLEAN_REGEX, "")
        return a.equals(b, true)
    }

    fun columnMatched(columnDefine: ColumnDefine, labels: MutableMap<KMutableProperty<*>, MutableList<String>>, text: String?): Boolean {
        if (text == null) return false;
        return labels[columnDefine.field]!!.find { name -> columnEquals(name, text) } != null
    }

    private fun matchColumnsByLine(lineData: List<String?>, define: ExcelDefine<*>, labels: MutableMap<KMutableProperty<*>, MutableList<String>>, results: MutableMap<String, String>): MutableMap<KMutableProperty<*>, Int> {
        val matches = mutableMapOf<KMutableProperty<*>, Int>()
        lineData.forEachIndexed { colIdx, text ->
            define.columns.forEach { columnDefine ->
                if (columnMatched(columnDefine, labels, text)) {
                    matches[columnDefine.field] = colIdx
                }
            }
        }
        if (matches.isEmpty()) return matches;
        define.columns.forEach {
            if (it.required && matches[it.field] == null) {
                logger.info("    匹配表头 ${define.name} 失败：未找到列：${it.name}")
                results["match_columns_fail:${define.name}"] = "未找到列：${it.name}"
                matches.clear()
                return matches
            }
        }
        return matches
    }


    fun <T : Any> importFile(file: ExcelImport, define: ExcelDefine<T>) {
        val configLabels = appConfigService.getMap(CNF_EXCEL_IMPORT_LABELS)!! as Map<String, List<String>>;
        val columnLabels = mutableMapOf<KMutableProperty<*>, MutableList<String>>()
        define.columns.forEach {
            val arr = mutableListOf<String>()
            arr.add(it.name)
            arr.addAll(configLabels[define.cls.simpleName + "." + it.field.name] ?: emptyList())
            columnLabels[it.field] = arr
        }
        val ctx = ImportContext<T>();
        ctx.columns = mutableMapOf()
        ctx.import = file

        fileInfoService.getInputStream(file.file.path).use {
            try {
                ctx.xlsData = AppUtils.readExcel(it!!)
            } catch (e: Exception) {
                throw ImportException("Excel读取失败, " + e.message);
            }
            if (define.columnsInOneRow) {
                ctx.columns.clear();
                for (i in ctx.xlsData.indices) {
                    val lineData = ctx.xlsData[i];
                    val matchColumns = matchColumnsByLine(lineData, define, columnLabels, file.results);
                    if (matchColumns.isEmpty()) continue;
                    matchColumns.forEach { (k, v) ->
                        ctx.columns[k] = XlsPos(ctx, i, v)
                    }
                    break;
                }
                if (ctx.columns.isEmpty()) {
                    return;
                }
                logger.info("    匹配表头 ${define.name} 成功。匹配表头数量${ctx.columns.size}")
                file.importedRowCount = 0;
                file.defineCls = define.cls.simpleName
                file.defineName = define.name
                if (define.onColumn != null) define.onColumn!!(ctx);
                logger.info("    开始导入...")
                val columnRow = ctx.columns.values.first().row;
                for (i in ctx.xlsData.indices) {
                    if (i <= columnRow) continue;
                    if (ctx.xlsData[i].filterNotNull().joinToString("").trim().isEmpty()) continue;
                    val data = define.cls.createInstance();
                    var skipLine = false;
                    ctx.columns.forEach { f, pos ->
                        if (skipLine) return@forEach
                        val colDefine = define.columns.find { it.field == f }!!
                        val value = ctx.xlsData[i][pos.col]
                        try {
                            if (colDefine.required && value == null) {
                                file.results["skip_line:${define.name}:" + (i + 1)] = "${ctx.xlsData[pos.row][pos.col]} 不能为空";
                                skipLine = true;
                                return@forEach;
                            }
                            f.setter.call(data, strParseOfKType(value, f.javaField!!.type.kotlin))
                        } catch (e: Exception) {
                            throw ImportException("行${pos.row + 1}, 列${pos.col + 1},${ctx.xlsData[pos.row][pos.col]}, 值 '${value}' 解析失败，要求类型：${f.javaField!!.type.kotlin.simpleName}，错误：${e.message}")
                        }
                    }
                    if (skipLine) continue;
                    val r = define.onData!!(ctx, data, i);
                    if (r != null) file.results["skip_line:${define.name}:$i"] = r;
                }
                if (define.onDone != null) define.onDone!!();
                logger.info("    完成.")
                file.status = ExcelImport.Status.Success
                file.results = file.results;
            } else {
                for (i in ctx.xlsData.indices) {
                    val lineData = ctx.xlsData[i];
                    define.columns.forEach { columnDefine ->
                        lineData.forEachIndexed() { colIdx, colText ->
                            if (ctx.columns.containsKey(columnDefine.field)) return@forEachIndexed
                            if (colText == null) return@forEachIndexed
                            if (columnMatched(columnDefine, columnLabels, colText)) {
                                ctx.columns[columnDefine.field] = XlsPos(ctx, i, colIdx)
                            }
                        }
                    }
                }
                val missRequiredColumn = define.columns.find { it.required && !ctx.columns.containsKey(it.field) }
                if (missRequiredColumn != null) {
                    logger.info("    匹配表头 ${define.name} 失败：未找到列：${missRequiredColumn.name}")
                    file.results["match_columns_fail:${define.name}"] = "未找到列：${missRequiredColumn.name}"
                    return;
                }
                logger.info("    匹配表头 ${define.name} 成功。匹配表头数量${ctx.columns.size}")
                file.importedRowCount = 0;
                file.defineCls = define.cls.simpleName
                file.defineName = define.name
                if (define.onColumn != null) define.onColumn!!(ctx);
                if (define.onDone != null) define.onDone!!();
                file.status = ExcelImport.Status.Success
            }
        }
    }

    fun folder(): FileFolder {
        return folderService.autoCreateOrGet("/Excel导入/" + SimpleDateFormat("YYYY-MM-dd").format(Date()));
    }

    fun importLockRun(from: String, wait: Long, cb: () -> Unit): Boolean {
        return redisService.runInLock("excel_import.importing", wait) {
            this.logger.info("Got import lock. from=${from}")
            cb()
            this.logger.info("Release import lock. from=${from}")
        }
    }

    @Transactional
    fun importAll() {
        importLockRun("excelImport@importAll", 10) {
            val pendingImports = repo.findAllByStatusIn(ExcelImport.Status.Pending)
            pendingImports.forEach {
                it.results = mutableMapOf();
            }
            var successCount = 0;
            var failCount = 0;
            logger.info("Importing begin : ${pendingImports.size}")
            for (d in excelDefines) {
                for (f in pendingImports) {
                    if (f.status != ExcelImport.Status.Pending) continue;
                    logger.info("Importing ${f.file.name} ${f.status}: try use ${d.name}")
                    f.errmsg = null;
                    try {
                        importFile(f, d)
                    } catch (e: ImportException) {
                        f.status = ExcelImport.Status.Fail;
                        f.errmsg = "错误：" + e.message;
                    } catch (e: Exception) {
                        f.status = ExcelImport.Status.Fail;
                        f.errmsg = "错误：" + e.message;
                    }
                    if (f.status == ExcelImport.Status.Success) successCount++;
                    if (f.status == ExcelImport.Status.Fail) failCount++;
                    repo.save(f);
                    repo.flush();
                    logger.info("    result: ${f.status} ${f.errmsg ?: ""}")
                }
            }
            for (f in pendingImports) {
                f.importedRowCount = 0;
                for (d in excelDefines) {
                    if (d.countImported != null) f.importedRowCount += d.countImported!!(f)?.toInt() ?: 0;
                }
                if (f.status == ExcelImport.Status.Pending) {
                    f.errmsg = "未识别Excel";
                    failCount++;
                }
                repo.save(f);
            }
            logger.info("Importing end : ${successCount} success, ${failCount} fail.")

        }
    }

    fun doDelete(ei: ExcelImport) {
        val define = excelDefines.find { it.cls.simpleName == ei.defineCls };
        if (define?.onDelete != null) {
            val deleted = define.onDelete!!(ei);
            logger.info("Deleted ${deleted} ${define.name} from ${ei}")
        }
        repo.delete(ei);
        logger.info("Deleted ${ei}")
    }

}
package com.vsimtone.rirm2.backend

import ch.qos.logback.classic.Level
import ch.qos.logback.classic.LoggerContext
import ch.qos.logback.classic.encoder.PatternLayoutEncoder
import ch.qos.logback.classic.filter.LevelFilter
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.Appender
import ch.qos.logback.core.ConsoleAppender
import ch.qos.logback.core.OutputStreamAppender
import ch.qos.logback.core.UnsynchronizedAppenderBase
import ch.qos.logback.core.rolling.FixedWindowRollingPolicy
import ch.qos.logback.core.rolling.RollingFileAppender
import ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy
import ch.qos.logback.core.spi.ContextAwareBase
import ch.qos.logback.core.spi.LifeCycle
import ch.qos.logback.core.status.Status
import ch.qos.logback.core.util.FileSize
import com.vsimtone.rirm2.backend.utils.Log
import org.apache.commons.io.FileUtils
import org.apache.commons.io.FilenameUtils
import org.apache.commons.lang3.StringUtils
import org.apache.poi.openxml4j.util.ZipSecureFile
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.slf4j.Logger.ROOT_LOGGER_NAME
import org.slf4j.LoggerFactory
import org.springframework.boot.Banner
import org.springframework.boot.SpringApplication
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration
import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent
import org.springframework.boot.runApplication
import org.springframework.cache.annotation.EnableCaching
import org.springframework.context.annotation.EnableAspectJAutoProxy
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.annotation.EnableScheduling
import org.springframework.session.data.redis.config.annotation.web.http.EnableRedisHttpSession
import org.springframework.transaction.annotation.EnableTransactionManagement
import org.springframework.util.ResourceUtils
import org.springframework.util.unit.DataSize
import java.io.PrintStream
import java.security.Security
import java.util.*
import javax.crypto.Cipher
import kotlin.system.exitProcess
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds


@SpringBootApplication()
@EnableScheduling
@EnableRedisHttpSession(maxInactiveIntervalInSeconds = 3600, redisNamespace = "rirm2")
@EnableTransactionManagement
@EnableAsync
@EnableCaching
@EnableJpaRepositories("com.vsimtone.rirm2.backend.repository")
@EnableAspectJAutoProxy(proxyTargetClass = false)
class Main : WebMvcAutoConfiguration() {}

fun checkJceLimit() {
    val maxKeySize = Cipher.getMaxAllowedKeyLength("AES")
    if (maxKeySize <= 128) {
        println("This jdk jce policy not support aes256.")
        exitProcess(1);
    }
}

fun configLogStart(a: ContextAwareBase) {
    var b = a as LifeCycle;
    b.start();
    var warnCnt = a.statusManager.copyOfStatusList.count { it.level > Status.INFO };
    if (!a.isStarted || warnCnt > 0) {
        a.statusManager.copyOfStatusList.forEach {
            System.err.println(it.toString());
        }
        System.err.println("log cmp start failed: $a");
        exitProcess(1);
    }
}

fun <T : OutputStreamAppender<ILoggingEvent>> configLogBaseAdapter(logCtx: LoggerContext, config: Map<String, String>, adapter: T, name: String, defaultPattern: String): T? {
    val enable = config["app.log.${name}.enable"] == "true";
    if (!enable) return null;
    var cnfPattern = config["app.log.${name}.pattern"]
    if (StringUtils.isEmpty(cnfPattern)) cnfPattern = defaultPattern;
    val logEncoder = PatternLayoutEncoder()
    logEncoder.context = logCtx
    logEncoder.pattern = cnfPattern;
    configLogStart(logEncoder)


    if (!StringUtils.isEmpty(config["app.log.${name}.level"])) {
        val levelFilter = LevelFilter();
        levelFilter.context = logCtx;
        levelFilter.setLevel(Level.toLevel(config["app.log.${name}.level"]));
        configLogStart(levelFilter)
    }

    adapter.encoder = logEncoder;
    adapter.name = name;
    adapter.context = logCtx;

    return adapter;
}

fun configLog(it: ApplicationEnvironmentPreparedEvent) {
    val logger = Log[Main::class.java];
    val config = mutableMapOf<String, String>();
    it.environment.propertySources.stream().forEach {
        val s = it.source
        if (s is Properties) {
            s.forEach { k, v ->
                if (!config.contains(k)) config[k.toString()] = v.toString();
            }
        }
        if (s is Map<*, *>) {
            s.forEach { k, v ->
                if (!config.contains(k)) config[k.toString()] = v.toString();
            }
        }
    }

    val logCtx: LoggerContext = LoggerFactory.getILoggerFactory() as LoggerContext;
    logCtx.statusManager.add() {
        if (it.level == Status.WARN) println("Log init warn: ${it.message}");
        if (it.level == Status.ERROR) println("Log init error: ${it.message}");
    };
    val rootLogger = logCtx.getLogger(ROOT_LOGGER_NAME);
    rootLogger.detachAndStopAllAppenders();

    val consoleLogToFile = config["app.log.redirectConsoleToFile"] == "true";
    val consoleAppender = configLogBaseAdapter(logCtx, config, ConsoleAppender(), "console", logCtx.getProperty("CONSOLE_LOG_PATTERN"));
    val rollingFileAppender = configLogBaseAdapter(logCtx, config, RollingFileAppender(), "file", logCtx.getProperty("FILE_LOG_PATTERN"));

    if (consoleLogToFile && consoleAppender != null) {
        System.err.println("redirectConsoleToFile require disable app.log.console.enable");
        exitProcess(1);
    }
    if (consoleLogToFile && rollingFileAppender == null) {
        System.err.println("redirectConsoleToFile require enable app.log.file.enable");
        exitProcess(1);
    }

    if (consoleAppender != null) {
        configLogStart(consoleAppender);
    }

    if (rollingFileAppender != null) {
        var maxFileSize = 1024 * 1024 * 100L; // 默认100MB
        var maxFileCount = 3;
        var logFilePath = "logs/app.log";

        if (!StringUtils.isEmpty(config["app.log.file.max-file-size"])) maxFileSize = FileSize.valueOf(config["app.log.file.max-file-size"]!!).size;
        if (!StringUtils.isEmpty(config["app.log.file.max-file-count"])) maxFileCount = config["app.log.file.max-file-count"]!!.toInt();
        if (!StringUtils.isEmpty(config["app.log.file.path"])) logFilePath = config["app.log.file.path"]!!;
        if (maxFileSize < 1024 * 1024 * 1) { // 最小1MB
            maxFileSize = 1024 * 1024 * 1
            println("app.log.file.max-file-size must be > 1MB")
        }
        if (maxFileCount < 2) {
            maxFileCount = 2;
            println("app.log.file.max-file-count must be > 2")
        }
        rollingFileAppender.file = logFilePath;
        rollingFileAppender.isAppend = true;

        val triggeringPolicy = SizeBasedTriggeringPolicy<ILoggingEvent>();
        triggeringPolicy.context = logCtx;
        triggeringPolicy.setMaxFileSize(FileSize(maxFileSize));
        // triggeringPolicy.checkIncrement = ch.qos.logback.core.util.Duration.buildBySeconds(60.0) // Not available in older logback versions
        configLogStart(triggeringPolicy);

        val rollingPolicy = FixedWindowRollingPolicy();
        rollingPolicy.setParent(rollingFileAppender);
        rollingPolicy.context = logCtx;
        rollingPolicy.minIndex = 1;
        rollingPolicy.maxIndex = maxFileCount;
        val ext = FilenameUtils.getExtension(logFilePath)
        rollingPolicy.fileNamePattern = logFilePath.replace(Regex("\\.${ext}\\\$"), "") + ".%i.${ext}";
        configLogStart(rollingPolicy);
        rollingFileAppender.rollingPolicy = rollingPolicy;

        rollingFileAppender.triggeringPolicy = triggeringPolicy;
        configLogStart(rollingFileAppender)
    }
    if (consoleAppender != null) rootLogger.addAppender(consoleAppender);
    if (rollingFileAppender != null) rootLogger.addAppender(rollingFileAppender);

    if (consoleLogToFile) {
        println("stdout and stderr redirect to ${rollingFileAppender!!.rollingPolicy.activeFileName}.");
        val stdoutLogger = logCtx.getLogger("STDOUT");
        System.setOut(object : PrintStream(System.out) {
            override fun println(x: String?) {
                if (x == null) return;
                if (stdoutLogger.isDebugEnabled) {
                    val stack = Thread.currentThread().stackTrace;
                    stdoutLogger.info("${stack[2]}: ${x}");
                } else {
                    stdoutLogger.info("${x}");
                }
            }
        });
        System.setErr(object : PrintStream(System.err) {
            override fun println(x: String?) {
                if (x == null) return;
                if (stdoutLogger.isDebugEnabled) {
                    val stack = Thread.currentThread().stackTrace;
                    stdoutLogger.error("${stack[2]}: ${x}");
                } else {
                    stdoutLogger.error("${x}");
                }
            }
        });
    }

    if (consoleLogToFile) {
        println("stdout and stderr redirected."); // this line in log file
    }

    config.forEach { (k, v) ->
        if (k.startsWith("app.log.level")) {
            val name = k.substring(14);
            logCtx.getLogger(name).level = Level.toLevel(v);
        }
    }
}

fun main(args: Array<String>) {
    Security.addProvider(BouncyCastleProvider());
    Security.setProperty("crypto.policy", "unlimited");
    checkJceLimit();
    ZipSecureFile.setMinInflateRatio(0.0);
    val app = SpringApplication(Main::class.java);
    app.setBannerMode(Banner.Mode.OFF);
    app.addListeners({
        if (it is ApplicationEnvironmentPreparedEvent) {
            configLog(it);
        }
    });
    app.run(*args);
}

package com.vsimtone.rirm2.backend.consts

import kotlin.reflect.full.memberProperties

object AdminPermissions {

    const val EXCEL_IMPORT = "ROLE_EXCEL_IMPORT";
    const val REPORT_FORM_MANAGE = "ROLE_REPORT_FORM_MANAGE";
    const val CHART_MANAGE = "ROLE_CHART_MANAGE";
    const val CHANGE_FORM_MANAGE = "ROLE_CHANGE_FORM_MANAGE";

    const val ALERT_INFO = "ROLE_ALERT_INFO";
    const val ITX_COMMAND = "ROLE_ITX_COMMAND";
    const val EXECL_CMD_IMPORT = "ROLE_EXECL_CMD_IMPORT";
    const val MANAGER = "ROLE_MANAGER";
    const val USER_ROLE = "ROLE_USER_ROLE";
    const val CMD_RULE = "ROLE_CMD_RULE";
    const val SYNC_RECORD = "ROLE_SYNC_RECORD";
    const val SYS_MONITOR = "ROLE_SYS_MONITOR";
    const val OPT_LOG = "ROLE_OPT_LOG";
    const val SYS_CONFIG = "ROLE_SYS_CONFIG";
    const val SUPER_DELETER = "ROLE_SUPER_DELETER";
    const val CHINA_CMD_MATCH = "ROLE_CHINA_CMD_MATCH";
    
    fun getAll(): Array<String> {
        return this::class.memberProperties
            .filter { it.isConst }
            .map { it.getter.call() as String }
            .toTypedArray()
    }
    
}

package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.*
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.ChangeFormCommandMappingService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController()
@RequestMapping("change_form_command_mappings")
class ChangeFormCommandMappingController : BaseController() {

    @Autowired
    lateinit var service: ChangeFormCommandMappingService

    @RequestMapping("")
    @Secured(AdminPermissions.CHINA_CMD_MATCH)
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam(value = "source", required = false) source: String?,
        @RequestParam(value = "target", required = false) target: String?,
        @RequestParam(value = "enabled", required = false) enabled: Boolean?,
    ): ApiResp {
        val q = QChangeFormCommandMapping.a;
        val queries = BooleanBuilder()
        if (!StringUtils.isEmpty(source)){
            queries.andAnyOf(BooleanBuilder(q.source.like("%$source%")))
        }
        if (!StringUtils.isEmpty(target)){
            queries.andAnyOf(BooleanBuilder(q.target.like("%$target%")))
        }
        if(enabled !=null){
            queries.andAnyOf(BooleanBuilder(q.enabled.eq(enabled)))
        }
        val p = service.repo.findAll(queries, pageAttr.get());
        return jsonOut(p)
    }


    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(AdminPermissions.CHINA_CMD_MATCH)
    @JsonView(JsonViews.AdminView::class)
    fun delete(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        try {
            service.repo.deleteById(id)
        } catch (e: Exception) {
            if (e.message != null && e.message!!.indexOf("ORA-02292") != -1) {
                return this.jsonOut(1, "该中文命令匹配信息不能删除。")
            }
            throw e;
        }
        addOptLog(OptLog.DELETE, "中文命令匹配", data.target, data);
        return jsonOut(true);
    }

    @RequestMapping("{id:\\d+}")
    @Secured(AdminPermissions.CHINA_CMD_MATCH)
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        addOptLog(OptLog.VIEW, "中文命令匹配", data.source, data);
        return jsonOut(data);
    }


    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(AdminPermissions.CHINA_CMD_MATCH)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun edit(@ModelAttribute("form") form: ChangeFormCommandMapping): ApiResp {
        var data: ChangeFormCommandMapping;
        if (form.id != null) {
            data = service.repo.findById(form.id!!).get();
        } else {
            data = ChangeFormCommandMapping();
        }

        data.enabled = form.enabled;
        data.source = form.source;
        data.target = form.target;
        data.remark = form.remark;
        service.repo.save(data);
        if (form.id != null)
            addOptLog(OptLog.EDIT, "中文命令匹配", data.source, data);
        else
            addOptLog(OptLog.ADD, "中文命令匹配", data.source, data);
        return jsonOut(true);
    }


}
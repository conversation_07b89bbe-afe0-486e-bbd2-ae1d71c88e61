package com.vsimtone.rirm2.backend.controller

import com.github.cage.Cage
import com.github.cage.image.RgbColorGenerator
import com.vsimtone.rirm2.backend.service.CaptchaService
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse
import javax.servlet.http.HttpSession
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.*

@RequestMapping(path = arrayOf("/captcha", "/www/captcha", "/admin/captcha"))
@RestController
open class CaptchaController {

    @Autowired
    lateinit var captchaService: CaptchaService

    companion object {

    }

    val logger = LoggerFactory.getLogger("captchaController")

    @RequestMapping(method = arrayOf(RequestMethod.POST, RequestMethod.GET))
    open fun output(
        session: HttpSession, req: HttpServletRequest, resp: HttpServletResponse, @RequestParam("type", required = false) type: CaptchaService.Companion.Type?
    ) {
        if (type == null || type == CaptchaService.Companion.Type.Image) {
            var cage = Cage(null, null, RgbColorGenerator(Random()), null, 1F, null, Random());
            resp.outputStream.use {
                resp.contentType = "image/jpeg"
                cage.draw(captchaService.generateImage(session), it)
            }
            return;
        } else {
            resp.sendError(HttpStatus.BAD_REQUEST.value())
        }
    }
}
package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.BaseEntity
import com.vsimtone.rirm2.backend.entity.CommandRule
import com.vsimtone.rirm2.backend.entity.ITXCommand
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.ITXCommandService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.text.SimpleDateFormat
import java.util.*

@RestController()
@RequestMapping("charts")
class ChartController : BaseController() {

    @Autowired
    lateinit var itxCommandService: ITXCommandService;

    @RequestMapping("/alert_level_count")
    @Secured(AdminPermissions.CHART_MANAGE)
    @JsonView(JsonViews.AdminList::class)
    fun alertLevelCount(year: Int): ApiResp {
        val calendar: Calendar = Calendar.getInstance();
        val sdf: SimpleDateFormat = SimpleDateFormat("yyyy-MM");//格式化为年月
        val currYear = calendar.get(Calendar.YEAR);
        val startTime: String? = year.toString() + "-01-01";
        val endTime: String? = year.toString() + "-12-31";
        val list = itxCommandService.alertLevelCount(startTime, endTime)
        val data = mutableMapOf<String, MutableMap<String, Long>>();

        val times: List<String>;
        if (currYear == year) {
            val maxTime: String = sdf.format(calendar.time);
            times = getMonthBetween(year.toString() + "-01", maxTime)
        } else {
            times = getMonthBetween(year.toString() + "-01", year.toString() + "-12")
        }

        val typeLists = arrayOf("Low", "Normal", "High");
        for (t in typeLists) {
            val tmp = mutableMapOf<String, Long>();
            for (time in times) {
                tmp.put(time, 0);
            }
            data.put(t, tmp);
        }
        list.forEach {
            if (it[1] == null || it[1].equals("null"))
                return@forEach
            val tmp = data.get(it[1].toString())
            if (tmp != null) {
                tmp.put(it[0].toString(), it[2] as Long)
            }
        }

        return jsonOut(data);
    }


    /**
     *
     * @param minDate 最小时间  2015-01
     * @param maxDate 最大时间 2015-10
     * @return 日期集合 格式为 年-月
     * @throws Exception
     */
    fun getMonthBetween(minDate: String, maxDate: String): List<String> {
        val result: ArrayList<String> = ArrayList<String>();
        val sdf: SimpleDateFormat = SimpleDateFormat("yyyy-MM");//格式化为年月

        val min: Calendar = Calendar.getInstance();
        val max: Calendar = Calendar.getInstance();

        min.time = sdf.parse(minDate);
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);

        max.time = sdf.parse(maxDate);
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);

        val curr: Calendar = min;
        while (curr.before(max)) {
            result.add(sdf.format(curr.time));
            curr.add(Calendar.MONTH, 1);
        }
        return result;
    }

    @RequestMapping("/audit_result_count")
    @Secured(AdminPermissions.CHART_MANAGE)
    @JsonView(JsonViews.AdminList::class)
    fun auditResultCount(year: Int): ApiResp {
        val calendar: Calendar = Calendar.getInstance();
        val sdf: SimpleDateFormat = SimpleDateFormat("yyyy-MM");//格式化为年月
        val currYear = calendar.get(Calendar.YEAR);
        val startTime: String? = year.toString() + "-01-01";
        val endTime: String? = year.toString() + "-12-31";
        val list = itxCommandService.auditResultCount(startTime, endTime)
        val data = mutableMapOf<String, MutableMap<String, Any>>();
 //            time      Pending   Pass   NoPass
//            2025-02	1323101	2618590	1
//            2025-01	8	18	0
        list.forEach {it->
            val tmp = mutableMapOf<String, Any>();
             tmp.put("Pending",it[1])
             tmp.put("Pass", it[2]);
             tmp.put("NoPass",it[3]);
            data.put(it[0].toString(),tmp);
        }

        return jsonOut(data);
    }

    @RequestMapping("/severity_level_ratio")
    @Secured(AdminPermissions.CHART_MANAGE)
    @JsonView(JsonViews.AdminList::class)
    fun severityLevelRatio(year: Int): ApiResp {
        val calendar: Calendar = Calendar.getInstance();
        val sdf: SimpleDateFormat = SimpleDateFormat("yyyy-MM");//格式化为年月
        val currYear = calendar.get(Calendar.YEAR);
        val startTime: String? = year.toString() + "-01-01";
        val endTime: String? = year.toString() + "-12-31";
        val baseQuery = BooleanBuilder()
        val list = itxCommandService.severityCategoryLevel(baseQuery, startTime, endTime);

//        数据库类	非常高   134
        return jsonOut(list);
    }


    @RequestMapping("/severity_level_count")
    @JsonView(JsonViews.AdminList::class)
    fun severityLevelCount(year: Int): ApiResp {
        return jsonOut(true);
    }

    @RequestMapping("/team_audit_count")
    @Secured(AdminPermissions.CHART_MANAGE)
    @JsonView(JsonViews.AdminList::class)
    fun teamAuditCount(
        @RequestParam("alertLevel") alertLevel: CommandRule.AlertLevel,
        @RequestParam("beginTime") beginTime: String,
        @RequestParam("endTime") endTime: String): ApiResp {
        if (StringUtils.isEmpty(beginTime)) {
            return jsonOut(1, "统计开始时间不能为空!")
        }
        if (StringUtils.isEmpty(endTime)) {
            return jsonOut(1, "统计结束时间不能为空!")
        }
        val list = itxCommandService.teamAuditCount(alertLevel,BaseEntity.DEFAULT_DATE_FORMAT.parse(beginTime), BaseEntity.DEFAULT_DATE_FORMAT.parse(endTime))
        var pageSize = list.size
        if (pageSize <= 0) {
            pageSize = 1;
        }
        val p = PageImpl(list, PageAttr(PageAttr.FIRST_NUM, pageSize.toLong()).get(), list.size.toLong());
        return jsonOut(p);
    }

}


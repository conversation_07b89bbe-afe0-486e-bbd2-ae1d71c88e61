package com.vsimtone.rirm2.backend.controller

import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.FileFolder
import com.vsimtone.rirm2.backend.entity.OptLog
import com.vsimtone.rirm2.backend.entity.QFileInfo
import com.vsimtone.rirm2.backend.service.FileFolderService
import com.vsimtone.rirm2.backend.service.FileInfoService
import javax.servlet.http.HttpServletRequest
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RequestMapping("file_infos")
@RestController
class FileInfoController : BaseController() {


    @Autowired
    lateinit var fileInfoService: FileInfoService

    @Autowired
    lateinit var folderService: FileFolderService

    @RequestMapping("")
    @Secured(AdminPermissions.SYS_CONFIG)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("folder", required = false) folder: Long?,
        @RequestParam("search", required = false) search: String?
    ): ApiResp {
        val a = QFileInfo.a
        var queries = BooleanBuilder()
        if (!StringUtils.isEmpty(search) || !StringUtils.isAllBlank())
            queries.andAnyOf(
                a.name.likeIgnoreCase("%$search%"),
                a.type.likeIgnoreCase("%$search%"),
                a.sha256sum.likeIgnoreCase("%$search%")
            );
        if (folder != null) {
            val fo = folderService.repo.findById(folder).get()
            queries.and(a.folder.path.like(fo.path + "%"))
        }
        return jsonOut(fileInfoService.repo.findAll(queries, pageAttr.get()));
    }

    @RequestMapping("upload", method = [RequestMethod.POST])
    @Secured(AdminPermissions.SYS_CONFIG)
    @ResponseBody
    @Transactional
    fun upload(
        req: HttpServletRequest,
        @RequestParam("file") file: MultipartFile,
        @RequestParam("folder", required = false) folder: Long?,
        @RequestParam("wangEditor", required = false) wangEditor: String?
    ): Any? {
        try {
            var f = fileInfoService.upload(file, folder ?: folderService.getRoot().id!!)
            if (wangEditor != null)
                return mapOf<String, Any>("errno" to 0L, "data" to arrayListOf("/api/files/" + f.path))
            addOptLog(OptLog.IMPORT, "excel上传", f.name, f);
            return jsonOut(f)
        } catch (e: Exception) {
            e.printStackTrace()
            return null;
        }
    }


    @RequestMapping("edit", method = [RequestMethod.POST])
    @Secured(AdminPermissions.SYS_CONFIG)
    @ResponseBody
    @Transactional
    fun edit(
        @RequestParam("id") id: Long,
        @RequestParam("name", required = false) name: String?,
        @RequestParam("folder", required = false) folder: Long?,
    ) {
        var f = fileInfoService.repo.findById(id).get();
        if (!StringUtils.isEmpty(name))
            f.name = name!!;
        if (folder != null) {
            f.folder = FileFolder()
            f.folder!!.id = folder
        }
        addOptLog(OptLog.EDIT, "excel修改", f.name, f);
        fileInfoService.repo.save(f);
    }


    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(AdminPermissions.SYS_CONFIG)
    fun delete(@PathVariable("id") id: Long): ApiResp {
        val data = fileInfoService.repo.findById(id).get();
        try {
            fileInfoService.repo.deleteById(id);
        } catch (e: java.lang.Exception) {
            return jsonOut(1, "禁止删除该文件");
        }
        addOptLog(OptLog.DELETE, "excel删除", data.name, data);
        return jsonOut(true);
    }

//    @RequestMapping("", method = [RequestMethod.POST])
//    @Transactional
//    fun modify(
//        @ModelAttribute("form") form: FileInfo
//    ): ApiResp {
//        var f = fileInfoService.repo.findById(form.id!!).get();
//        fileInfoService.repo.save(f);
//        return jsonOut(f);
//    }


}
package com.vsimtone.rirm2.backend.controller


import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.FileFolder
import com.vsimtone.rirm2.backend.entity.QFileFolder
import com.vsimtone.rirm2.backend.service.FileFolderService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RequestMapping("file_folders")
@RestController
class FileFolderController : BaseController() {

    @Autowired
    lateinit var fileFolderService: FileFolderService


    @Secured(AdminPermissions.SYS_CONFIG)
    @RequestMapping("")
    fun list(
        @RequestParam("parent", required = false) parent: Long?,
        @RequestParam("search", required = false) search: String?,
    ): ApiResp {
        val a = QFileFolder.a
        val queries = BooleanBuilder()
        if (parent != null)
            queries.and(a.parent.id.eq(parent))
        else if (StringUtils.isEmpty(search))
            queries.and(a.parent.isNull)
        else {
            return jsonOut(fileFolderService.repo.findAll(queries, PageAttr(PageAttr.FIRST_NUM, 15, null, false).get()).content.map {
                mapOf(
                    "names" to fileFolderService.getNames(it),
                    "path" to it.path,
                    "id" to it.id.toString()
                )
            })
        }
        return jsonOut(fileFolderService.repo.findAll(queries));
    }

    @Secured(AdminPermissions.SYS_CONFIG)
    @RequestMapping("getNamesByPath")
    fun getNamesByPath(@RequestParam("path") path: String): ApiResp {
        var names = mutableListOf<String>()
        path.split("|").forEach {
            if (!it.isEmpty())
                names.add(fileFolderService.repo.findById(it.toLong()).get().name)
        }
        return jsonOut(names);
    }

    @Secured(AdminPermissions.SYS_CONFIG)
    @RequestMapping("{id:\\d+}")
    fun view(@PathVariable("id") id: Long): ApiResp {
        return jsonOut(fileFolderService.repo.findById(id).get());
    }

    @Secured(AdminPermissions.SYS_CONFIG)
    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    fun delete(@PathVariable("id") id: Long): ApiResp {
        val folder = fileFolderService.repo.findById(id).get();
        try {
            fileFolderService.runInTran {
                fileFolderService.delFolder(folder);
            }
        } catch (e: Exception) {
            return jsonOut(1, "删除失败");
        }
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(AdminPermissions.SYS_CONFIG)
    @Transactional
    fun edit(@ModelAttribute("form") form: FileFolder): ApiResp {
        var data: FileFolder;
        if (form.id != null) {
            data = fileFolderService.repo.findById(form.id!!).get();
        } else {
            data = FileFolder();
            if (form.parent != null)
                form.parent = fileFolderService.repo.findById(form.parent!!.id!!).get()
            if (form.parent == null)
                return jsonOut(2, "请选择上级目录")
            data.parent = form.parent
        }

        val tmp = fileFolderService.repo.findByName(form.name);
        if(tmp !=null && tmp.id != data.id){
             return this.jsonOut(1, "目录名称不能重复!")
        }

        data.name = form.name
        fileFolderService.save(data);
        return jsonOut(data);
    }
}
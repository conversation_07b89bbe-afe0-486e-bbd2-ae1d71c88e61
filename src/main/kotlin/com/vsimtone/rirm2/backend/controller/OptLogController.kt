package com.vsimtone.rirm2.backend.controller

import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.QOptLog
import org.apache.commons.lang3.StringUtils
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.*
import java.security.Permissions
import java.util.*
import kotlin.text.trim

@RestController()
@RequestMapping("opt_logs")
class OptLogController : BaseController() {

    @Secured(AdminPermissions.OPT_LOG)
    @GetMapping("")
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("type", required = false) type: String?,
        @RequestParam("startTime", required = false) startTime: Date?,
        @RequestParam("endTime", required = false) endTime: Date?,
        @RequestParam("moduleName", required = false) moduleName: String?,
        @RequestParam("objName", required = false) objName: String?,
        @RequestParam("username", required = false) username: String?
    ): ApiResp {
        val q = QOptLog.a
        val queries = BooleanBuilder()
        if (!StringUtils.isEmpty(type))
            queries.andAnyOf(BooleanBuilder(q.type.eq(type)))
        if (startTime != null)
            queries.andAnyOf(BooleanBuilder(q.createdAt.gt(startTime)))
        if (endTime != null)
            queries.andAnyOf(BooleanBuilder(q.createdAt.lt(endTime)))
        if (!StringUtils.isEmpty(moduleName?.trim()))
            queries.andAnyOf(BooleanBuilder(q.moduleName.like("%$moduleName%")))
        if (!StringUtils.isEmpty(objName?.trim()))
            queries.andAnyOf(BooleanBuilder(q.objName.like("%$objName%")))
        if (!StringUtils.isEmpty(username?.trim()))
            queries.andAnyOf(BooleanBuilder(q.username.like("%$username%")))
        pageAttr.sort = arrayListOf("desc_id");
        return jsonOut(optLogService.findAll(queries, pageAttr))
    }
}
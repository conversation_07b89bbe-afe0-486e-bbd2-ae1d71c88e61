package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.fasterxml.jackson.databind.util.ArrayBuilders
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.*
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.ChangeFormHostService
import com.vsimtone.rirm2.backend.service.ChangeFormItemService
import com.vsimtone.rirm2.backend.service.ChangeFormItemStatsService
import com.vsimtone.rirm2.backend.service.ITXCommandService
import com.vsimtone.rirm2.backend.utils.RestUtil
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.ModelAttribute
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.*
import kotlin.collections.ArrayList

@RestController()
@RequestMapping("report_forms")
class ReportFormController : BaseController() {

    @Autowired
    lateinit var changeFormHostService: ChangeFormHostService

    @Autowired
    lateinit var changeFormItemService: ChangeFormItemService

    @Autowired
    lateinit var changeFormItemStatsService: ChangeFormItemStatsService

    @Autowired
    lateinit var itxCommandService: ITXCommandService

    @RequestMapping("/change_form_hosts")
    @Secured(AdminPermissions.REPORT_FORM_MANAGE)
    @JsonView(JsonViews.AdminList::class)
    fun hostList(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("startTime", required = false) startTime: Date?,
        @RequestParam("endTime", required = false) endTime: Date?,
        @RequestParam("typeCheck", required = false) typeCheck: ChangeFormItem.Companion.TypeCheck?
    ): ApiResp {
        val q = QChangeFormHost.a
        val queries = BooleanBuilder()
        queries.and((q.changeFormItem.isNull))
        if (!StringUtils.isEmpty(search)) {
            queries.and(q.changeFormId.like("%$search%").or(q.team.like("%$search%")))
        }
        if (startTime != null) {
            queries.and(q.beginTime.goe(startTime))
        }
        if (endTime != null) {
            queries.and(q.endTime.loe(endTime))
        }
        if(typeCheck !=null) {
            queries.and(q.changeFormItem.typeCheck.eq(typeCheck))
        }
        return jsonOut(changeFormHostService.repo.findAll(queries, pageAttr.get()));
    }


    @RequestMapping("/change_form_items")
    @Secured(AdminPermissions.REPORT_FORM_MANAGE)
    @JsonView(JsonViews.AdminList::class)
    fun itemList(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("startTime", required = false) startTime: Date?,
        @RequestParam("endTime", required = false) endTime: Date?,
        @RequestParam("typeCheck", required = false) typeCheck: ChangeFormItem.Companion.TypeCheck?
    ): ApiResp {
        val q = QChangeFormItem.a
        val queries = BooleanBuilder()
        val tmp: Long = 0L;
        queries.andAnyOf((q.hostCount.eq(tmp)))
        if (!StringUtils.isEmpty(search)) {
            queries.andAnyOf(q.formId.like("%$search%").or(q.team.like("%$search%")))
        }
        if (startTime != null) {
            queries.andAnyOf(q.planBeginTime.goe(startTime))
        }
        if (endTime != null) {
            queries.andAnyOf(q.planEndTime.loe(endTime))
        }
        if(typeCheck !=null) {
            queries.and(q.typeCheck.eq(typeCheck))
        }
        return jsonOut(changeFormItemService.repo.findAll(queries, pageAttr.get()));
    }

    @RequestMapping("/itx_host_audits")
    @Secured(AdminPermissions.REPORT_FORM_MANAGE, AdminPermissions.CHART_MANAGE)
    @JsonView(JsonViews.AdminList::class)
    fun itxHostAuditList(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("realUser", required = false) realUser: String?,
        @RequestParam("loginUser", required = false) loginUser: String?,
        @RequestParam("serverIP", required = false) serverIP: String?,
        @RequestParam("clientIP", required = false) clientIP: String?,
        @RequestParam("changeFormId", required = false) changeFormId: String?,
        @RequestParam("categoryId", required = false) categoryId: Long?,
        @RequestParam("alertLevel", required = false) alertLevel: Array<CommandRule.AlertLevel>?,
        @RequestParam("startTime", required = false) startTime: Date?,
        @RequestParam("endTime", required = false) endTime: Date?,
        @RequestParam("flagAuditResult", required = false) flagAuditResult: Boolean?,
    ): ApiResp {
        val q = QITXCommand.a
        val queries = BooleanBuilder()
        queries.and(q.rule.alertLevel.`in`(CommandRule.AlertLevel.Normal, CommandRule.AlertLevel.High))
        if (!StringUtils.isEmpty(search)) {
            queries.and(q.changeFormItem.formId.like("%$search%"))
        }
        if (!StringUtils.isEmpty(realUser)) {
            queries.and(q.session.realUser.eq(realUser))
        }
        if (!StringUtils.isEmpty(loginUser)) {
            queries.and(q.session.loginUser.eq(loginUser))
        }
        if (!StringUtils.isEmpty(serverIP)) {
            queries.and(q.session.serverIP.eq(serverIP))
        }
        if (!StringUtils.isEmpty(clientIP)) {
            queries.and(q.session.clientIP.eq(clientIP))
        }
        if (!StringUtils.isEmpty(changeFormId)) {
            queries.and(q.changeFormItem.formId.eq(changeFormId))
        }
        if (categoryId != null) {
            queries.and(q.rule.commandCategory.id.eq(categoryId))
        }
        if (alertLevel != null && alertLevel.isNotEmpty()) {
            queries.and(q.rule.alertLevel.`in`(alertLevel.toList()))
        }
        if (startTime != null) {
            queries.and(q.execTime.goe(startTime))
        }
        if (endTime != null) {
            queries.and(q.execTime.loe(endTime))
        }
        if (flagAuditResult != null) {
            if (flagAuditResult) queries.and(q.auditResult.isNotNull)
            else queries.and(q.auditResult.isNull)
        }
        val p = itxCommandService.repo.findAll(queries, pageAttr.get());
        p.content.forEach {
            RestUtil.unwrapBean(it.changeFormItem)
            RestUtil.unwrapBean(it.session)
            RestUtil.unwrapBean(it.rule)
        }
        return jsonOut(p);
    }

    @RequestMapping("/audit_change_form_items")
    @Secured(AdminPermissions.REPORT_FORM_MANAGE)
    @JsonView(JsonViews.AdminList::class)
    fun auditItemList(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("hostCount", required = false) hostCount: Long?,
        @RequestParam("cmdCount", required = false) cmdCount: Long?,
        @RequestParam("itxCmdTotalCount", required = false) itxCmdTotalCount: Long?,
        @RequestParam("startTime", required = false) startTime: Date?,
        @RequestParam("endTime", required = false) endTime: Date?,
        @RequestParam("typeCheck", required = false) typeCheck: ChangeFormItem.Companion.TypeCheck?
    ): ApiResp {
        val q = QChangeFormItem.a
        val queries = BooleanBuilder()
        queries.and(QChangeFormItem.a.hostCount.gt(0))
        if (!StringUtils.isEmpty(search)) {
            queries.andAnyOf(q.formId.like("%$search%").or(q.team.like("%$search%")).or(q.name.like("%$search%")))
        }
        if (hostCount != null) {
            if (hostCount.toInt() == 1) queries.andAnyOf(q.hostCount.gt(0));
            else queries.andAnyOf(q.hostCount.eq(0));
        }

        if (cmdCount != null) {
            if (cmdCount.toInt() == 1) queries.andAnyOf(q.cmdCount.gt(0));
            else queries.andAnyOf(q.cmdCount.eq(0));
        }

        if (itxCmdTotalCount != null) {
            if (itxCmdTotalCount.toInt() == 1) queries.andAnyOf(q.itxCmdTotalCount.gt(0));
            else queries.andAnyOf(q.itxCmdTotalCount.eq(0));
        }

        if (startTime != null) {
            queries.andAnyOf(q.planBeginTime.goe(startTime))
        }
        if (endTime != null) {
            queries.andAnyOf(q.planEndTime.loe(endTime))
        }
        if(typeCheck !=null) {
            queries.and(q.typeCheck.eq(typeCheck))
        }

        val p = changeFormItemService.findAll(queries, pageAttr)
        val keys = mutableListOf<String>()
        keys.add(this.changeFormItemService.getStatsKey(null, ITXCommand.Companion.MatchResult.Unmatched, null, null))
        keys.add(this.changeFormItemService.getStatsKey(null, null, ITXCommand.Companion.AuditResult.NoPass, ITXCommand.Companion.ProcessResult.UnProcess))
//        keys.add(this.changeFormItemService.getStatsKey(CommandRule.AlertLevel.High, null, ITXCommand.Companion.AuditResult.Pending, null))
//        keys.add(this.changeFormItemService.getStatsKey(CommandRule.AlertLevel.Normal, null, ITXCommand.Companion.AuditResult.Pending, null))
        keys.add(this.changeFormItemService.getStatsKey(null, null, ITXCommand.Companion.AuditResult.NoPass, ITXCommand.Companion.ProcessResult.Confirmed))
        keys.add(this.changeFormItemService.getStatsKey(null, null, ITXCommand.Companion.AuditResult.Pass, ITXCommand.Companion.ProcessResult.Confirmed))
        keys.add(this.changeFormItemService.getStatsKey(null, null, null, ITXCommand.Companion.ProcessResult.AutoConfirmed))
        keys.add(this.changeFormItemService.getStatsKey(null, null, null, ITXCommand.Companion.ProcessResult.Confirmed))
        keys.add(this.changeFormItemService.getStatsKey(null, null, null, ITXCommand.Companion.ProcessResult.UnProcess))
        // 更多
        keys.add(this.changeFormItemService.getStatsKey(null, null, ITXCommand.Companion.AuditResult.Pending, null))
        p.content.forEach { item ->
            val stats = mutableMapOf<String, Long>()
            for (key in keys) {
                stats[key] = 0
            }
            item.putBundle("stats", stats);
        }
        this.changeFormItemStatsService.findAll(QChangeFormItemStats.a.changeFormItem.id.`in`(*p.content.map { it.id!! }.toTypedArray()).and(QChangeFormItemStats.a.statsKey.`in`(keys))).forEach { s ->
            val item = p.content.find { it.id == s.changeFormItem!!.id }!!
            val stats = (item.getBundle()!!["stats"] as MutableMap<String, Long>)
            stats[s.statsKey!!] = s.statsVal!!;
        }
        return jsonOut(p);
    }

    @RequestMapping("/command_category_process")
    @Secured(AdminPermissions.CHART_MANAGE)
    @JsonView(JsonViews.AdminList::class)
    fun commandCategoryProcess(
        @ModelAttribute("page") pageAttr: PageAttr, @RequestParam("beginTime") beginTime: String, @RequestParam("endTime") endTime: String
    ): ApiResp {
        val data = ArrayList<MutableMap<String, Any>>();
        val list = itxCommandService.commandCategoryProcess(beginTime, endTime);
        list.forEach {
            val tmp = mutableMapOf<String, Any>();
//            name  commandTotal AutoConfirmed UnProcess  Confirmed
//            登陆登出类	1	8	0
//            提交作业	137	17	0

            tmp.put("name", it[0]);
            tmp.put("commandTotal", it[1])
            tmp.put("AutoConfirmed", it[2]);
            tmp.put("UnProcess", it[3]);
            tmp.put("Confirmed", it[4]);
            data.add(tmp);
        }
        return jsonOut(data);
    }

}
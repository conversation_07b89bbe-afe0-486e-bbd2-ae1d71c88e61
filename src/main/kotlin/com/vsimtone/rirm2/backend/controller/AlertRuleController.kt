package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.*
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.repository.AlertRecordRepository
import com.vsimtone.rirm2.backend.repository.AlertRuleRepository
import com.vsimtone.rirm2.backend.service.AlertRuleService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RestController()
@RequestMapping("alert_rules")
class AlertRuleController : BaseController() {

    @Autowired
    lateinit var service: AlertRuleService

    @Autowired
    lateinit var alertRecordRepository: AlertRecordRepository

    @Secured(AdminPermissions.ALERT_INFO)
    @RequestMapping("")
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("alertLevel", required = false) alertLevel: Array<CommandRule.AlertLevel>?,
    ): ApiResp {
        val q = QAlertRule.a
        val queries = BooleanBuilder()
        if (!StringUtils.isEmpty(search)) {
            queries.andAnyOf(q.alertTitle.like("%$search%"))
        }
        if (alertLevel != null && alertLevel.isNotEmpty()) {
            queries.andAnyOf(q.alertLevel.`in`(alertLevel.toList()))
        }
        return jsonOut(service.repo.findAll(queries, pageAttr.get()));
    }

    @Secured(AdminPermissions.ALERT_INFO)
    @RequestMapping("{id:\\d+}")
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get()
        addOptLog(OptLog.VIEW, "告警规则", data.alertTitle!!, data);
        return jsonOut(data);
    }

    @Secured(AdminPermissions.ALERT_INFO)
    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @JsonView(JsonViews.AdminView::class)
    fun delete(@PathVariable("id") id: Long): ApiResp {
        if (alertRecordRepository.count(QAlertRecord.a.alertRule.id.eq(id)) > 0) {
            return this.jsonOut(1, "有告警记录，不允许删除")
        }
        val data = service.repo.findById(id).get()
        service.repo.deleteById(id)
        addOptLog(OptLog.DELETE, "告警规则", data.alertTitle!!, data);
        return jsonOut(true);
    }

    @Secured(AdminPermissions.ALERT_INFO)
    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun edit(@ModelAttribute("form") form: AlertRule): ApiResp {
        var data: AlertRule;
        if (form.id != null) {
            data = service.repo.findById(form.id!!).get();
        } else {
            data = AlertRule();
        }
        data.enabled = form.enabled
        data.alertTitle = form.alertTitle;
        data.alertBody = form.alertBody;
        data.alertLevel = form.alertLevel;
        if (form.commandCategory != null && form.commandCategory!!.id != null)
            data.commandCategory = form.commandCategory;
        else
            data.commandCategory = null;
        data.commandRule = form.commandRule;
        data.mailReceivers = form.mailReceivers;
        data.serverIP = form.serverIP;
        service.repo.save(data);
        if (form.id != null)
            addOptLog(OptLog.EDIT, "告警规则", data.alertTitle!!, data);
        else
            addOptLog(OptLog.ADD, "告警规则", data.alertTitle!!, data);
        return jsonOut(true);
    }


}

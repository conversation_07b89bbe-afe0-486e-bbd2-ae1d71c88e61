package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.*
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.AdminRoleService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RestController()
@RequestMapping("admin_roles")
class AdminRoleController : BaseController() {

    @Autowired
    lateinit var service: AdminRoleService;

    @RequestMapping("")
    @Secured(AdminPermissions.USER_ROLE)
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam(value = "roleName", required = false) roleName: String?,
    ): ApiResp {
        val q = QAdminRole.a;
        val queries = BooleanBuilder()
        if (!StringUtils.isEmpty(roleName)){
            queries.andAnyOf(BooleanBuilder(q.roleName.like("%$roleName%")))
        }
        val p = service.repo.findAll(queries, pageAttr.get());
        return jsonOut(p)
    }


    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(AdminPermissions.USER_ROLE)
    @JsonView(JsonViews.AdminView::class)
    fun delete(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        try {
            service.repo.deleteById(id)
        } catch (e: Exception) {
            if (e.message != null && e.message!!.indexOf("ORA-02292") != -1) {
                return this.jsonOut(1, "该角色信息不能够删除!")
            }
            throw e;
        }
        addOptLog(OptLog.DELETE, "角色管理", data.roleName, data);
        return jsonOut(true);
    }


    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(AdminPermissions.USER_ROLE)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun edit(@ModelAttribute("form") form: AdminRole): ApiResp {
        var data: AdminRole;
        if (form.id != null) {
            data = service.repo.findById(form.id!!).get();
        } else {
            data = AdminRole();
        }

        val tmp = service.repo.findOneByRoleName(form.roleName)
        if (tmp != null && tmp.id != data.id) {
            return this.jsonOut(1, "角色名称不能重复!")
        }
        data.roleName = form.roleName;
        data.permissions = form.permissions;
        service.repo.save(data);
        if (form.id != null)
            addOptLog(OptLog.EDIT, "角色管理", data.roleName, data);
        else
            addOptLog(OptLog.ADD, "角色管理", data.roleName, data);
        return jsonOut(true);
    }


    @RequestMapping("{id:\\d+}")
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get()
        return jsonOut(data);
    }
}
package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.CommandCategory
import com.vsimtone.rirm2.backend.entity.OptLog
import com.vsimtone.rirm2.backend.entity.QCommandCategory
import com.vsimtone.rirm2.backend.entity.QCommandRule
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.CommandCategoryService
import com.vsimtone.rirm2.backend.service.CommandRuleService
import com.vsimtone.rirm2.backend.service.IDGeneratorService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RestController()
@RequestMapping("command_categories")
class CommandCategoryController : BaseController() {

    @Autowired
    lateinit var service: CommandCategoryService

    @Autowired
    lateinit var idGeneratorService: IDGeneratorService

    @Autowired
    lateinit var commandRuleService: CommandRuleService

    @RequestMapping("")
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @RequestParam("parent", required = false) parent: Long?,
        @RequestParam("search", required = false) search: String?,
    ): ApiResp {
        val queries = BooleanBuilder();
        val a = QCommandCategory.a;
        if (parent != null)
            queries.and(a.parent.id.eq(parent))
        else if (StringUtils.isEmpty(search))
            queries.and(a.parent.isNull)
        else {
            return jsonOut(service.repo.findAll(queries, PageAttr(PageAttr.FIRST_NUM, 15, null, false).get()).content.map {
                mapOf(
                    "names" to service.getNames(it),
                    "path" to it.path,
                    "id" to it.id.toString()
                )
            })
        }

        return jsonOut(service.repo.findAll(queries));
    }

    @RequestMapping("{id:\\d+}")
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        addOptLog(OptLog.VIEW, "命令分类", data.name, data);
        return jsonOut(data);
    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(AdminPermissions.CMD_RULE)
    @JsonView(JsonViews.AdminView::class)
    fun delete(@PathVariable("id") id: Long): ApiResp {
        var lists: List<CommandCategory>;
        lists = service.repo.findByParentId(id);
        for(tmp in lists){
            var tmpRule = commandRuleService.repo.findAll(QCommandRule.a.commandCategory.eq(tmp),PageAttr(PageAttr.FIRST_NUM,1).get()).firstOrNull()
            if(tmpRule !=null){
                return jsonOut(1,"该数据下的子项数据已被其它地方引用，不能删除该数据!");
            }
            service.repo.delete(tmp)
        }

        var category:  CommandCategory = service.repo.findById(id!!).get();
        var tmpRule = commandRuleService.repo.findAll(QCommandRule.a.commandCategory.eq(category),PageAttr(PageAttr.FIRST_NUM,1).get()).firstOrNull()
        if(tmpRule !=null){
            return jsonOut(1,"该数据已被其它地方引用，不能删除该数据!");
        }
        service.repo.delete(category)
        addOptLog(OptLog.DELETE, "命令分类", category.name, category);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(AdminPermissions.CMD_RULE)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun edit(@ModelAttribute("form") form: CommandCategory): ApiResp {
        var data: CommandCategory;
        if (form.id != null) {
            data = service.repo.findById(form.id!!).get();
        } else {
            data = CommandCategory();
            if (form.parent != null) {
                form.parent = service.repo.findById(form.parent!!.id!!).get()
            }
            data.parent = form.parent;
        }
        val tmp = service.repo.findByName(form.name)
        if(tmp !=null && tmp.id != data.id) {
            return this.jsonOut(1, "命令分类不能重复!")
        }
        data.name = form.name;
        data.remark = form.remark;
        service.repo.save(data);
        data.path = updatePath(data);
        service.repo.save(data);
        if (form.id != null)
            addOptLog(OptLog.EDIT, "命令分类", data.name, data);
        else
            addOptLog(OptLog.ADD, "命令分类", data.name, data);
        return this.jsonOut(data)
    }

    fun updatePath(data: CommandCategory): String {
        if (data.parent == null)
            data.path = data.id.toString() + "|";
        else
            data.path = data.parent!!.path.toString() + data.id.toString() + "|";
        return data.path;
    }

}

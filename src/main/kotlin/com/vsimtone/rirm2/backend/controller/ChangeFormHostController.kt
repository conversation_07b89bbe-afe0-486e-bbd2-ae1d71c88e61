package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.ChangeFormHost
import com.vsimtone.rirm2.backend.entity.OptLog
import com.vsimtone.rirm2.backend.entity.QChangeFormHost
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.ChangeFormHostService
import com.vsimtone.rirm2.backend.service.ChangeFormItemService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import java.util.*

@RestController()
@RequestMapping("change_form_hosts")
class ChangeFormHostController : BaseController() {

    @Autowired
    lateinit var service: ChangeFormHostService

    @Autowired
    lateinit var changeFormItemService: ChangeFormItemService

    @RequestMapping("")
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("changeFormItem", required = false) changeFormItem: Long?,
        @RequestParam("startTime", required = false) startTime: Date?,
        @RequestParam("endTime", required = false) endTime: Date?,
    ): ApiResp {
        val q = QChangeFormHost.a
        val queries = BooleanBuilder()
        if (!StringUtils.isEmpty(search)){
            queries.andAnyOf(BooleanBuilder(q.changeFormId.like("%$search%").or(q.team.like("%$search%").or(q.idOpsName.like("%$search%")).or(q.idOpsNumber.like("%$search%")))))
        }
        if (changeFormItem != null) {
            queries.andAnyOf(q.changeFormItem.id.eq(changeFormItem));
        }
        if(startTime != null) {
            queries.andAnyOf(q.beginTime.goe(startTime))
        }
        if(endTime != null) {
            queries.andAnyOf(q.endTime.loe(endTime))
        }
        return jsonOut(service.repo.findAll(queries, pageAttr.get()));
    }

    @RequestMapping("{id:\\d+}")
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        addOptLog(OptLog.VIEW, "控制日志", data.applyId, data);
        return jsonOut(data);
    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    @JsonView(JsonViews.AdminView::class)
    fun delete(@PathVariable("id") id: Long): ApiResp {
        var tmp = service.repo.findById(id).get()
        service.repo.deleteById(id)
        changeFormItemService.addToMatchQueue(tmp.changeFormId)
        addOptLog(OptLog.DELETE, "控制日志", tmp.applyId, tmp);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun edit(@ModelAttribute("form") form: ChangeFormHost): ApiResp {
        var data: ChangeFormHost;
        if (form.id != null) {
            data = service.repo.findById(form.id!!).get();
        } else {
            data = ChangeFormHost();
        }
        service.repo.save(data);
        if (form.id != null)
            addOptLog(OptLog.EDIT, "控制日志", data.applyId, data);
        else
            addOptLog(OptLog.ADD, "控制日志", data.applyId, data);
        return jsonOut(data);
    }


}

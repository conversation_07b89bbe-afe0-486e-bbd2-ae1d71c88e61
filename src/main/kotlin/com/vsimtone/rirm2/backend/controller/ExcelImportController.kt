package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.ExcelImport
import com.vsimtone.rirm2.backend.entity.OptLog
import com.vsimtone.rirm2.backend.entity.QExcelImport
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.ExcelImportService
import com.vsimtone.rirm2.backend.service.FileInfoService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController()
@RequestMapping("excel_imports")
class ExcelImportController : BaseController() {

    @Autowired
    lateinit var service: ExcelImportService

    @Autowired
    lateinit var fileService: FileInfoService

    @RequestMapping("defines")
    @Secured(AdminPermissions.SYS_CONFIG)
    @JsonView(JsonViews.AdminView::class)
    fun defines(): ApiResp {
        return jsonOut(service.excelDefines.map {
            mapOf<String, Any?>(
                "name" to it.name,
                "key" to it.cls.simpleName,
                "columns" to it.columns.map {
                    mapOf<String, Any?>(
                        "name" to it.name,
                        "key" to it.field.name,
                        "required" to it.required
                    )
                }
            )
        });
    }

    @RequestMapping("")
    @Secured(AdminPermissions.EXCEL_IMPORT)
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("status", required = false) status: Array<ExcelImport.Status>?
    ): ApiResp {
        val q = QExcelImport.a
        val queries = BooleanBuilder()
        if (!StringUtils.isEmpty(search)) {
            queries.andAnyOf(q.file.name.like("%$search%").or(q.defineName.like("%$search%")))
        }
        if (status != null && status.size > 0) {
            queries.andAnyOf(q.status.`in`(status.toList()));
        }
        return jsonOut(service.repo.findAll(queries, pageAttr.get()));
    }

    @RequestMapping("{id:\\d+}")
    @Secured(AdminPermissions.EXCEL_IMPORT)
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        addOptLog(OptLog.ADD, "excel导入", data.file.name, data);
        return jsonOut(data);
    }

    @PostMapping("import_now")
    @Secured(AdminPermissions.EXCEL_IMPORT)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun importNow(): ApiResp {
        addOptLog(OptLog.IMPORT, "excel执行导入", "立即导入", "");
        return jsonOut(service.importAll());
    }


    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(AdminPermissions.EXCEL_IMPORT)
    @JsonView(JsonViews.AdminView::class)
    fun delete(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        try {
            service.runInTran {
                service.doDelete(data)
            }
        } catch (e: Throwable) {
            return jsonOut(1, "错误：" + (e.cause ?: e).message)
        }
        addOptLog(OptLog.DELETE, "excel删除", data.file.name, data);
        return jsonOut(true);
    }

    @RequestMapping(value = ["upload"], method = [RequestMethod.POST])
    @Secured(AdminPermissions.EXCEL_IMPORT)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun upload(
        @RequestParam("file") file: MultipartFile
    ): ApiResp {
        val data = ExcelImport();
        val fileInfo = fileService.upload(file, service.folder().id)
        if (service.repo.findByFile(fileInfo) != null)
            return jsonOut(1, file.originalFilename + " 已存在!");
        fileInfo.disableDelete = true;
        fileService.repo.save(fileInfo);
        data.file = fileInfo;
        service.repo.save(data);
        addOptLog(OptLog.IMPORT, "excel上传", data.file.name, data);
        return jsonOut(true);
    }

    @RequestMapping(value = ["{id:\\d+}/reset_import"], method = [RequestMethod.POST])
    @Secured(AdminPermissions.EXCEL_IMPORT)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun resetImport(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        data.status = ExcelImport.Status.Pending;
        service.repo.save(data);
        addOptLog(OptLog.IMPORT, "excel重导入", data.file.name, data);
        return jsonOut(service.importAll())
    }

}

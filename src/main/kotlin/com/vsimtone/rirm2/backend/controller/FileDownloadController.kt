package com.vsimtone.rirm2.backend.controller

import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.OptLog
import com.vsimtone.rirm2.backend.service.APPConfigService
import com.vsimtone.rirm2.backend.service.FileFolderService
import com.vsimtone.rirm2.backend.service.FileInfoService
import javax.servlet.http.HttpServletResponse
import org.apache.commons.io.FilenameUtils
import org.apache.commons.io.IOUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.web.server.MimeMappings
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.io.EOFException
import java.net.URLEncoder
import java.nio.file.attribute.FileTime
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

@RequestMapping("files")
@RestController
class FileDownloadController : BaseController() {


    @Autowired
    lateinit var fileInfoService: FileInfoService

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var fileFolderService: FileFolderService


    @RequestMapping("zip")
    @Secured(AdminPermissions.SYS_CONFIG)
    fun downloadZip(
        @RequestParam("tokens") tokens: Array<String>,
        resp: HttpServletResponse
    ): Any? {
        resp.setHeader("Cache-Control", "max-age=0")
        resp.setHeader("Access-Control-Allow-Origin", "*")
        resp.contentType = MimeMappings.DEFAULT.get("zip");
        val encodeFilename =
            URLEncoder.encode(tokens.size.toString() + "_" + System.currentTimeMillis() + ".zip", "UTF-8")
        resp.setHeader(
            "Content-Disposition",
            "attachment; filename=\"\${$encodeFilename}\"; filename*=utf-8''$encodeFilename"
        )
        val bufLen = appConfigService.getLong(FileInfoService.CNF_FILE_DOWNLOAD_SPEED)!!.toInt()
        val entryList = mutableListOf<String>()
        val z = ZipOutputStream(resp.outputStream)
        z.use {
            for (token in tokens)
                try {
                    val f = fileInfoService.findByToken(token) ?: continue;
                    val inputStream = fileInfoService.getInputStream(f.path) ?: continue;
                    inputStream.use {
                        val time = FileTime.fromMillis(f.createdAt!!.time)
                        val names = mutableListOf<String>()
                        f.folder!!.path.split("|").forEach {
                            if (it.isNotEmpty()) {
                                val folder = fileFolderService.repo.findById(it.toLong()).get()
                                if (folder.parent != null)
                                    names.add(folder.name)
                            }
                        }
                        names.add(f.name)
                        var name = names.joinToString("/")
                        var i = 1;
                        if (entryList.contains(name))
                            while (true) {
                                if (entryList.contains(name + ".${i}"))
                                    i++;
                                else {
                                    name += ".${i}"
                                    break
                                }
                            }
                        entryList.add(name);
                        z.putNextEntry(
                            ZipEntry(name).setCreationTime(time).setLastAccessTime(time).setLastModifiedTime(time)
                        )
                        val buf = ByteArray(bufLen)
                        while (true) {
                            val t = System.currentTimeMillis()
                            val len = IOUtils.read(inputStream, buf)
                            if (len <= 0) break
                            z.write(buf, 0, len)
                            z.flush()
                            val st = System.currentTimeMillis() - t
                            if (st < 1000)
                                Thread.sleep(1000 - st)
                        }
                        z.closeEntry();
                    }
                } catch (e: Exception) {
                    if (e !is EOFException)
                        e.printStackTrace()
                    resp.sendError(500, "Zip File error");
                }
        }
        return null;
    }


    @RequestMapping("{token:\\w+\\.\\w{1,5}}")
    @Secured(AdminPermissions.SYS_CONFIG, AdminPermissions.CHANGE_FORM_MANAGE, AdminPermissions.EXCEL_IMPORT)
    fun download(
        @PathVariable("token") token: String,
        resp: HttpServletResponse,
        @RequestParam("v", required = false) v: Boolean?
    ): Any? {
        resp.setHeader("Cache-Control", "max-age=864000")
        resp.setHeader("Access-Control-Allow-Origin", "*")
        try {
            var f = fileInfoService.repo.findByToken(token) ?: return jsonOut(404, "file not found");
            var inputStream = fileInfoService.getInputStream(f.path) ?: return jsonOut(404, "file not found");
            inputStream.use {
                resp.contentType = MimeMappings.DEFAULT.get(FilenameUtils.getExtension(f.path));
                if (v == null || v == false) {
                    val encodeFilename = URLEncoder.encode(f.name, "UTF-8")
                    resp.setHeader(
                        "Content-Disposition",
                        "attachment; filename=\"\${$encodeFilename}\"; filename*=utf-8''$encodeFilename"
                    )
                }
                if (inputStream.available() != 0)
                    resp.setContentLength(inputStream.available())
                else if (f.size != 0L) {
                    resp.setContentLength(f.size.toInt())
                }
                var ros = resp.outputStream
                var bufLen = appConfigService.getLong(FileInfoService.CNF_FILE_DOWNLOAD_SPEED)!!.toInt()
                var buf = ByteArray(bufLen)
                while (true) {
                    var t = System.currentTimeMillis()
                    var len = IOUtils.read(it, buf)
                    if (len <= 0) break
                    ros.write(buf, 0, len)
                    ros.flush()
                    var st = System.currentTimeMillis() - t
                    if (st < 1000)
                        Thread.sleep(1000 - st)
                }
            }
            addOptLog(OptLog.DOWNLOAD, "excel下载", f.name, f);
        } catch (e: Exception) {
            if (!(e is EOFException))
                e.printStackTrace()
            resp.sendError(500, "File error");
        }
        return null;
    }

}
package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.*
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.AlertRecordService
import com.vsimtone.rirm2.backend.service.AlertRuleService
import com.vsimtone.rirm2.backend.service.ChangeFormItemService
import com.vsimtone.rirm2.backend.service.CommandRuleService
import com.vsimtone.rirm2.backend.service.ExcelImportService
import com.vsimtone.rirm2.backend.service.FileInfoService
import com.vsimtone.rirm2.backend.service.ITXCommandService
import com.vsimtone.rirm2.backend.service.ITXEventQueueService
import com.vsimtone.rirm2.backend.service.ITXSessionService
import com.vsimtone.rirm2.backend.utils.AppUtils
import com.vsimtone.rirm2.backend.utils.JSONUtils
import com.vsimtone.rirm2.backend.utils.TimeStats
import javax.servlet.http.HttpServletResponse
import org.apache.commons.compress.archivers.ArchiveInputStream
import org.apache.commons.compress.archivers.ArchiveStreamFactory
import org.apache.commons.compress.compressors.CompressorStreamFactory
import org.apache.commons.io.FilenameUtils
import org.apache.commons.io.IOUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.jpa.domain.AbstractPersistable_.id
import org.springframework.http.MediaType
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.util.unit.DataSize
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import java.io.ByteArrayInputStream
import java.io.InputStream
import java.util.*
import java.util.zip.GZIPInputStream
import kotlin.concurrent.thread

@RestController()
@RequestMapping("itx_sessions")
class ITXSessionController : BaseController() {

    @Autowired
    lateinit var service: ITXSessionService

    @Autowired
    lateinit var itxCommandService: ITXCommandService

    @Autowired
    lateinit var fileService: FileInfoService

    @Autowired
    lateinit var itxEventQueueService: ITXEventQueueService

    @Autowired
    lateinit var changeFormItemService: ChangeFormItemService

    @Autowired
    lateinit var alertRecordService: AlertRecordService

    @Autowired
    lateinit var excelImportService: ExcelImportService

    @Autowired
    lateinit var commandRuleService: CommandRuleService

    @Autowired
    lateinit var alertRuleService: AlertRuleService

    @RequestMapping("")
    @Secured(AdminPermissions.ITX_COMMAND)
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("type", required = false) type: ITXSession.Type?,
        @RequestParam("startTime", required = false) startTime: Date?,
        @RequestParam("endTime", required = false) endTime: Date?,
    ): ApiResp {
        val q = QITXSession.a
        val queries = BooleanBuilder()
        if (!StringUtils.isEmpty(search)) {
            queries.andAnyOf(q.clientIP.eq(search).or(q.serverIP.eq(search)).or(q.loginUser.eq(search)).or(q.realUser.eq(search)).or(q.sessionId.eq(search)))
        }
        if (type != null) {
            queries.andAnyOf(q.type.eq(type))
        }
        if (startTime != null) {
            queries.and(q.beginTime.goe(startTime))
        }
        if (endTime != null) {
            queries.and(q.endTime.loe(endTime))
        }
        val p = service.repo.findAll(queries, pageAttr.get())
        return jsonOut(p);
    }

    @RequestMapping("{id:\\d+}")
    @Secured(AdminPermissions.ITX_COMMAND)
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        addOptLog(OptLog.VIEW, "会话", data.sessionId, data);
        return jsonOut(data);
    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(AdminPermissions.SUPER_DELETER)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun delete(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        val changeFormIds = itxCommandService.query()
            .select(QITXCommand.a.changeFormItem.id)
            .where(QITXCommand.a.session.id.eq(id).and(QITXCommand.a.changeFormItem.isNotNull))
            .groupBy(QITXCommand.a.changeFormItem.id).fetch()
        alertRecordService.batchDelete(QAlertRecord.a.sess.id.eq(id));
        itxCommandService.batchDelete(QITXCommand.a.session.id.eq(id));
        service.batchDelete(QITXSession.a.id.eq(id));
        service.repo.flush()
        changeFormIds.forEach { id ->
            changeFormItemService.lazyUpdateStats(id)
        }
        addOptLog(OptLog.DELETE, "会话", data.sessionId, data);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(AdminPermissions.ITX_COMMAND)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun edit(@ModelAttribute("form") form: ITXSession): ApiResp {
        var data: ITXSession;
        if (form.id != null) {
            data = service.repo.findById(form.id!!).get();
        } else {
            data = ITXSession();
        }
        service.repo.save(data);
        if (form.id != null)
            addOptLog(OptLog.EDIT, "会话", data.sessionId, data);
        else
            addOptLog(OptLog.ADD, "会话", data.sessionId, data);
        return jsonOut(data);
    }

    data class ImportFileInfo(
        val name: String, val type: String, val data: List<List<String?>>, val columnsColIdx: MutableMap<String, Int> = mutableMapOf<String, Int>()
    );
    fun getColStr(imtFile: ImportFileInfo, row: Int, columnKey: String, allowEmpty: Boolean = false): String {
        val column = imtFile.columnsColIdx[columnKey]!!
        var rowDate = imtFile.data[row]
        val v = rowDate[column]?.toString()?.trim()
        if (!allowEmpty && StringUtils.isEmpty(v)) error("第${column + 1}列不能为空")
        return v ?: ""
    }

    @RequestMapping(value = ["import"], method = [RequestMethod.POST], produces = [MediaType.TEXT_EVENT_STREAM_VALUE])
    @Secured(AdminPermissions.EXECL_CMD_IMPORT)
    @JsonView(JsonViews.AdminView::class)
    fun import(response: HttpServletResponse, @RequestParam("upfile") upfile: MultipartFile): SseEmitter {
        this.logger.info("itx session upload start: ${upfile.originalFilename} ${upfile.size}")
        val sseEmitter = SseEmitter(6 * 3600 * 1000L); // 最多执行6小时
        val sseEnd = fun(code: Int, _data: Any) {
            this.logger.info("itx session upload done: ${JSONUtils.toString(_data)}")
            var data: Any? = null
            if (code != 0) data = this.jsonOut(code, _data as String?)
            else data = this.jsonOut(_data)
            val evt = SseEmitter.event().name("result").data(JSONUtils.toString(data)).build()
            sseEmitter.send(evt)
            sseEmitter.complete()
        }
        var curStep = 1;
        val stepTotal = 5;
        var lastSseProgressTime = -1L;
        val sseProgress = fun(msg: String, progressValue: Int, progressTotal: Int, canIgnore: Boolean) {
            if (canIgnore && lastSseProgressTime != -1L && progressValue != 0 && progressValue < progressTotal - 1) {
                if (System.currentTimeMillis() - lastSseProgressTime <= 5 * 1000) return;
            }
            if (canIgnore) lastSseProgressTime = System.currentTimeMillis()
            else lastSseProgressTime = -1L
            val data = mutableMapOf<String, Any>(
                "steps" to "$curStep/$stepTotal", "progress" to 0, "progressValue" to progressValue, "progressTotal" to progressTotal, "msg" to msg
            );
            if (progressValue > 0 && progressTotal > 0) {
                data["progress"] = progressValue * 10000 / progressTotal;
            }
            this.logger.info("itx session upload progress: ${JSONUtils.toString(data)}")
            val evt = SseEmitter.event().name("progress").data(JSONUtils.toString(data)).build()
            sseEmitter.send(evt)
        }
        thread run@{
            var errorCount = 0;
            var successCount = 0;
            var repeatCount = 0;
            val imtErrMessages = mutableListOf<String>()
            var tooManyErrorsException = RuntimeException("too many errors.");
            try {
                if (upfile.size >= 1024 * 1024 * 100) { // 最大100MB
                    return@run sseEnd(1, "文件${upfile.name}过大")
                }
                sseProgress("解析文件中", 0, 0, false);
                var extName = FilenameUtils.getExtension(upfile.originalFilename);
                val importFiles = mutableListOf<ImportFileInfo>()
                val addFile = fun(name: String, s: InputStream): Boolean {
                    val extName = FilenameUtils.getExtension(name)
                    if (!name.startsWith("session.") && !name.startsWith("command.")) {
                        return false;
                    }
                    if (extName == "xlsx" || extName == "xls") {
                        sseProgress("解析文件: $name", 0, 0, false);
                        val xlsData = AppUtils.readExcel(s)
                        val type = name.split(".").first()
                        importFiles.add(ImportFileInfo(name, type, xlsData))
                        return true;
                    }
                    return false
                }
                if (extName == "zip" || extName == "tar" || extName == "tgz") {
                    var upfileData = IOUtils.toByteArray(upfile.inputStream)
                    if (extName == "tgz") {
                        sseProgress("GZ解压中", 0, 0, false);
                        upfileData = GZIPInputStream(ByteArrayInputStream(upfileData)).readBytes()
                        extName = "tar"
                    }
                    val archiveInputStream: ArchiveInputStream<*> = ArchiveStreamFactory().createArchiveInputStream(extName, ByteArrayInputStream(upfileData))
                    archiveInputStream.use {
                        var entry = archiveInputStream.nextEntry
                        while (entry != null) {
                            if (entry.isDirectory) {
                                entry = archiveInputStream.nextEntry
                                continue
                            }
                            if (entry.size >= 1024 * 1024 * 100) { // 最大100MB
                                return@run sseEnd(1, "文件${entry.name}过大")
                            }
                            if (entry.size <= 0) {
                                return@run sseEnd(1, "文件${entry.name}无法解压")
                            }
                            sseProgress("解压文件: ${entry.name} ${entry.size}", 0, 0, false);
                            val buffer = ByteArray(entry.size.toInt())
                            IOUtils.readFully(archiveInputStream, buffer)
                            addFile(entry.name, ByteArrayInputStream(buffer))
                            entry = archiveInputStream.nextEntry
                        }
                    }
                } else {
                    if (!addFile(upfile.originalFilename, upfile.inputStream)) return@run sseEnd(1, "不支持的格式，请上传xls|xlsx|zip|tar|tgz类型的文件, excel文件需以session或者command开头")
                }
                if (importFiles.isEmpty()) {
                    return@run sseEnd(1, "未读取到excel，请上传xls|xlsx|zip|tar|tgz类型的文件, excel文件需以session或者command开头")
                }
                curStep++;
                val commandColumnTitle = arrayOf("sessionId:会话ID", "execTime:执行时间", "cmd:命令", "screenSEQ:屏幕序号")
                val sessionColumnTitle = arrayOf("beginTime:开始时间", "endTime:结束时间", "type:类型", "clientIP:客户端IP", "serverIP:服务端IP", "loginUser:登陆用户", "realUser:真实用户", "sessionId:会话ID")
                importFiles.forEachIndexed { fileIdx, imtFile ->
                    sseProgress("校验文件: " + imtFile.name, fileIdx, importFiles.size, false);
                    val xlsData = imtFile.data
                    val columns = xlsData[0]
                    val titles = mutableListOf<String>()
                    if (imtFile.type == "session") titles.addAll(sessionColumnTitle)
                    if (imtFile.type == "command") titles.addAll(commandColumnTitle)
                    titles.forEach { colIt ->
                        val kv = colIt.split(Regex(":"), 2)
                        val idx = columns.indexOf(kv[1])
                        if (idx == -1) return@run sseEnd(1, "文件${imtFile.name}: 未发现列 ${kv[1]}")
                        imtFile.columnsColIdx[kv[0]] = idx
                    }
                }
                val commandFiles = importFiles.filter { it.type == "command" }
                val sessionFiles = importFiles.filter { it.type == "session" }
                return@run this.service.runInTran {
                    val locked = this.excelImportService.importLockRun("itxSessionController@import", 60) lockRun@{
                        curStep++;
                        val itxCommands = mutableListOf<ITXCommand>()
                        val itxSessions = mutableMapOf<String, ITXSession>()
                        val cmdIdCache = mutableMapOf<String, Long>()
                        var importedSessionLines = 0;
                        val totalSessionLines = sessionFiles.sumOf { it.data.size - 1 }
                        sessionFiles.forEachIndexed { imtIdx, imtFile ->
                            val xlsData = imtFile.data
                            var timeStats = TimeStats()
                            xlsData.forEachIndexed { i, row ->
                                if (i == 0) return@forEachIndexed
                                sseProgress("导入会话(${imtIdx + 1}/${sessionFiles.size}): " + imtFile.name + "", importedSessionLines++, totalSessionLines, true);
                                try {
                                    timeStats.reset();
                                    var beginTime = getColStr(imtFile, i, "beginTime")
                                    var endTime = getColStr(imtFile, i, "endTime")
                                    var type = getColStr(imtFile, i, "type")
                                    var clientIP = getColStr(imtFile, i, "clientIP")
                                    var serverIP = getColStr(imtFile, i, "serverIP")
                                    var loginUser = getColStr(imtFile, i, "loginUser")
                                    var realUser = getColStr(imtFile, i, "realUser")
                                    var sessionId = getColStr(imtFile, i, "sessionId")
                                    var itxSession = itxSessions[sessionId] ?: service.repo.findOne(QITXSession.a.sessionId.eq(sessionId)).orElseGet { null } ?: ITXSession()
                                    timeStats.point("t1");
                                    val isNew = itxSession.id == null
                                    if (isNew) {
                                        timeStats.reset();
                                        itxSession.beginTime = AppUtils.xlsToDate(beginTime)
                                        itxSession.endTime = AppUtils.xlsToDate(endTime)
                                        if (type == "大机") itxSession.type = ITXSession.Type.BOC
                                        else if (type.lowercase() == "linux") itxSession.type = ITXSession.Type.Linux
                                        else if (type.lowercase() == "aix") itxSession.type = ITXSession.Type.AIX
                                        else error("未知的类型: $type")
                                        itxSession.clientIP = clientIP
                                        itxSession.serverIP = serverIP
                                        itxSession.loginUser = loginUser
                                        itxSession.realUser = realUser
                                        itxSession.sessionId = sessionId
                                        service.repo.save(itxSession)
                                        timeStats.point("t2");
                                    }
                                    timeStats.reset()
                                    itxSessions[sessionId] = itxSession
                                    if (!isNew) {
                                        itxCommandService.findAll(QITXCommand.a.session.eq(itxSession)).forEach { cmdIdCache[it.cmdId] = it.id!! }
                                        repeatCount++
                                    } else {
                                        successCount++
                                    }
                                    timeStats.point("t3");
                                } catch (e: Exception) {
                                    errorCount++
                                    imtErrMessages.add("文件${imtFile.name}第${i + 1}行错误:" + (e.message ?: "空指针"))
                                    if (errorCount >= 10) throw tooManyErrorsException
                                }
                            }
                            this.logger.info("upload process ${imtFile.name}:${imtFile.data.size} time stats: ${timeStats}")
                            this.logger.info("upload count succ=${successCount}, err=${errorCount}, repeat=${repeatCount}")
                        }
                        curStep++;
                        var importedCommandLines = 0;
                        val totalCommandLines = commandFiles.sumOf { it.data.size - 1 }
                        commandFiles.forEachIndexed { imtIdx, imtFile ->
                            val xlsData = imtFile.data
                            var timeStats = TimeStats()
                            xlsData.forEachIndexed { i, row ->
                                if (i == 0) return@forEachIndexed
                                sseProgress("导入命令(${imtIdx + 1}/${commandFiles.size}): " + imtFile.name, importedCommandLines++, totalCommandLines, true);
                                try {
                                    timeStats.reset();
                                    var sessionId = getColStr(imtFile, i, "sessionId")
                                    var execTime = getColStr(imtFile, i, "execTime")
                                    var cmd = getColStr(imtFile, i, "cmd")
                                    var screenSEQ = getColStr(imtFile, i, "screenSEQ", true)
                                    var sessionInfo = itxSessions[sessionId] ?: service.repo.findOne(QITXSession.a.sessionId.eq(sessionId)).orElseGet { null }
                                    if (sessionInfo == null) error("会话ID'$sessionId'不存在")
                                    if (itxSessions[sessionId] == null) {
                                        itxSessions[sessionId] = sessionInfo
                                        itxCommandService.findAll(QITXCommand.a.session.eq(sessionInfo)).forEach { cmdIdCache[it.cmdId] = it.id!! }
                                    }
                                    var itxCommand = ITXCommand();
                                    itxCommand.loginUser = sessionInfo.loginUser
                                    itxCommand.session = sessionInfo
                                    itxCommand.execTime = AppUtils.xlsToDate(execTime)
                                    itxCommand.cmd = cmd
                                    if (!StringUtils.isAllEmpty(screenSEQ)) itxCommand.replayInfo = screenSEQ
                                    if (sessionInfo.replayInfo == null) sessionInfo.replayInfo = itxCommand.replayInfo
                                    var itxCommandId = itxCommandService.getCmdId(itxCommand)
                                    timeStats.point("t1");
                                    if (cmdIdCache[itxCommandId] != null) {
                                        repeatCount++;
                                        return@forEachIndexed
                                    }
                                    itxCommand.cmdId = itxCommandId
                                    timeStats.point("t2");
                                    itxCommandService.repo.save(itxCommand)
                                    successCount++
                                    cmdIdCache[itxCommandId] = itxCommand.id!!
                                    itxCommands.add(itxCommand)
                                    sessionInfo.commandCount++
                                    timeStats.point("t3");
                                } catch (e: Exception) {
                                    errorCount++
                                    imtErrMessages.add("文件${imtFile.name}第${i + 1}行错误:" + (e.message ?: "空指针"))
                                    if (errorCount >= 20) throw tooManyErrorsException
                                }
                            }
                            this.logger.info("upload process ${imtFile.name}:${imtFile.data.size} time stats: ${timeStats}")
                            this.logger.info("upload count succ=${successCount}, err=${errorCount}, repeat=${repeatCount}")
                        }
                        curStep++;
                        var timeStats = TimeStats()
                        itxSessions.values.forEach {
                            service.repo.save(it)
                        }
                        timeStats.point("t1");
                        sseProgress("后处理", 0, 0, false)
                        val commandRules = commandRuleService.repo.findAll(QCommandRule.a.enabled.isTrue).toList();
                        val alertRules = alertRuleService.repo.findAll(QAlertRule.a.enabled.isTrue).toList();
                        itxEventQueueService.newCommands(commandRules, alertRules, itxCommands)
                        timeStats.point("t2");
                        service.repo.flush();
                        timeStats.point("t3");
                        sseProgress("成功", 0, 0, false)
                        this.logger.info("upload end process time stats ${timeStats}")
                        sseEnd(0, mutableMapOf("count" to mutableMapOf("success" to successCount, "error" to errorCount, "repeat" to repeatCount), "msg" to imtErrMessages))
                    }
                    if (!locked) {
                        sseEnd(1, "导入锁定失败,请稍后再试.")
                    }
                }
            } catch (e: Exception) {
                if (e == tooManyErrorsException) {
                    sseEnd(1, "错误过多.\n${imtErrMessages.joinToString("\n")}")
                } else {
                    this.logger.error("upload error", e)
                    sseEnd(500, "Upload exception")
                }
            }
        }
        return sseEmitter
        addOptLog(OptLog.IMPORT, "会话", upfile.name, upfile);
    }


}

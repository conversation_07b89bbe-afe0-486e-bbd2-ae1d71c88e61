package com.vsimtone.rirm2.backend.controller

import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PerfMonStats
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.service.PerfMonService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("perf_mon")
class PerfMonController : BaseController() {

    @Autowired
    lateinit var perfMonService: PerfMonService

    @Secured(AdminPermissions.SYS_MONITOR)
    @RequestMapping("")
    fun list(
        @RequestParam("begin", required = false) begin: Long?,
        @RequestParam("end", required = false) end: Long?,
        @RequestParam("target") target: String,
        @RequestParam("name") name: String,
        @RequestParam("size", required = false) size: Long?
    ): ApiResp {
        val limit = size ?: 1440;
        val content = mutableListOf<PerfMonStats>()
        perfMonService.findAll(target, name, true) {
            if (content.size >= limit)
                return@findAll false;
            if (begin != null && it.time < begin) {
                return@findAll true;
            }
            if (end != null && it.time > end) {
                return@findAll false;
            }
            content.add(it)
            return@findAll true;
        }
        return jsonOut(content)
    }

}
package com.vsimtone.rirm2.backend.controller


import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.service.AdminUserService
import com.vsimtone.rirm2.backend.service.OptLogService
import com.vsimtone.rirm2.backend.utils.Log
import javax.servlet.http.HttpServletRequest
import javax.servlet.http.HttpServletResponse
import javax.servlet.http.HttpSession
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.propertyeditors.CustomDateEditor
import org.springframework.web.bind.ServletRequestDataBinder
import org.springframework.web.bind.annotation.InitBinder
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import java.text.DateFormat
import java.text.SimpleDateFormat
import java.util.*


open class BaseController {
    protected open var logger = Log.get(this.javaClass.simpleName)

    @Autowired
    lateinit var optLogService: OptLogService;

    @Autowired
    lateinit var userService: AdminUserService;

    fun addOptLog(
        type: String,
        moduleName: String,
        objName: String,
        _details: Any?
    ) {
        val user = userService.currUser()
        optLogService.addOptLog(user?.username, type, moduleName, objName, _details)
    }

    @InitBinder
    @Throws(Exception::class)
    protected open fun initBinder(request: HttpServletRequest?, binder: ServletRequestDataBinder) {
        val format: DateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
        val dateEditor = CustomDateEditor(format, true)
        binder.registerCustomEditor(Date::class.java, dateEditor)
    }

    open fun getAttribute(attributeName: String?): Any? {
        return getRequest().getAttribute(attributeName)
    }

    open fun setAttribute(attributeName: String?, `object`: Any?) {
        getRequest().setAttribute(attributeName, `object`)
    }

    open fun getSession(attributeName: String?): Any? {
        return getRequest().getSession(true).getAttribute(attributeName)
    }

    open fun setSession(attributeName: String?, `object`: Any?) {
        getRequest().getSession(true).setAttribute(attributeName, `object`)
    }

    open fun getRequest(): HttpServletRequest {
        val ra = RequestContextHolder.getRequestAttributes()
        return (ra as ServletRequestAttributes).request
    }

    open fun getResponse(): HttpServletResponse {
        val ra = RequestContextHolder.getRequestAttributes()
        return (ra as ServletRequestAttributes).response
    }

    open fun getSession(): HttpSession? {
        return getRequest().getSession(true)
    }


    open fun getHeader(headerName: String?): String? {
        return getRequest().getHeader(headerName)
    }

    open fun allowCrossDomainAccess() {
        val servletResponse = getResponse()
        servletResponse.setHeader("Access-Control-Allow-Origin", "*")
        servletResponse.setHeader("Access-Control-Allow-Methods", "POST,GET")
    }

    fun jsonOut(data: Any?): ApiResp {
        var rp = ApiResp(data);
        return rp
    }

    fun jsonOut(code: Int, data: String?): ApiResp {
        var rp = ApiResp(code, data);
        return rp
    }
}
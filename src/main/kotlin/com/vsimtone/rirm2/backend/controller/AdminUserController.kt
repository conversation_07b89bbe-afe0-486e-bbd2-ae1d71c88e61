package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.annotation.OptLogAudit
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.*
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.APPConfigService
import com.vsimtone.rirm2.backend.service.AdminUserService
import com.vsimtone.rirm2.backend.service.ITXCommandService
import javax.servlet.http.HttpSession
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.security.web.csrf.DefaultCsrfToken
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RestController()
@RequestMapping("admin_users")
class AdminUserController : BaseController() {

    @Autowired
    lateinit var adminUserService: AdminUserService

    @Autowired
    lateinit var appConfigService: APPConfigService

    @Autowired
    lateinit var itxCommandService: ITXCommandService

    @Secured(AdminPermissions.MANAGER)
    @RequestMapping("")
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("orgPath", required = false) orgPath: String?,
        @RequestParam("enabled", required = false) enabled: Boolean?,
        @RequestParam("locked", required = false) locked: Boolean?,
    ): ApiResp {
        val q = QAdminUser.a
        val queries = BooleanBuilder()
        if (!StringUtils.isEmpty(search))
            queries.andAnyOf(BooleanBuilder(q.nickname.like("%$search%")).or(q.username.like("%$search%")))
        if (!StringUtils.isEmpty(orgPath))
            queries.and(q.org.path.like("$orgPath%"));
        if (enabled != null)
            queries.andAnyOf(q.enabled.eq(enabled))
        if (locked != null)
            queries.andAnyOf(q.locked.eq(locked))
        val p = adminUserService.repo.findAll(queries, pageAttr.get())
        p.content.forEach {
            if (it.username == "admin") {
                it.permissions = AdminPermissions.getAll().toMutableList()
            }
        }
        return jsonOut(p);
    }

    @RequestMapping("curr")
    fun curr(session: HttpSession): ApiResp {
        val user = adminUserService.currUser()
        val result = HashMap<String, Any?>()
        result["user"] = user
        if (user != null) {
            result["config"] = mutableMapOf(
                "intellinx_host" to appConfigService.getString(APPConfigService.CNF_INTELLINX_WEB_HOST),
                "intellinx_port" to appConfigService.getInteger(APPConfigService.CNF_INTELLINX_WEB_PORT)
            )
            result["user_firstLogin"]= adminUserService.findByUsername(user.username)!!.firstLogin
        }
        return jsonOut(result)
    }


    @Secured(AdminPermissions.MANAGER)
    @RequestMapping("{id:\\d+}")
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        val u = adminUserService.repo.findById(id).get()
        if (u.username == "admin") {
            u.permissions = AdminPermissions.getAll().toMutableList()
        }
        addOptLog(OptLog.VIEW, "管理员", u.username, u);
        return jsonOut(u);
    }


    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(AdminPermissions.MANAGER)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun delete(@PathVariable("id") id: Long): ApiResp {
        val data = adminUserService.repo.findById(id).get()
        if (data.username == "admin")
            return jsonOut(1, "不能删除管理员");
        var tmpCommand = itxCommandService.repo.findAll(QITXCommand.a.auditUser.id.eq(data.id), PageAttr(PageAttr.FIRST_NUM, 1).get()).firstOrNull()
        if (tmpCommand != null) {
            return jsonOut(1, "该用户进行了命令的审计工作，不能删除该用户!");
        }
        adminUserService.repo.deleteById(id)
        addOptLog(OptLog.DELETE, "管理员", data.username, data);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(AdminPermissions.MANAGER)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun edit(@ModelAttribute("form") form: AdminUser): ApiResp {
        var data: AdminUser;
        if (form.id != null) {
            data = adminUserService.repo.findById(form.id!!).get();
        } else {
            data = AdminUser();
            data.firstLogin = true;
        }
        val tmp = adminUserService.repo.findOneByUsername(form.username)
        if (tmp != null && tmp.id != data.id) {
            return this.jsonOut(1, "用户名不能重复!")
        }
        if (data.id == null || data.username != "admin")
            data.username = form.username;
        data.nickname = form.nickname;
        data.enabled = form.enabled;
        data.permissions = form.permissions;
        data.role = form.role;
        data.locked = form.locked;
        data.org = form.org;
        if (data.username == "admin") data.permissions = AdminPermissions.getAll().toMutableList()
        if (data.locked) data.loginFailCount = 0;
        if (!StringUtils.isEmpty(form.password)) data.password = adminUserService.passwordEncoder.encode(form.password);
        adminUserService.repo.save(data);
        if (form.id != null)
            addOptLog(OptLog.EDIT, "管理员", data.username, data);
        else
            addOptLog(OptLog.ADD, "管理员", data.username, data);
        return jsonOut(data.id);
    }

    @RequestMapping(value = ["edit_pwd"], method = [RequestMethod.POST])
    @Transactional
    @JsonView(JsonViews.AdminView::class)
    fun editPwd(
        @RequestParam("oldPassword") oldPassword: String,
        @RequestParam("password") password: String
    ): ApiResp {
        var currUser = adminUserService.currUser();
        var data = userService.repo.findById(currUser!!.id).get();
        if (userService.checkPassword(data!!, oldPassword) != null)
            return jsonOut(1, "旧密码错误，不能进行修改")
        if (!StringUtils.isEmpty(password)) {
            var pwdRule = "密码不符合规则：长度8-32位，必须含一个大写字母，一个小写字母，一个数字";
            if (password.length < 8)
                return jsonOut(1, pwdRule)
            if (!password.matches(Regex(".*[A-Z]+.*")))
                return jsonOut(1, pwdRule)
            if (!password.matches(Regex(".*[a-z]+.*")))
                return jsonOut(1, pwdRule)
            if (!password.matches(Regex(".*[0-9]+.*")))
                return jsonOut(1, pwdRule)
            data.password = userService.passwordEncoder.encode(password);
        }
        adminUserService.repo.save(data);
        addOptLog("修改密码", "用户", data.username, data);
        return jsonOut(true);
    }


    @RequestMapping(value = ["setloginData"], method = [RequestMethod.POST])
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun setFirstLoginData(@RequestParam("username") username: String): ApiResp{
        val data = adminUserService.findByUsername(username)
        data!!.firstLogin = false;
        adminUserService.repo.save(data);
        return jsonOut(true)
    }

}
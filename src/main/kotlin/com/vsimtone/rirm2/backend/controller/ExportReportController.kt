package com.vsimtone.rirm2.backend.controller

import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.entity.OptLog
import org.springframework.web.bind.annotation.*

@RestController()
@RequestMapping("export_report")
class ExportReportController : BaseController() {

    @RequestMapping("done", method = arrayOf(RequestMethod.POST))
    fun done(
        @RequestParam(value = "module", required = true) module: String,
        @RequestParam(value = "obj", required = true) objName: String,
        @RequestParam(value = "details", required = true) details: String,
    ): ApiResp {
        addOptLog(OptLog.EXPORT, module, objName, details)
        return jsonOut(true);
    }
}

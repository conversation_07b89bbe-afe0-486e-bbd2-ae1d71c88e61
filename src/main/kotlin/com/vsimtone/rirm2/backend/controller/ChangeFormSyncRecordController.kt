package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.ChangeFormSyncRecordService
import com.vsimtone.rirm2.backend.entity.QChangeFormSyncRecord
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.web.bind.annotation.ModelAttribute
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.*


@RestController()
@RequestMapping("changeFormSyncRecords")
class ChangeFormSyncRecordController: BaseController() {

    @Autowired
    lateinit var service: ChangeFormSyncRecordService;

    @RequestMapping("")
    @Secured(AdminPermissions.SYS_CONFIG)
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("startTime", required = false) startTime: Date?,
        @RequestParam("endTime", required = false) endTime: Date?,
    ): ApiResp {
        val q = QChangeFormSyncRecord.a
        val queries = BooleanBuilder()

        if(startTime != null) {
            queries.andAnyOf(q.syncTimeBegin.goe(startTime))
        }
        if(endTime != null) {
            queries.andAnyOf(q.syncTimeEnd.loe(endTime))
        }
        return jsonOut(service.repo.findAll(queries, pageAttr.get()));
    }
}
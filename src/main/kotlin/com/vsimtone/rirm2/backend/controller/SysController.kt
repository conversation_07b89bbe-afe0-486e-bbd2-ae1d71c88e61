package com.vsimtone.rirm2.backend.controller

import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.service.SysAlertService
import com.vsimtone.rirm2.backend.service.SysService
import javax.servlet.http.HttpServletRequest
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageImpl
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.security.Permissions
import kotlin.text.get

@RestController()
@RequestMapping("sys")
class SysController : BaseController() {

    @Autowired
    lateinit var sysAlertService: SysAlertService

    @Autowired
    @org.springframework.context.annotation.Lazy
    lateinit var sysService: SysService

    @RequestMapping("server_nodes")
    @Secured(AdminPermissions.SYS_MONITOR)
    fun serverNodes(): ApiResp {
        return jsonOut(
            PageImpl(
                sysService.serverNodes.readAllValues().toMutableList(),
                PageAttr().get(),
                sysService.serverNodes.size.toLong()
            )
        )
    }

    @RequestMapping("alerts")
    @Secured(AdminPermissions.SYS_MONITOR)
    fun alerts(request: HttpServletRequest): ApiResp {
        request.setAttribute("SKIP_LOG", true)
        return jsonOut(sysAlertService.sysAlerts)
    }

}

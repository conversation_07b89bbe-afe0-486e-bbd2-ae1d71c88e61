package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.AlertRecord
import com.vsimtone.rirm2.backend.entity.CommandRule
import com.vsimtone.rirm2.backend.entity.OptLog
import com.vsimtone.rirm2.backend.entity.QAlertRecord
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.AlertRecordService
import com.vsimtone.rirm2.backend.utils.AppUtils
import com.vsimtone.rirm2.backend.utils.RestUtil
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RestController()
@RequestMapping("alert_records")
class AlertRecordController : BaseController() {

    @Autowired
    lateinit var service: AlertRecordService

    @Secured(AdminPermissions.ALERT_INFO)
    @RequestMapping("")
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("alertRule", required = false) alertRule: Long?,
        @RequestParam("mailSendStatus", required = false) mailSendStatus: Array<AlertRecord.MailSendStatus>?,
        @RequestParam("alertLevel", required = false) alertLevel: Array<CommandRule.AlertLevel>?,
        @RequestParam("processed", required = false) processed: Boolean?,
    ): ApiResp {
        val q = QAlertRecord.a
        val queries = BooleanBuilder()
        if (!StringUtils.isEmpty(search)) {
            queries.andAnyOf(q.alertRule.alertTitle.like("%$search%").or(q.alertRule.mailReceivers.like("%$search%")))
        }
        if (alertRule != null) {
            queries.andAnyOf(q.alertRule.id.eq(alertRule))
        }
        if (mailSendStatus != null && mailSendStatus.isNotEmpty()) {
            queries.andAnyOf(q.mailSendStatus.`in`(mailSendStatus.toList()))
        }
        if(alertLevel !=null)
            queries.andAnyOf(q.cmdRule.alertLevel.`in`(alertLevel.toList()))
        if(processed !=null)
            queries.andAnyOf(q.processed.eq(processed))
        val p = service.repo.findAll(queries, pageAttr.get());
        p.content.forEach {
            RestUtil.unwrapBean(it.cmd.changeFormItem)
        }
        return jsonOut(p);
    }

    @Secured(AdminPermissions.ALERT_INFO)
    @RequestMapping("{id:\\d+}")
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        addOptLog(OptLog.VIEW, "告警记录", data.cmdRule!!.name, data);
        return jsonOut(data);
    }

    @Secured(AdminPermissions.ALERT_INFO)
    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @JsonView(JsonViews.AdminView::class)
    fun delete(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        service.repo.deleteById(id)
        addOptLog(OptLog.DELETE, "告警记录", data.cmdRule!!.name, data);
        return jsonOut(true);
    }

    @Secured(AdminPermissions.ALERT_INFO)
    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun edit(@ModelAttribute("form") form: AlertRecord): ApiResp {
        var data: AlertRecord;
        if (form.id != null) {
            data = service.repo.findById(form.id!!).get();
        } else {
            data = AlertRecord();
        }
        service.repo.save(data);
        if (form.id != null)
            addOptLog(OptLog.EDIT, "告警记录", data.cmdRule!!.name, data);
        else
            addOptLog(OptLog.ADD, "告警记录", data.cmdRule!!.name, data);
        return jsonOut(data);
    }


    @Secured(AdminPermissions.ALERT_INFO)
    @RequestMapping("doProcessed", method = [RequestMethod.POST])
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun doProcessed(@RequestParam("id") ids: Array<Long>): ApiResp {
        ids.forEach {
            val data = service.findById(it)
            data.processed = true;
            service.repo.save(data)
        }
        addOptLog(OptLog.EDIT, "告警记录", "批量处理告警记录", ids);
        return jsonOut(true);
    }


}

package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.ChangeFormCommand
import com.vsimtone.rirm2.backend.entity.OptLog
import com.vsimtone.rirm2.backend.entity.QChangeFormCommand
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.ChangeFormCommandService
import com.vsimtone.rirm2.backend.service.ChangeFormItemService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RestController()
@RequestMapping("change_form_commands")
class ChangeFormCommandController : BaseController() {

    @Autowired
    lateinit var service: ChangeFormCommandService

    @Autowired
    lateinit var changeFormItemService: ChangeFormItemService

    @RequestMapping("")
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("changeFormItem", required = false) changeFormItem: Long?
    ): ApiResp {
        val q = QChangeFormCommand.a
        val queries = BooleanBuilder()
        if (!StringUtils.isEmpty(search)) {
            queries.andAnyOf(BooleanBuilder(q.changeFormId.like("%$search%")))
        }
        if (changeFormItem != null) {
            queries.andAnyOf(q.changeFormItem.id.eq(changeFormItem));
        }

        return jsonOut(service.repo.findAll(queries, pageAttr.get()));
    }

    @RequestMapping("{id:\\d+}")
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get()
        addOptLog(OptLog.VIEW, "实施方案", data.command, data);
        return jsonOut(data);
    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @JsonView(JsonViews.AdminView::class)
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    fun delete(@PathVariable("id") id: Long): ApiResp {
        var tmp = service.repo.findById(id).get()
        service.repo.deleteById(id)
        changeFormItemService.addToMatchQueue(tmp.changeFormId)
        addOptLog(OptLog.DELETE, "实施方案", tmp.command, tmp);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @JsonView(JsonViews.AdminView::class)
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    @Transactional
    fun edit(@ModelAttribute("form") form: ChangeFormCommand): ApiResp {
        var data: ChangeFormCommand;
        if (form.id != null) {
            data = service.repo.findById(form.id!!).get();
        } else {
            data = ChangeFormCommand();
        }
        service.repo.save(data);
        if (form.id != null)
            addOptLog(OptLog.EDIT, "实施方案", data.command, data);
        else
            addOptLog(OptLog.ADD, "实施方案", data.command, data);
        return jsonOut(data);
    }


}

package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.RestException
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.APPConfigService
import com.vsimtone.rirm2.backend.service.ChangeFormSyncService
import com.vsimtone.rirm2.backend.service.WXTSyncCommandService
import com.vsimtone.rirm2.backend.utils.JSONUtils
import org.apache.http.client.utils.URIUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.net.URI
import java.net.URL


@RestController()
@RequestMapping("appconfigs")
class APPConfigController {
    @Autowired
    private lateinit var appConfigService: APPConfigService

    companion object {
        const val PASSWORD_HIDDEN_STRING = "************";
    }

    @RequestMapping("list")
    @Secured(AdminPermissions.SYS_CONFIG)
    @JsonView(JsonViews.AdminList::class)
    fun list(@RequestParam("key") key: Array<String>): ApiResp {
        var data = appConfigService.search(*key).toMutableMap()
        data.forEach {
            if (it.key == WXTSyncCommandService.CNF_KEY_API_PASS) {
                data[it.key] = PASSWORD_HIDDEN_STRING;
            }
            if (it.key == ChangeFormSyncService.CNF_KEY_API_CONFIG) {
                val m = (it.value!! as Map<String, Any>).toMutableMap()
                m.forEach {
                    if (it.value != null && it.value is String) {
                        m[it.key] = it.value.toString().replace(Regex("token=[^&]*", RegexOption.IGNORE_CASE), "token=${PASSWORD_HIDDEN_STRING}")
                    }
                }
                data[it.key] = m
            }
        }
        return ApiResp(data)
    }


    @RequestMapping("set", method = [RequestMethod.POST])
    @Transactional
    @Secured(AdminPermissions.SYS_CONFIG)
    fun set(
        @RequestParam("key") key: List<String>,
        @RequestParam("val") `val`: List<String>,
        @RequestParam("type") type: List<String>
    ): ApiResp {
        key.forEachIndexed { index, s ->
            var v = `val`[index].trim()
            val k = key[index]
            val t = type[index]
            if (k == "__ignore__") return@forEachIndexed
            if (k == WXTSyncCommandService.CNF_KEY_API_PASS && v.matches(Regex("^\\*+$", RegexOption.IGNORE_CASE))) {
                throw RestException(1, "访问控制系统同步接口密码不合法")
            }
            if (k == ChangeFormSyncService.CNF_KEY_API_CONFIG) {
                val old = this.appConfigService.getMap(ChangeFormSyncService.CNF_KEY_API_CONFIG) ?: mapOf<String, Any>()
                val v2 = JSONUtils.toObject(v, Map::class.java).toMutableMap()
                v2.forEach {
                    if (old[it.key] != null && it.value != null && it.value is String) {
                        val oldToken = Regex("token=[^&]+", RegexOption.IGNORE_CASE).find(old[it.key]!!.toString())?.value
                        val newToken = Regex("token=\\*+", RegexOption.IGNORE_CASE).find(it.value.toString())?.value
                        if (newToken != null && oldToken != null) {
                            v2[it.key] = it.value!!.toString().replace(newToken, oldToken)
                        }
                    }
                }
                v = JSONUtils.toString(v2);
            }
            appConfigService.setConfig(k, v, t)
        }
        return ApiResp(true);
    }

}

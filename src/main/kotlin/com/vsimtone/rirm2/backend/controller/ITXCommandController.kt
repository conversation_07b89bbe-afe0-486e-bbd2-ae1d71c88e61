package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.*
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.AdminUserService
import com.vsimtone.rirm2.backend.service.ChangeFormItemService
import com.vsimtone.rirm2.backend.service.ITXCommandService
import com.vsimtone.rirm2.backend.service.ITXEventQueueService
import com.vsimtone.rirm2.backend.service.ITXSessionService
import com.vsimtone.rirm2.backend.utils.AppUtils
import com.vsimtone.rirm2.backend.utils.JSONUtils
import com.vsimtone.rirm2.backend.utils.RestUtil

import org.apache.commons.compress.archivers.ArchiveInputStream
import org.apache.commons.compress.archivers.ArchiveStreamFactory
import org.apache.commons.compress.compressors.CompressorStreamFactory
import org.apache.commons.io.FilenameUtils
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter
import java.io.FileOutputStream
import java.io.InputStream
import java.util.*
import kotlin.collections.mutableMapOf

@RestController()
@RequestMapping("itx_commands")
class ITXCommandController : BaseController() {

    @Autowired
    lateinit var service: ITXCommandService

    @Autowired
    lateinit var itxSessionService: ITXSessionService

    @Autowired
    lateinit var changeFormItemService: ChangeFormItemService

    @Autowired
    lateinit var adminUserService: AdminUserService

    @RequestMapping("")
    @Secured(AdminPermissions.ITX_COMMAND, AdminPermissions.REPORT_FORM_MANAGE)
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("session", required = false) session: Long?,
        @RequestParam("changeFormItem", required = false) changeFormItem: Long?,
        @RequestParam("isMatched", required = false) isMatched: Boolean?,
        @RequestParam("auditResult", required = false) auditResult: Array<ITXCommand.Companion.AuditResult>?,
        @RequestParam("processResult", required = false) processResult: Array<ITXCommand.Companion.ProcessResult>?,
        @RequestParam("type", required = false) type: ITXSession.Type?,
        @RequestParam("alertLevel", required = false) alertLevel: CommandRule.AlertLevel?,
        @RequestParam("categoryPath", required = false) categoryPath: String?,
        @RequestParam("matchCommandRule", required = false) matchCommandRule: Boolean?,
        @RequestParam("matchChangeForm", required = false) matchChangeForm: Boolean?,
        @RequestParam("startTime", required = false) startTime: Date?,
        @RequestParam("endTime", required = false) endTime: Date?,
        @RequestParam("cmd", required = false) cmd: String?,
        @RequestParam("serverIP", required = false) serverIP: String?,
        @RequestParam("clientIP", required = false) clientIP: String?,
        @RequestParam("loginUser", required = false) loginUser: String?,
        @RequestParam("auditUser", required = false) auditUser: String?
    ): ApiResp {
        val q = QITXCommand.a
        val queries = BooleanBuilder()
        if (!StringUtils.isEmpty(search)) {
            queries.andAnyOf(q.session.sessionId.eq("%$search%").or(q.cmd.like("%$search%")).
            or(q.session.serverIP.like("%$search%").or(q.session.clientIP.like("%$search%")).or(q.changeFormItem.formId.like("%$search%"))))
        }
        if (session != null) {
            queries.and(q.session.id.eq(session));
        }
        if (changeFormItem != null) {
            queries.and(q.changeFormItem.id.eq(changeFormItem));
        }
        if (isMatched != null) {
            if (isMatched == false) {
                queries.and(q.matchResult.eq(ITXCommand.Companion.MatchResult.Unmatched));
            } else {
                queries.and(q.matchResult.eq(ITXCommand.Companion.MatchResult.Matched));
            }
        }
        if (auditResult != null && auditResult.isNotEmpty()) {
            queries.and(q.auditResult.`in`(auditResult.toList()));
        }
        if (processResult != null && processResult.isNotEmpty()) {
            queries.and(q.processResult.`in`(processResult.toList()));
        }
        if (type != null) {
            queries.and(q.session.type.eq(type))
        }
        if (alertLevel != null) {
            queries.and(q.rule.alertLevel.eq(alertLevel))
        }
        if (!StringUtils.isEmpty(categoryPath)) queries.and(q.rule.commandCategory.path.like("$categoryPath%"));
        if (matchCommandRule != null) {
            if (matchCommandRule) queries.and(q.rule.isNotNull)
            else queries.and(q.rule.isNull)
        }
        if (matchChangeForm != null) {
            if (matchChangeForm) queries.and(q.changeFormItem.isNotNull)
            else queries.and(q.changeFormItem.isNull)
        }
        if (startTime != null) {
            queries.and(q.execTime.goe(startTime))
        }
        if (endTime != null) {
            queries.and(q.execTime.loe(endTime))
        }
        if (!StringUtils.isEmpty(cmd)) {
            queries.and(q.cmd.like("%$cmd%"))
        }
        if (!StringUtils.isEmpty(serverIP)) {
            queries.and(q.session.serverIP.eq(serverIP))
        }
        if (!StringUtils.isEmpty(clientIP)) {
            queries.and(q.session.clientIP.eq(clientIP))
        }
        if (!StringUtils.isEmpty(loginUser)) {
            queries.and(q.session.loginUser.eq(loginUser))
        }
        if (!StringUtils.isEmpty(auditUser)) {
            queries.and(q.auditUser.nickname.eq(auditUser))
        }
        val p = service.repo.findAll(queries, pageAttr.get());
        p.content.forEach {
            RestUtil.unwrapBean(it.changeFormItem)
            RestUtil.unwrapBean(it.session)
            RestUtil.unwrapBean(it.rule)
            RestUtil.unwrapBean(it.auditUser)
        }
        return jsonOut(p);
    }

    @RequestMapping("{id:\\d+}")
    @Secured(AdminPermissions.ITX_COMMAND, AdminPermissions.REPORT_FORM_MANAGE)
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        addOptLog(OptLog.VIEW, "命令", data.cmd!!, data);
        return jsonOut(data);
    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(AdminPermissions.ITX_COMMAND)
    @JsonView(JsonViews.AdminView::class)
    fun delete(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        service.repo.deleteById(id)
        addOptLog(OptLog.DELETE, "命令", data.cmd!!, data);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(AdminPermissions.ITX_COMMAND)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun edit(@ModelAttribute("form") form: ITXCommand): ApiResp {
        var data: ITXCommand;
        if (form.id != null) {
            data = service.repo.findById(form.id!!).get();
        } else {
            data = ITXCommand();
        }
        service.repo.save(data);
        if (form.id != null)
            addOptLog(OptLog.EDIT, "命令", data.cmd!!, data);
        else
            addOptLog(OptLog.ADD, "命令", data.cmd!!, data);
        return jsonOut(data);
    }

    @RequestMapping(value = ["dealResult"], method = [RequestMethod.POST])
    @Secured(AdminPermissions.ITX_COMMAND, AdminPermissions.REPORT_FORM_MANAGE)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun dealResult(
        @ModelAttribute("id") id: Long,
        @RequestParam("auditResult") auditResult: ITXCommand.Companion.AuditResult,
        @RequestParam("processResult") processResult: ITXCommand.Companion.ProcessResult,
        @RequestParam("processDesc") processDesc: String?,
    ): ApiResp {
        var data = service.repo.findById(id).get();
        data.auditUser = this.adminUserService.findByUsername(this.adminUserService.currUser()!!.username);
        data.auditResult = auditResult;
        data.processResult = processResult;
        data.processDesc = processDesc;
        service.repo.save(data);
        service.runInAfterTranCommitted {
            this.changeFormItemService.lazyUpdateStats(data.changeFormItem!!.id!!)
        }
        this.logger.info("deal result ${data.changeFormItem!!.id} ${data.id} ${data.auditResult} ${data.processResult} ${data.processDesc}")
        addOptLog(OptLog.DISPOSE, "命令", data.cmd!!, data);
        return jsonOut(data);
    }


}

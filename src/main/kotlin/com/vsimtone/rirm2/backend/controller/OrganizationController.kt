package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.OptLog
import com.vsimtone.rirm2.backend.entity.Organization
import com.vsimtone.rirm2.backend.entity.QAdminUser
import com.vsimtone.rirm2.backend.entity.QOrganization
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.AdminUserService
import com.vsimtone.rirm2.backend.service.IDGeneratorService
import com.vsimtone.rirm2.backend.service.OrganizationService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RestController()
@RequestMapping("organizations")
class OrganizationController: BaseController() {

    @Autowired
    lateinit var service: OrganizationService;

    @Autowired
    lateinit var idGeneratorService: IDGeneratorService

    @RequestMapping("")
    @Secured(AdminPermissions.MANAGER)
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @RequestParam("parent", required = false) parent: Long?,
        @RequestParam("search", required = false) search: String?,
    ): ApiResp {
        val queries = BooleanBuilder();
        val a = QOrganization.a;
        if (parent != null)
            queries.and(a.parent.id.eq(parent))
        else if (StringUtils.isEmpty(search))
            queries.and(a.parent.isNull)
        else {
            return jsonOut(service.repo.findAll(queries, PageAttr(PageAttr.FIRST_NUM, 15, null, false).get()).content.map {
                mapOf(
                    "names" to service.getNames(it),
                    "path" to it.path,
                    "id" to it.id.toString()
                )
            })
        }
        return jsonOut(service.repo.findAll(queries));
    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(AdminPermissions.MANAGER)
    @JsonView(JsonViews.AdminView::class)
    fun delete(@PathVariable("id") id: Long): ApiResp {
        var lists: List<Organization>;
        lists = service.repo.findByParentId(id);
        for(tmp in lists){
            var tmpUser = userService.repo.findAll(QAdminUser.a.org.eq(tmp),PageAttr(PageAttr.FIRST_NUM,1).get()).firstOrNull();
            if(tmpUser !=null)
                return jsonOut(1,"该数据已被其它地方引用，不能删除该数据!");
            service.repo.delete(tmp)
        }
        var org: Organization = service.repo.findById(id!!).get();
        var tmpUser = userService.repo.findAll(QAdminUser.a.org.eq(org),PageAttr(PageAttr.FIRST_NUM,1).get()).firstOrNull();
        if(tmpUser !=null)
            return jsonOut(1,"该数据已被其它地方引用，不能删除该数据!");
        service.repo.delete(org)
        addOptLog(OptLog.DELETE, "组织机构", org.name, org);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(AdminPermissions.MANAGER)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun edit(@ModelAttribute("form") form: Organization): ApiResp {
        var data: Organization;
        if (form.id != null) {
            data = service.repo.findById(form.id!!).get();
        } else {
            data = Organization();
            if (form.parent != null) {
                form.parent = service.repo.findById(form.parent!!.id!!).get()
            }
            data.parent = form.parent;
        }

        var q = QOrganization.a.name.eq(form.name)
        if(data.parent==null)
            q = q.and(QOrganization.a.parent.isNull)
        else
            q = q.and(QOrganization.a.parent.eq(data.parent))
        var tmp = service.findAll(q).firstOrNull();
        if(tmp !=null && tmp.id != data.id){
            return this.jsonOut(1, "组织机构不能重复!")
        }

        data.name = form.name;
        service.repo.save(data);
        data.path = service.updatePath(data);
        service.repo.save(data);
        if (form.id != null)
            addOptLog(OptLog.EDIT, "组织机构", data.name, data);
        else
            addOptLog(OptLog.ADD, "组织机构", data.name, data);
        return this.jsonOut(data)
    }


    @Secured(AdminPermissions.MANAGER)
    @RequestMapping("{id:\\d+}")
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        addOptLog(OptLog.VIEW, "组织机构", data.name, data);
        return jsonOut(data);
    }



}
package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.bean.RestException
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.controller.APPConfigController.Companion.PASSWORD_HIDDEN_STRING
import com.vsimtone.rirm2.backend.entity.*
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.*
import com.vsimtone.rirm2.backend.service.ChangeFormSyncService.Companion.ApiRespPage
import com.vsimtone.rirm2.backend.utils.CryptUtils
import com.vsimtone.rirm2.backend.utils.JSONUtils
import org.apache.commons.lang3.StringUtils
import org.apache.poi.ss.formula.functions.T
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import java.util.*

@RestController()
@RequestMapping("change_form_items")
class ChangeFormItemController : BaseController() {

    @Autowired
    lateinit var service: ChangeFormItemService

    @Autowired
    lateinit var changeFormHostService: ChangeFormHostService

    @Autowired
    lateinit var changeFormCommandService: ChangeFormCommandService

    @Autowired
    lateinit var itxCommandService: ITXCommandService

    @Autowired
    lateinit var adminUserService: AdminUserService

    @Autowired
    private lateinit var appConfigService: APPConfigService

    @RequestMapping("")
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?,
        @RequestParam("startTime", required = false) startTime: Date?,
        @RequestParam("endTime", required = false) endTime: Date?,
        @RequestParam("typeCheck", required = false) typeCheck: ChangeFormItem.Companion.TypeCheck?
    ): ApiResp {
        val q = QChangeFormItem.a
        val queries = BooleanBuilder()
        if (!StringUtils.isEmpty(search)) {
            queries.andAnyOf(BooleanBuilder(q.formId.like("%$search%")).or(q.name.like("%$search%")))
        }
        if (startTime != null) {
            queries.andAnyOf(q.planBeginTime.goe(startTime))
        }
        if (endTime != null) {
            queries.andAnyOf(q.planEndTime.loe(endTime))
        }
        if (typeCheck != null) {
            queries.andAnyOf(q.typeCheck.eq(typeCheck))
        }
        val p = service.repo.findAll(queries, pageAttr.get());
        return jsonOut(p);
    }

    @GetMapping("rematch/{id:\\d+}")
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun rematch(@PathVariable("id") id: Long): ApiResp {
        val item = this.service.findById(id);
        this.service.addToMatchQueue(item.formId);
        addOptLog(OptLog.EDIT, "变更单重匹配", item.formId, item);
        return this.jsonOut(true);
    }

    @RequestMapping("{id:\\d+}")
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        addOptLog(OptLog.VIEW, "变更单", data.formId, data);
        return jsonOut(data);
    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(AdminPermissions.SUPER_DELETER)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun delete(@PathVariable("id") id: Long): ApiResp {
        val item = this.service.findById(id)
        this.logger.info("do delete ${item}")
        var count = 0
        this.itxCommandService.findAll(QITXCommand.a.changeFormItem.id.eq(id)).forEach {
            it.changeFormItem = null
            it.matchResult = null
            it.auditResult = null
            it.processResult = null
            it.processDesc = null
            this.itxCommandService.save(it)
            count++
        }
        this.logger.info("reset itx command to no changeform. count=$count")
        this.changeFormHostService.batchDelete(QChangeFormHost.a.changeFormItem.id.eq(id))
        this.changeFormCommandService.batchDelete(QChangeFormCommand.a.changeFormItem.id.eq(id))
        this.changeFormHostService.batchDelete(QChangeFormHost.a.changeFormId.eq(item.formId))
        this.changeFormCommandService.batchDelete(QChangeFormCommand.a.changeFormId.eq(item.formId))
        this.service.batchDelete(QChangeFormItem.a.id.eq(id))
        this.logger.info("delete done")
        addOptLog(OptLog.DELETE, "变更单", item.formId, item);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun edit(@ModelAttribute("form") form: ChangeFormItem): ApiResp {
        var data: ChangeFormItem;
        if (form.id != null) {
            data = service.repo.findById(form.id!!).get();
        } else {
            data = ChangeFormItem();
        }
        service.repo.save(data);
        if (form.id != null)
            addOptLog(OptLog.EDIT, "变更单", data.formId, data);
        else
            addOptLog(OptLog.ADD, "变更单", data.formId, data);
        return jsonOut(data);
    }

    @RequestMapping(value = ["dealResult"], method = [RequestMethod.POST])
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun dealResult(
        @ModelAttribute("id") id: Long,
        @RequestParam("processResult") processResult: ChangeFormItem.Companion.TypeCheck,
        @RequestParam("processDesc") processDesc: String?,
    ): ApiResp {
        var data = service.repo.findById(id).get();
        data.typeCheckEditUser = this.adminUserService.findByUsername(this.adminUserService.currUser()!!.username);
        data.typeCheck = processResult;
        data.typeCheckEditRemark = processDesc;
        service.repo.save(data);
        this.logger.info("deal result ${data.formId} ${data.name} ${data.typeCheckEditUser} ${data.typeCheck} ${data.typeCheckEditRemark}")
        addOptLog(OptLog.DISPOSE, "变更单", data.formId!!, data);
        return jsonOut(data);
    }

    @RequestMapping("type_check_config_get")
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    fun typeCheckConfigGet(@RequestParam("key") key: Array<String>): ApiResp {
        key.forEach {
            if (
                it != ChangeFormItemService.CNF_KEY_TYPE_CHECK_CHANGE_TYPE_NEED_CONFIRMS &&
                it != ChangeFormItemService.CNF_KEY_TYPE_CHECK_CHANGE_TYPE2_NEED_CONFIRMS &&
                it != ChangeFormItemService.CNF_KEY_TYPE_CHECK_CUSTOM_NORMAL_SYMBOLS
            ) {
                throw RestException(1, "key error")
            }
        }
        var data = appConfigService.search(*key).toMutableMap()
        return ApiResp(data)
    }

    @RequestMapping(value = ["type_check_config_set"], method = [RequestMethod.POST])
    @Secured(AdminPermissions.CHANGE_FORM_MANAGE)
    @Transactional
    open fun typeCheckConfigSet(
        @RequestParam("key") key: List<String>,
        @RequestParam("val") `val`: List<String>
    ): ApiResp {
        key.forEach {
            if (
                it != ChangeFormItemService.CNF_KEY_TYPE_CHECK_CHANGE_TYPE_NEED_CONFIRMS &&
                it != ChangeFormItemService.CNF_KEY_TYPE_CHECK_CHANGE_TYPE2_NEED_CONFIRMS &&
                it != ChangeFormItemService.CNF_KEY_TYPE_CHECK_CUSTOM_NORMAL_SYMBOLS
            ) {
                throw RestException(1, "key error")
            }
        }
        key.forEachIndexed { k, v ->
            this.appConfigService.setConfig(v, `val`[k]);
        }
        return this.jsonOut(true)
    }
}

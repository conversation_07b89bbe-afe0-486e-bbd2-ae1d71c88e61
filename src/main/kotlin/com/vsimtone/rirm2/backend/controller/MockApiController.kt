package com.vsimtone.rirm2.backend.controller

import com.hankcs.hanlp.corpus.tag.Nature.begin
import com.vsimtone.rirm2.backend.entity.BaseEntity
import com.vsimtone.rirm2.backend.entity.BaseEntity.Companion.DEFAULT_DATE_PATTERN
import com.vsimtone.rirm2.backend.service.ChangeFormSyncService
import com.vsimtone.rirm2.backend.service.ChangeFormSyncService.Companion.ApiRespPage
import com.vsimtone.rirm2.backend.service.ExcelImportService
import com.vsimtone.rirm2.backend.service.WXTSyncCommandService
import com.vsimtone.rirm2.backend.utils.CryptUtils
import com.vsimtone.rirm2.backend.utils.DateUtil
import com.vsimtone.rirm2.backend.utils.JSONUtils
import javax.servlet.http.HttpServletRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.redis.connection.ReactiveStreamCommands.AddStreamRecord.body

import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ModelAttribute
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.HttpStatusCodeException
import java.text.SimpleDateFormat
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.Calendar
import java.util.Date
import java.util.TimeZone

@RestController()
@RequestMapping("mock_api")
class MockApiController : BaseController() {
    @Autowired
    lateinit var excelImportService: ExcelImportService

    fun generatePrivateIPv4(value: Int): String {
        // 限制 value 在 0 到 2^22 - 1 之间（10.0.0.0/8 范围）
        val maskedValue = value and 0x3FFFFF // 2^22 = 4194304

        // 将 value 转换为 4 个字节
        val byte1 = 10 // 10.x.x.x
        val byte2 = (maskedValue shr 16) and 0xFF
        val byte3 = (maskedValue shr 8) and 0xFF
        val byte4 = maskedValue and 0xFF

        // 将字节数组格式化为 IPv4 地址
        return "$byte1.$byte2.$byte3.$byte4"
    }

    fun getEndDate(begin: Long): Long {
        val s = CryptUtils.sha256hex(CryptUtils.sha256hex("${begin}"));
        val r = "\\d".toRegex().find(s)?.value?.toInt() ?: 0;
        return begin + 600 * 1000 + r * 3600 * 1000L;
    }

    fun generateMockData(startDate: Long, endDate: Long, name: String, _size: Int, formId: String): MutableList<*> {
        var data = mutableListOf<Any>();
        var size = _size;
        if (size <= 0) size = 1;
        var timeSpace = (endDate - startDate) / size;
        if (timeSpace <= 0)
            return mutableListOf<Any>();

        for (idx in 0 until size) {
            var it: Any? = null;
            var begin = startDate + idx * timeSpace
            var end = begin + timeSpace
            if (name == "item") {
                var _it = ChangeFormSyncService.Companion.ApiChangeFormItemListItem()
                _it.tickID = "id-${begin}"
                _it.scheduledStartDate = BaseEntity.DEFAULT_DATE_FORMAT.format(Date(begin))
                _it.scheduledEndDate = BaseEntity.DEFAULT_DATE_FORMAT.format(Date(getEndDate(begin)))
                _it.description = "desc-${begin}"
                _it.asgrp = "team-${(Math.random() * 100).toInt()}"
                _it.requestType = 1
                _it.changeRequestStatus = (Math.random() * 13.0).toInt()
                it = _it
            }
            if (name == "cmd") {
                it = ChangeFormSyncService.Companion.ApiChangeFormCommandListItem()
                it.ticketID = formId
                it.chgItem = "cmd-${(Math.random() * 100).toInt()} -arg ${(Math.random() * 10).toInt()}"
                it.description = "desc-${begin % 1000000}"
                it.startDate = SimpleDateFormat("yyyy/MM/dd HH:mm").format(Date(begin))
                it.endDate = SimpleDateFormat("yyyy/MM/dd HH:mm").format(Date(end))
            }
            if (name == "host1") {
                it = ChangeFormSyncService.Companion.ApiChangeFormHost1ListItem()
                it.ticketID = "id-${begin + 1}"
                it.idRecordSN = "sn-${begin + 1}"
                it.produceNum = formId
                it.idName = "id-${begin % 10}"
                it.userName = "user-l${(Math.random() * 10).toInt()}"
                it.applyTime = BaseEntity.DEFAULT_DATE_FORMAT.format(Date(begin))
                it.startTime = BaseEntity.DEFAULT_DATE_FORMAT.format(Date(begin))
                it.endTime = BaseEntity.DEFAULT_DATE_FORMAT.format(Date(end))
            }
            if (name == "host2") {
                it = ChangeFormSyncService.Companion.ApiChangeFormHost2ListItem()
                it.ticketID = "id-${begin + 2}"
                it.opsRecordSN = "sn-${begin + 2}"
                it.produceNum = formId
                it.opsName = "user-l${(Math.random() * 10).toInt()}"
                it.applyTime = BaseEntity.DEFAULT_DATE_FORMAT.format(Date(begin))
                it.startTime = BaseEntity.DEFAULT_DATE_FORMAT.format(Date(begin))
                it.endTime = BaseEntity.DEFAULT_DATE_FORMAT.format(Date(end))
            }
            if (name == "host3") {
                it = ChangeFormSyncService.Companion.ApiChangeFormHost3ListItem()
                it.sn = "sn-${begin + 3}"
                it.produceNum = formId
                it.idGroupIndex = formId
                it.startTime = BaseEntity.DEFAULT_DATE_FORMAT.format(Date(begin))
                it.endTime = BaseEntity.DEFAULT_DATE_FORMAT.format(Date(end))
                it.applyTime = BaseEntity.DEFAULT_DATE_FORMAT.format(Date(begin))
            }
            if (name == "host4") {
                it = ChangeFormSyncService.Companion.ApiChangeFormHost4ListItem()
                it.idRecordSN = "sn-${begin + 4}"
                it.idSystem = "system-${begin % 10}"
                it.idUser = "user-l${(Math.random() * 10).toInt()}"
                it.environmentType = "env-${begin % 10}"
            }

            if (name == "wxt_session") {
                var _it = WXTSyncCommandService.Companion.ApiSessionAuditListItem()
                _it.sessionId = "id-${begin}"
                _it.begin = Date(begin).time
                _it.end = getEndDate(begin)
                var rnd = Math.random() * 100
                if (rnd <= 80) {
                    _it.sysType = "Linux"
                } else {
                    _it.sysType = "Aix"
                }
                _it.clientIP = generatePrivateIPv4(idx)
                _it.serverIP = generatePrivateIPv4(1000000 + idx)
                _it.status = 2
                _it.loginUser = "user-l${(Math.random() * 10).toInt()}"
                _it.realUser = "user-r${(Math.random() * 10).toInt()}"
                _it.type = "tui"
                _it.protocol = "ssh"
                it = _it
            }
            if (name == "wxt_command") {
                it = WXTSyncCommandService.Companion.ApiCommandAuditListItem()
                it.session = formId
                it.cmd = "cmd-${(Math.random() * 100).toInt()} -arg ${(Math.random() * 10).toInt()}"
                it.execTime = Date(begin).time
            }

            if (it != null) data.add(it)
        }
        return data
    }

    @RequestMapping(value = ["change_form_api/{name}"], method = [RequestMethod.GET])
    fun changeFormApiMock(
        @PathVariable("name") name: String,
        @RequestParam("size") size: Int,
        @RequestParam("current") current: Int,
        @RequestParam("token", required = false) _token: String?,
        @RequestParam("startTime", required = false) startTime: String?,
        @RequestParam("endTime", required = false) endTime: String?,
        @RequestParam("TicketID", required = false) ticketID: String?,
        @RequestParam("IDGroupNo", required = false) IDGroupNo: String?,
        @RequestParam("ProduceNum", required = false) ProduceNum: String?
    ): Any {
        if (Math.random() * 100 < 3) { // 3%的概率失败
            val r = ChangeFormSyncService.Companion.ApiResp();
            r.success = false;
            r.code = 5006;
            r.message = "Limited Request Interval: Your request is too frequent!";
            return r;
        }
        var itemTimeSpace = 900 * 1000L; // 15分钟一个变更单

        var startDate = 0L;
        var endDate = 0L;
        if (ticketID == null && startTime != null && endTime != null) {
            startDate = ExcelImportService.parseDate(startTime).time
            endDate = ExcelImportService.parseDate(endTime).time
        } else {
            var formId = listOf(ticketID, IDGroupNo, ProduceNum).firstOrNull { it != null } ?: "id-${System.currentTimeMillis()}"
            formId = formId.replace("id-", "");
            startDate = formId.toLongOrNull() ?: 0;
            endDate = getEndDate(startDate)
        }
        var pageResult: ApiRespPage<*> = ApiRespPage<Any>();
        var data: MutableList<*> = mutableListOf<Any>();
        var token = getRequest().getHeader("Token") ?: _token;
        var chunkedData: MutableList<MutableList<*>> = mutableListOf();
        var resp = fun(success: Boolean, data: Any): ChangeFormSyncService.Companion.ApiResp {
            var it = ChangeFormSyncService.Companion.ApiResp()
            it.success = success;
            if (success) {
                it.data = data;
            } else {
                it.message = data as String;
            }
            return it
        }
        val realToken = CryptUtils.sha256hex("rirm2-sync-api-mock-token-${name}")
        if (token != realToken) { // for item is 53cc2f1e57148bedff30aeecb89519f9df083f0eff368eb463d3d567bda944e9
            this.logger.info("token is ${name} ${realToken}")
            return resp(false, "token is wrong")
        }
        if (name == "item") {
            data = generateMockData(startDate, endDate, name, ((endDate - startDate) / itemTimeSpace).toInt(), "")
            pageResult = ChangeFormSyncService.Companion.ApiChangeFormItemListData()
        }
        if (name == "cmd") {
            data = generateMockData(startDate, endDate, name, (endDate % 100).toInt(), ticketID!!)
            pageResult = ChangeFormSyncService.Companion.ApiChangeFormCommandListData()
        }
        if (name == "host1") {
            data = generateMockData(startDate, endDate, name, (endDate % 100).toInt(), ProduceNum!!)
            pageResult = ChangeFormSyncService.Companion.ApiChangeFormHost1ListData()
        }
        if (name == "host2") {
            data = generateMockData(startDate, endDate, name, (endDate % 100).toInt(), ProduceNum!!)
            pageResult = ChangeFormSyncService.Companion.ApiChangeFormHost2ListData()
        }
        if (name == "host3") {
            data = generateMockData(startDate, endDate, name, 1, ProduceNum!!)
            pageResult = ChangeFormSyncService.Companion.ApiChangeFormHost3ListData()
        }
        if (name == "host4") {
            data = generateMockData(startDate, endDate, name, (endDate % 100).toInt(), IDGroupNo!!)
            pageResult = ChangeFormSyncService.Companion.ApiChangeFormHost4ListData()
        }
        if (data.isEmpty()) return resp(false, "name $name unknown");

        chunkedData = data.chunked(size!!.toInt()) as MutableList<MutableList<*>>
        if (chunkedData.size < current!!) return resp(false, "page overflow");

        pageResult.size = size!!.toInt();
        pageResult.current = current!!.toInt();
        pageResult.total = data.size;
        pageResult.num = data.size;
        pageResult.records = chunkedData[current!!.toInt() - 1] as List<Nothing>;
        return resp(true, pageResult)
    }

    class ApiReqAuth {
        var username: String = "";
        var password: String = "";
    }

    @RequestMapping(value = ["wxt_api/authenticate"], method = [RequestMethod.POST])
    fun wxtApiAuthenticateMock(@RequestBody params: ApiReqAuth): Any {
        if (params.username == "mock-user-01" && params.password == "fahShuash7pu") {
            return mapOf("ST_AUTH_TOKEN" to CryptUtils.sha256hex("rirm2-mock-key-${params.username}:${params.password}"))
        }
        throw RuntimeException("auth fail")
    }

    @RequestMapping(value = ["wxt_api/{name1}/{name2}/{name3}", "wxt_api/{name1}/{name2}"], method = [RequestMethod.GET])
    fun wxtApiMock(req: HttpServletRequest, @PathVariable names: Map<String, String>, @ModelAttribute() params: WXTSyncCommandService.Companion.ApiReqParams): Any {

        if (Math.random() * 100 < 3) { // 3%的概率触发失败
            return ResponseEntity.status(429).body<String>("429");
        }

        var startDate = 0L;
        var endDate = 0L;
        var sessTimeSpace = 300 * 1000L; // 5分钟一个会话

        if (params.endAfter != null) {
            startDate = params.endAfter!!.time
            endDate = params.endBefore!!.time
        } else {
            startDate = names["name3"]!!.replace("id-", "").toLong();
            endDate = getEndDate(startDate)
        }

        val ST_AUTH_TOKEN = req.cookies.find { it.name == "ST_AUTH_TOKEN" }?.value
        if (ST_AUTH_TOKEN != CryptUtils.sha256hex("rirm2-mock-key-mock-user-01:fahShuash7pu")) {
            return ResponseEntity.status(403).body<String>("403");
        }
        if (params.size!! <= 0) return ResponseEntity.status(400).body<String>("400");
        var pageResult: WXTSyncCommandService.Companion.ApiRespPage<*> = WXTSyncCommandService.Companion.ApiRespPage<Any>();
        var data: MutableList<*> = mutableListOf<Any>();
        var chunkedData: MutableList<MutableList<*>> = mutableListOf();
        if (names["name1"] == "sesslog" && names["name2"] == "tui") {
            data = generateMockData(startDate, endDate, "wxt_session", ((endDate - startDate) / sessTimeSpace).toInt(), "")
            pageResult = WXTSyncCommandService.Companion.ApiSessionAuditListData()
        } else if (names["name1"] == "tuidetail" && names["name2"] == "sessid") {
            data = generateMockData(startDate, endDate, "wxt_command", 10 + (endDate % 1000).toInt(), names["name3"]!!)
            pageResult = WXTSyncCommandService.Companion.ApiCommandAuditListData()
        } else {
            return ResponseEntity.status(400).body<String>("400");
        }
        if (data.isEmpty()) return ResponseEntity.status(500).body<String>("data empty");

        chunkedData = data.chunked(params.size!!.toInt()) as MutableList<MutableList<*>>
        if (chunkedData.size == 0 || chunkedData.size < params.page!!) return ResponseEntity.status(400).body("page overflow: max=${chunkedData.size - 1}, cur=${params.page!!}");
        pageResult.size = params.size!!.toInt();
        pageResult.totalPages = chunkedData.size;
        pageResult.totalElements = data.size;
        pageResult.page = params.page!!.toInt();
        pageResult.content = chunkedData[params.page!!.toInt()] as List<Nothing>;
        return pageResult
    }
}

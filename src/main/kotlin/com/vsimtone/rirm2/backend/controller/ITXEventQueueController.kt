package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.ITXEventQueue
import com.vsimtone.rirm2.backend.entity.QITXEventQueue
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.ITXEventQueueService
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*

@RestController()
@RequestMapping("itx_event_queues")
class ITXEventQueueController : BaseController() {

    @Autowired
    lateinit var service: ITXEventQueueService

    @RequestMapping("")
    @Secured(AdminPermissions.ITX_COMMAND)
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr,
        @RequestParam("search", required = false) search: String?
    ): ApiResp {
        val q = QITXEventQueue.a
        val queries = BooleanBuilder()
        if (!StringUtils.isEmpty(search)) {
        }
        return jsonOut(service.repo.findAll(queries, pageAttr.get()));
    }

    @RequestMapping("{id:\\d+}")
    @Secured(AdminPermissions.ITX_COMMAND)
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        return jsonOut(service.repo.findById(id).get());
    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(AdminPermissions.ITX_COMMAND)
    @JsonView(JsonViews.AdminView::class)
    fun delete(@PathVariable("id") id: Long): ApiResp {
        service.repo.deleteById(id)
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(AdminPermissions.ITX_COMMAND)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun edit(@ModelAttribute("form") form: ITXEventQueue): ApiResp {
        var data: ITXEventQueue;
        if (form.id != null) {
            data = service.repo.findById(form.id!!).get();
        } else {
            data = ITXEventQueue();
        }
        service.repo.save(data);
        return jsonOut(data);
    }


}

package com.vsimtone.rirm2.backend.controller

import com.fasterxml.jackson.annotation.JsonView
import com.querydsl.core.BooleanBuilder
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.bean.PageAttr
import com.vsimtone.rirm2.backend.consts.AdminPermissions
import com.vsimtone.rirm2.backend.entity.CommandCategory
import com.vsimtone.rirm2.backend.entity.CommandRule
import com.vsimtone.rirm2.backend.entity.CommandRule.AlertLevel
import com.vsimtone.rirm2.backend.entity.OptLog
import com.vsimtone.rirm2.backend.entity.QCommandRule
import com.vsimtone.rirm2.backend.json.JsonViews
import com.vsimtone.rirm2.backend.service.CommandCategoryService
import com.vsimtone.rirm2.backend.service.CommandRuleService
import com.vsimtone.rirm2.backend.service.ITXEventQueueService
import com.vsimtone.rirm2.backend.utils.AppUtils
import com.vsimtone.rirm2.backend.utils.TimeStats
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.access.annotation.Secured
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController()
@RequestMapping("command_rules")
class CommandRuleController : BaseController() {

    @Autowired
    lateinit var service: CommandRuleService

    @Autowired
    lateinit var commandCatalogService: CommandCategoryService

    @Autowired
    lateinit var itxEventQueueService: ITXEventQueueService

    @RequestMapping("")
    @Secured(AdminPermissions.CMD_RULE)
    @JsonView(JsonViews.AdminList::class)
    fun list(
        @ModelAttribute("page") pageAttr: PageAttr, @RequestParam("search", required = false) search: String?, @RequestParam("alertLevel", required = false) alertLevel: Array<CommandRule.AlertLevel>?, @RequestParam("categoryPath", required = false) categoryPath: String?
    ): ApiResp {
        val q = QCommandRule.a
        val queries = BooleanBuilder()
        if (!StringUtils.isEmpty(search)) {
            queries.andAnyOf(BooleanBuilder(q.commandCategory.name.like("%$search%")).or(q.name.like("%$search%")))
        }
        if (alertLevel != null && alertLevel.isNotEmpty()) {
            queries.andAnyOf(q.alertLevel.`in`(alertLevel.toList()))
        }
        if (!StringUtils.isEmpty(categoryPath)) queries.and(q.commandCategory.path.like("$categoryPath%"));
        val p = service.repo.findAll(queries, pageAttr.get());
        return jsonOut(p);
    }

    @RequestMapping("{id:\\d+}")
    @Secured(AdminPermissions.CMD_RULE)
    @JsonView(JsonViews.AdminView::class)
    fun view(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        addOptLog(OptLog.VIEW, "命令规则", data.name, data);
        return jsonOut(data);
    }

    @RequestMapping("{id:\\d+}/delete", method = [RequestMethod.POST])
    @Secured(AdminPermissions.CMD_RULE)
    @JsonView(JsonViews.AdminView::class)
    fun delete(@PathVariable("id") id: Long): ApiResp {
        val data = service.repo.findById(id).get();
        try {
            service.repo.deleteById(id)
        } catch (e: Exception) {
            if (e.message != null && e.message!!.indexOf("ORA-02292") != -1) {
                return this.jsonOut(1, "该规则已被引用，不能删除。")
            }
            throw e;
        }
        addOptLog(OptLog.DELETE, "命令规则", data.name, data);
        return jsonOut(true);
    }

    @RequestMapping(value = [""], method = [RequestMethod.POST])
    @Secured(AdminPermissions.CMD_RULE)
    @JsonView(JsonViews.AdminView::class)
    @Transactional
    fun edit(@ModelAttribute("form") form: CommandRule): ApiResp {
        var data: CommandRule;
        if (form.id != null) {
            data = service.repo.findById(form.id!!).get();
        } else {
            data = CommandRule();
        }
        form.regexFlags.forEach {
            try {
                CommandRule.RegexFlag.valueOf(it)
            } catch (e: Exception) {
                return jsonOut(1, "正则类型错误：$it");
            }
        }
        form.regex.forEach {
            try {
                itxEventQueueService.regexMatch("ls -a -l -h | less", form.regexFlags, it)
            } catch (e: Exception) {
                return jsonOut(1, "正则表达式错误：$it");
            }
        }
        data.enabled = form.enabled;
        data.name = form.name;
        data.regexFlags = form.regexFlags;
        data.regex = form.regex;
        data.commandCategory = form.commandCategory;
        data.alertLevel = form.alertLevel;
        data.description = form.description;
        service.repo.save(data);
        if (form.id != null)
            addOptLog(OptLog.EDIT, "命令规则", data.name, data);
        else
            addOptLog(OptLog.ADD, "命令规则", data.name, data);
        return jsonOut(true);
    }

    fun getColStr(columnNames: MutableMap<String, Int>, rowIndex: Int, rowData: List<Any?>, columnKey: String, allowEmpty: Boolean = false): String {
        val column = columnNames[columnKey]!!
        val v = rowData[column]?.toString()?.trim()
        if (!allowEmpty && StringUtils.isEmpty(v))
            error("第${rowIndex}行，第${column}列不能为空")
        return v ?: ""
    }

    @RequestMapping(value = ["import"], method = [RequestMethod.POST])
    @Secured(AdminPermissions.CMD_RULE)
    @JsonView(JsonViews.AdminView::class)
    fun import(@RequestParam("upfile") upfile: MultipartFile): ApiResp {
        if (upfile.size >= 1024 * 1024 * 100) { // 最大100MB
            return this.jsonOut(1, "文件${upfile.name}过大")
        }
        var addCount = 0;
        var updateCount = 0;
        try {

            upfile.inputStream.use {
                val xlsData = AppUtils.readExcel(it)
                val columns = xlsData[0]
                xlsData.removeAt(0)
                val columnNames = mutableMapOf<String, Int>()
                val columnTitles = arrayOf("name:规则名称", "catalog:分类", "level:命令等级", "regexFlags:正则类别", "regex:匹配正则", "desc:描述")
                columnTitles.forEach { colIt ->
                    val kv = colIt.split(Regex(":"), 2)
                    val idx = columns.indexOf(kv[1])
                    if (idx == -1) return error("未发现列 ${kv[1]}")
                    columnNames[kv[0]] = idx
                }
                var timeStats = TimeStats()
                this.service.runInTran {
                    var allCommandRules = mutableMapOf<String, CommandRule>()
                    var xlsCommandRules = mutableMapOf<String, CommandRule>()
                    this.service.findAll().map {
                        if (allCommandRules[it.name] != null) {
                            error("当前存在名称重复数据，不允许导入：${it.name}")
                        }
                        allCommandRules[it.name] = it
                    }
                    var catalogs = mutableMapOf<String, CommandCategory>()
                    this.commandCatalogService.findAll().forEach {
                        catalogs[it.name] = it
                    }
                    timeStats.point("t1");
                    val levelMap = mutableMapOf<String, CommandRule.AlertLevel>(
                        "信息" to AlertLevel.Low, "警告" to AlertLevel.Normal, "严重" to AlertLevel.High
                    )
                    val flagMap = mutableMapOf<String, CommandRule.RegexFlag>(
                        "忽略大小写" to CommandRule.RegexFlag.IGNORE_CASE,
                        "区域-名称" to CommandRule.RegexFlag.AREA_NAME,
                        "区域-参数" to CommandRule.RegexFlag.AREA_ARGS,
                        "匹配-全部" to CommandRule.RegexFlag.MATCH_FULL,
                        "匹配-开始" to CommandRule.RegexFlag.MATCH_BEGIN,
                        "匹配-结束" to CommandRule.RegexFlag.MATCH_END,
                    )
                    xlsData.forEachIndexed { i, row ->
                        var name = getColStr(columnNames, i, row, "name")
                        var catalog = getColStr(columnNames, i, row, "catalog")
                        var _level = getColStr(columnNames, i, row, "level")
                        var _regexFlags = getColStr(columnNames, i, row, "regexFlags", true)
                        var _regex = getColStr(columnNames, i, row, "regex")
                        var desc = getColStr(columnNames, i, row, "desc", true)
                        if (xlsCommandRules[name] != null) {
                            return@runInTran error("第${i + 2}行: 名称重复")
                        }
                        if (catalogs[catalog] == null) {
                            return@runInTran error("第${i + 2}行: 分类不存在[${catalog}]")
                        }
                        var regexFlags = mutableListOf<String>()
                        var regexs = mutableListOf<String>()
                        var level = levelMap[_level] ?: error("第${i + 2}行: 命令等级错误[$_level]");
                        _regexFlags.split(Regex("\\s*,\\s*")).filter { !it.trim().isEmpty() }.forEach { _f ->
                            val f = _f.trim();
                            if (flagMap[f] == null) return@runInTran error("第${i + 2}行: 正则类型错误[$f]")
                            regexFlags.add(flagMap[f]!!.name)
                        }
                        _regex.split("\n").forEach { _r ->
                            val r = _r.trim();
                            try {
                                itxEventQueueService.regexMatch("ls -a -l -h | less", regexFlags, r)
                                regexs.add(r)
                            } catch (e: Exception) {
                                return@runInTran error("第${i + 2}行: 匹配正则错误[$r]");
                            }
                        }
                        if (regexs.isEmpty()) return@runInTran error("第${i + 2}行: 匹配正则不能为空")
                        var commandRule = allCommandRules[name] ?: CommandRule()
                        if (commandRule.id == null) {
                            commandRule.name = name
                        }
                        commandRule.commandCategory = catalogs[catalog]!!
                        commandRule.regexFlags = regexFlags
                        commandRule.regex = regexs
                        commandRule.alertLevel = level
                        commandRule.description = desc
                        xlsCommandRules[commandRule.name] = commandRule;
                    }
                    timeStats.point("t2");
                    xlsCommandRules.values.forEach {
                        if (it.id == null) {
                            addCount++
                        } else {
                            updateCount++
                        }
                        this.service.save(it)
                    }
                    this.service.repo.flush();
                    timeStats.point("t3");
                }
                this.logger.info("import time stats: ${timeStats}")
            }
        } catch (e: Exception) {
            this.logger.error("import error", e)
            return this.jsonOut(1, e.message ?: "未知错误")
        }
        addOptLog(OptLog.IMPORT, "命令规则", "命令规则导入", "导入新增数据："+addCount+"条；导入更新数据："+updateCount+"条。");
        return this.jsonOut(mapOf("count" to mapOf("add" to addCount, "update" to updateCount)));
    }


}

package com.vsimtone.rirm2.backend.utils

import java.time.Instant


class SnowflakeID {

    private val UNUSED_BITS = 1 // Sign bit, Unused (always set to 0)

    private val EPOCH_BITS = 41
    private val NODE_ID_BITS = 10
    private val SEQUENCE_BITS = 12

    private val maxNodeId = (1L shl NODE_ID_BITS) - 1
    private val maxSequence = (1L shl SEQUENCE_BITS) - 1

    // Custom Epoch (January 1, 2015 Midnight UTC = 2015-01-01T00:00:00Z)
    private val DEFAULT_CUSTOM_EPOCH = 1420070400000L

    private var nodeId: Long = 0
    private var customEpoch: Long = 0

    @Volatile
    private var lastTimestamp = -1L

    @Volatile
    private var sequence = 0L

    // Create Snowflake with a nodeId
    constructor(nodeId: Long) {
        if (nodeId > maxNodeId) throw RuntimeException("Node id is too high")
        this.nodeId = nodeId
        this.customEpoch = DEFAULT_CUSTOM_EPOCH
    }


    @Synchronized
    fun nextId(): Long {
        var currentTimestamp = timestamp()
        check(currentTimestamp >= lastTimestamp) { "Invalid System Clock!" }
        if (currentTimestamp == lastTimestamp) {
            sequence = sequence + 1 and maxSequence
            if (sequence == 0L) { // Sequence Exhausted, wait till next millisecond.
                currentTimestamp = waitNextMillis(currentTimestamp)
            }
        } else { // reset sequence to start with zero for the next millisecond
            sequence = 0
        }
        lastTimestamp = currentTimestamp
        return (currentTimestamp shl NODE_ID_BITS + SEQUENCE_BITS or (nodeId shl SEQUENCE_BITS)
                or sequence)
    }


    // Get current timestamp in milliseconds, adjust for the custom epoch.
    private fun timestamp(): Long {
        return Instant.now().toEpochMilli() - customEpoch
    }

    // Block and wait till next millisecond
    private fun waitNextMillis(currentTimestamp: Long): Long {
        var currentTimestamp = currentTimestamp
        while (currentTimestamp == lastTimestamp) {
            currentTimestamp = timestamp()
        }
        return currentTimestamp
    }

    fun parse(id: Long): LongArray? {
        val maskNodeId = (1L shl NODE_ID_BITS) - 1 shl SEQUENCE_BITS
        val maskSequence = (1L shl SEQUENCE_BITS) - 1
        val timestamp = (id shr NODE_ID_BITS + SEQUENCE_BITS) + customEpoch
        val nodeId = id and maskNodeId shr SEQUENCE_BITS
        val sequence = id and maskSequence
        return longArrayOf(timestamp, nodeId, sequence)
    }

    override fun toString(): String {
        return ("Snowflake Settings [EPOCH_BITS=" + EPOCH_BITS + ", NODE_ID_BITS=" + NODE_ID_BITS
                + ", SEQUENCE_BITS=" + SEQUENCE_BITS + ", CUSTOM_EPOCH=" + customEpoch
                + ", NodeId=" + nodeId + "]")
    }
}
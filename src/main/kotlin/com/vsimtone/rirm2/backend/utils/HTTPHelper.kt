package com.vsimtone.rirm2.backend.utils


import okhttp3.*
import okhttp3.MediaType.Companion.toMediaType
import okhttp3.RequestBody.Companion.toRequestBody
import okhttp3.internal.closeQuietly
import org.apache.commons.io.IOUtils
import org.slf4j.LoggerFactory
import java.io.ByteArrayOutputStream
import java.io.IOException
import java.io.InputStream
import java.net.URLEncoder
import java.util.concurrent.TimeUnit
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManager
import javax.net.ssl.X509TrustManager
import kotlin.jvm.java

class HTTPHelper {

    private val logger = LoggerFactory.getLogger("http")

    lateinit var client: OkHttpClient

    companion object {
        var TYPE_APPLICATION_WWW_FORM_URLENCODED = "application/x-www-form-urlencoded"
        var TYPE_APPLICATION_JSON = "application/json"

        class RespBytes {
            lateinit var response: Response
            lateinit var body: ByteArrayOutputStream
        }

        class Options : Cloneable {
            var _headers: MutableMap<String, String>? = null
            var _body: RequestBody? = null
            var _maxBodySize = 1024 * 1024 * 10; // 10M

            constructor() {
            }

            fun formBody(body: Map<String, Any?>?): Options {
                if (body != null) {
                    var form = FormBody.Builder();
                    if (body != null)
                        body!!.forEach { k, v ->
                            if (v == null) {
                                form.add(k.trim(), "");
                            } else if (v is Array<*>) {
                                v.forEach {
                                    if (it != null)
                                        form.add(k.trim(), it.toString().trim());
                                    else
                                        form.add(k.trim(), "");
                                }
                            } else if (v is Array<*>) {
                                v.forEach {
                                    if (it != null)
                                        form.add(k.trim(), it.toString().trim());
                                    else
                                        form.add(k.trim(), "");
                                }
                            } else {
                                form.add(k.trim(), v.toString().trim())
                            }
                        }
                    _body = form.build()
                }
                return this
            }

            fun jsonBody(body: Map<String, Any?>?): Options {
                if (body != null) {
                    _body = JSONUtils.toString(body).toRequestBody(TYPE_APPLICATION_JSON.toMediaType())
                }
                return this
            }

            fun rawBody(type: String?, body: String?): Options {
                if (type != null && body != null) {
                    _body = body.toRequestBody(type.toMediaType())
                }
                return this
            }

            fun rawBody(type: String?, body: ByteArray?): Options {
                if (type != null && body != null) {
                    _body = RequestBody.create(type.toMediaType(), body)
                }
                return this
            }

            fun headers(headers: Map<String, String>?): Options {
                if (headers != null)
                    _headers = headers.toMutableMap()
                else
                    _headers = null
                return this
            }

            fun header(name: String, value: String): Options {
                if (_headers == null) _headers = mutableMapOf()
                _headers!![name] = value
                return this
            }

            fun maxBodySize(s: Int): Options {
                _maxBodySize = s;
                return this;
            }

            fun copy(): Options {
                return this.clone() as Options
            }

        }


        val defaultOpts = Options()
    }

    interface HTTPListener {
        fun onRequest(request: Request)
        fun onResponse(request: Request, response: Response, time: Long)
        fun onException(url: String, body: RequestBody?, request: Request?, response: Response?)
    }

    var listener: HTTPListener? = null

    constructor() {
        var builder = OkHttpClient.Builder();
        builder.readTimeout(150, TimeUnit.SECONDS)
        builder.connectTimeout(15, TimeUnit.SECONDS)
        builder.writeTimeout(15, TimeUnit.SECONDS)
        // 创建一个不验证任何证书的TrustManager
        val trustAllCerts = arrayOf<TrustManager>(
            object : X509TrustManager {
                override fun checkClientTrusted(chain: Array<java.security.cert.X509Certificate>, authType: String) {}
                override fun checkServerTrusted(chain: Array<java.security.cert.X509Certificate>, authType: String) {}
                override fun getAcceptedIssuers(): Array<java.security.cert.X509Certificate> {
                    return arrayOf()
                }
            }
        )
        // 使用不安全的TrustManager创建一个SSLContext
        val sslContext = SSLContext.getInstance("SSL")
        sslContext.init(null, trustAllCerts, java.security.SecureRandom())
        // 创建一个不安全的SSL socket工厂
        val sslSocketFactory = sslContext.socketFactory
        builder.sslSocketFactory(sslSocketFactory, trustAllCerts[0] as X509TrustManager)
        builder.hostnameVerifier { _, _ -> true }
        client = builder.build()
    }

    constructor(client: OkHttpClient) {
        this.client = client
    }

    fun mapToQueryString(params: Map<String, Any?>): String {
        var qs = mutableListOf<String>()
        params.forEach {
            if (it.value != null)
                qs.add(URLEncoder.encode(it.key.trim(), "UTF-8") + "=" + URLEncoder.encode(it.value.toString().trim(), "UTF-8"))
        }
        return qs.joinToString("&")
    }

    fun urlConcat(_url: String, qs: String): String {
        var url = _url;
        if (url.indexOf("?") == -1)
            url = "$url?$qs";
        else
            url = "$url&$qs";
        return url;
    }

//    @Throws(IOException::class)
//    fun invoke(
//        url: String,
//        body: RequestBody?
//    ): String {
//        return invoke(url, body, null, String::class.java)
//    }

    @Throws(IOException::class)
    fun <T> invoke(
        method: String,
        url: String,
        resultCls: Class<T>?,
        opts: Options
    ): T? {
        val beginTime = System.currentTimeMillis()
        val _url: String = url
        var request: Request? = null
        var response: Response? = null
        val body = opts._body
        try {
            val requestBuilder = Request.Builder().url(_url)
            if (opts._headers != null)
                opts._headers!!.map {
                    requestBuilder.addHeader(it.key, it.value)
                }
            if (method.equals("get", true)) {
                request = requestBuilder.get().build()
            } else if (method.equals("delete", true)) {
                request = requestBuilder.delete(body).build()
            } else if (method.equals("post", true)) {
                request = requestBuilder.post(body!!).build()
            } else {
                throw IllegalArgumentException("Unknow method ${method}")
            }
            if (listener != null) listener!!.onRequest(request)
            response = client.newCall(request).execute()
            if (listener != null) listener!!.onResponse(request, response, System.currentTimeMillis() - beginTime)
            val rbody = response.body
            if (rbody == null && resultCls == null)
                return null

            if (resultCls == InputStream::class.java)
                return rbody!!.byteStream() as T

            if (rbody!!.contentLength() >= opts._maxBodySize)
                throw RuntimeException("HTTP Response body exceed the limit")

            val baos = object : ByteArrayOutputStream() {
                override fun write(b: Int) {
                    super.write(b)
                    if (size() >= opts._maxBodySize) {
                        throw RuntimeException("HTTP Response body exceed the limit")
                    }
                }
            };
            IOUtils.copy(rbody.byteStream(), baos);

            if (resultCls == ByteArray::class.java)
                return baos.toByteArray() as T

            if (resultCls == RespBytes::class.java) {
                var r = RespBytes()
                r.response = response
                r.body = baos
                return r as T;
            }
            val result = baos.toString()
            if (resultCls == String::class.java)
                return result as T
            return JSONUtils.toObject(result, resultCls!!);
        } catch (e: Exception) {
            response?.closeQuietly()
            if (listener != null) listener!!.onException(url, body, request, response)
            throw RuntimeException("Request ${method} ${url} exception : " + e.message, e)
        } finally {
            if (resultCls != InputStream::class.java)
                response?.closeQuietly()
        }
    }

    fun <T> post(url: String, body: Map<String, Any?>?, c: Class<T>, opts: Options = defaultOpts): T {
        return invoke("post", url, c, opts.copy().formBody(body))!!;
    }

    fun <T> get(_url: String, params: Map<String, Any?>, c: Class<T>, opts: Options = defaultOpts): T {
        var qs = mapToQueryString(params)
        var url = urlConcat(_url, qs)
        return invoke("get", url, c, opts)!!
    }

    fun <T> delete(_url: String, body: Map<String, Any?>?, c: Class<T>, opts: Options = defaultOpts): T {
        return invoke("delete", _url, c, opts.copy().formBody(body))!!
    }

    fun <T> postJSON(url: String, body: Map<String, Any?>?, c: Class<T>, opts: Options = defaultOpts): T {
        return invoke("post", url, c, opts.copy().jsonBody(body))!!;
    }

    fun <T> deleteJSON(_url: String, body: Map<String, Any?>?, c: Class<T>, opts: Options = defaultOpts): T {
        return invoke("delete", _url, c, opts.copy().jsonBody(body))!!
    }

}
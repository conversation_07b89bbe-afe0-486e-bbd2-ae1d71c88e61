package com.vsimtone.rirm2.backend.utils

import org.hibernate.Hibernate
import org.hibernate.collection.internal.PersistentBag
import org.hibernate.proxy.HibernateProxy
import org.hibernate.proxy.LazyInitializer
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.lang.reflect.Field
import java.lang.reflect.InvocationTargetException
import java.lang.reflect.Method
import java.util.*
import java.util.zip.GZIPInputStream
import java.util.zip.GZIPOutputStream
import kotlin.reflect.KMutableProperty1
import kotlin.reflect.full.memberProperties

/**
 * Created by zhangkun on 2015/7/12.
 */
object RestUtil {


    internal var logger = Log.get(RestUtil::class.java)


    var excludeBeanCopy = Arrays.asList(*arrayOf("id", "createdAt", "updatedAt", "deleted", "bundle"))

    @Throws(NoSuchMethodException::class, InvocationTargetException::class, IllegalAccessException::class)
    fun getBeanValue(o: Any, f: Field): Any? {
        val c = f.declaringClass
        val name = f.name
        val getMethodNames = arrayOf("get" + name.substring(0, 1).uppercase(Locale.getDefault()) + name.substring(1), "is" + name.substring(0, 1).uppercase(Locale.getDefault()) + name.substring(1), name)

        var m: Method? = null
        for (mn in getMethodNames)
            try {
                m = c.getMethod(mn)
                break
            } catch (nsm: NoSuchMethodException) {
            }

        if (m == null)
            throw RuntimeException("getBeanValue error, not found get method " + c.simpleName + "." + name)
        return m.invoke(o)
    }

    fun <T> unwrapBean(data: T): T {
        if (data is HibernateProxy) {
            return Hibernate.unproxy(data) as T
        } else if (data is LazyInitializer) {
            return data.implementation as T
        } else if (data is PersistentBag) {
            return (data as Collection<*>).map { unwrapBean(it!!) }.toMutableList() as T
        } else if (data is MutableList<*>) {
            return data.map { unwrapBean(it!!) }.toMutableList() as T
        } else if (data is Collection<*>) {
            return data.map { unwrapBean(it!!) } as T
        } else return data
    }

    fun <T, V> unwrapBean(field: KMutableProperty1<T, V>, obj: T) {
        val data = field.get(obj) ?: return
        field.set(obj, unwrapBean(data))
    }

    fun <T : Any> copyBean(source: T, target: T, excludeProperties: Array<String>) {
        val sourceProperties = source::class.memberProperties
        val targetProperties = target::class.memberProperties
        for (sourceProp in sourceProperties) {
            if (sourceProp.name in excludeProperties) continue
            val targetProp = targetProperties.find { it.name == sourceProp.name }
            if (targetProp != null && targetProp.returnType == sourceProp.returnType) {
                targetProp as kotlin.reflect.KMutableProperty1<T, Any?>
                sourceProp as kotlin.reflect.KMutableProperty1<T, Any?>
                targetProp.set(target, sourceProp.get(source))
            }
        }
    }

    fun gzipEncode(data: ByteArray): ByteArray {
        var baos = ByteArrayOutputStream()
        GZIPOutputStream(baos).use {
            it.write(data)
        }
        return baos.toByteArray()
    }

    fun gzipDecode(data: ByteArray): ByteArray {
        var bais = ByteArrayInputStream(data)
        GZIPInputStream(bais).use {
            return it.readBytes()
        }
    }
}

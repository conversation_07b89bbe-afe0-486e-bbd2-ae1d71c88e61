package com.vsimtone.rirm2.backend.utils


import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory


object YAMLUtils {

    open val om = ObjectMapper(YAMLFactory())

    init {
        om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    fun <T> toObject(data: String, c: Class<T>): T {
        return om.readValue(data, c)
    }

    fun toString(data: Any): String {
        return om.writeValueAsString(data)
    }

}

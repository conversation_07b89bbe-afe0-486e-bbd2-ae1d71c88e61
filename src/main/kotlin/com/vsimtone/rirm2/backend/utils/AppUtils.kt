package com.vsimtone.rirm2.backend.utils

import com.vsimtone.rirm2.backend.entity.BaseEntity
import javax.servlet.http.HttpServletRequest
import org.apache.commons.io.IOUtils
import org.apache.commons.lang3.StringUtils
import org.apache.poi.hssf.usermodel.HSSFSheet
import org.apache.poi.hssf.usermodel.HSSFWorkbook
import org.apache.poi.ss.usermodel.CellType
import org.apache.poi.ss.usermodel.DateUtil
import org.apache.poi.ss.usermodel.RichTextString
import org.apache.poi.ss.usermodel.WorkbookFactory
import org.redisson.client.codec.*
import org.springframework.beans.BeanUtils
import org.springframework.core.io.ClassPathResource
import org.springframework.util.ResourceUtils
import java.beans.PropertyDescriptor
import java.io.*
import java.lang.reflect.InvocationTargetException
import java.net.InetAddress
import java.net.URL
import java.nio.ByteBuffer
import java.nio.charset.Charset
import java.nio.charset.CodingErrorAction
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*
import java.util.regex.Pattern
import javax.script.Invocable
import javax.script.ScriptEngineManager


/**
 * Created by zhangkun on 14-10-14.
 */
object AppUtils {

    val MAIL_REGEXP =
        Regex("(?:[a-z0-9!#\$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#\$%&'*+/=?^_`{|}~-]+)*|\"(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21\\x23-\\x5b\\x5d-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])*\")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21-\\x5a\\x53-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])+)\\])")
    val MAIL_REGEXP_FULL =
        Regex("(?:[a-z0-9!#\$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#\$%&'*+/=?^_`{|}~-]+)*|\"(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21\\x23-\\x5b\\x5d-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])*\")@(?:(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?|\\[(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?|[a-z0-9-]*[a-z0-9]:(?:[\\x01-\\x08\\x0b\\x0c\\x0e-\\x1f\\x21-\\x5a\\x53-\\x7f]|\\\\[\\x01-\\x09\\x0b\\x0c\\x0e-\\x7f])+)\\])")

    val INS_STR = "ilyYtToO0JjCczZ-_Aa1LFfSsXx7"

    val logger = Log.get(AppUtils::class.java)

    private val configCaches = HashMap<String, String>()

    private val HEADERS_TO_TRY = arrayOf(
        "X-Forwarded-For",
        "X-real-ip",
        "Proxy-Client-IP",
        "WL-Proxy-Client-IP",
        "HTTP_X_FORWARDED_FOR",
        "HTTP_X_FORWARDED",
        "HTTP_X_CLUSTER_CLIENT_IP",
        "HTTP_CLIENT_IP",
        "HTTP_FORWARDED_FOR",
        "HTTP_FORWARDED",
        "HTTP_VIA",
        "REMOTE_ADDR"
    )

    val DEFAULT_DATE_FORMAT get() = SimpleDateFormat(BaseEntity.DEFAULT_DATE_PATTERN)

    /**返回本地主机。 */
    /**返回 IP 地址字符串（以文本表现形式） */
    val localHostIP: String
        get() {
            var ip: String
            try {
                val addr = InetAddress.getLocalHost()
                ip = addr.hostAddress
            } catch (ex: Exception) {
                ip = ""
            }

            return ip
        }

    class ProgressReport {
        var reportTime = 0L;
        var total = 0;
        lateinit var prefix: String;
        lateinit var logger: Log;

        constructor(prefix: String, logger: Log, total: Int) {
            this.total = total;
            this.prefix = prefix;
            this.logger = logger;
        }

        fun report(curr: Int) {
            if (curr == 0 || curr >= total - 1 || System.currentTimeMillis() - reportTime >= 1000) {
                reportTime = System.currentTimeMillis()
                val p = String.format(
                    "%.2f",
                    (curr * 100.0 / total)
                );
                this.logger.info("${this.prefix} ${curr}/${this.total} ${p}%")
            }
        }
    }

    fun charsetDetect(data: ByteBuffer, charsets: List<Charset>): Charset? {
        var hitScore = 1f
        var hitCharset: Charset? = null
        charsets.forEach {
            val decoder = it.newDecoder().onUnmappableCharacter(CodingErrorAction.REPLACE).onMalformedInput(CodingErrorAction.REPLACE);
            data.rewind();
            val s = decoder.decode(data).toString()
            if (s.isEmpty()) return@forEach;
            val messyScore = messyCodeRate(s, it)
            if (messyScore > 0.4) return@forEach;
            val plainScore = (1 - plainTextCodeRate(s));
            val score = messyScore + plainScore
            if (score < hitScore) {
                hitScore = score;
                hitCharset = it;
                if (hitScore == 0f) return it;
            }
        }
        return hitCharset;
    }

    @Synchronized
    fun readConfig(name: String): String {
        if (configCaches.containsKey(name) == false) {
            val cpr = ClassPathResource("config/" + name)
            try {
                cpr.inputStream.use { `is` -> configCaches.put(name, IOUtils.toString(`is`, "UTF-8")) }
            } catch (e: IOException) {
                e.printStackTrace()
            }

        }
        return configCaches[name]!!
    }

    @JvmOverloads
    fun randomSTR(str: String, intStr: String = INS_STR): String {
        val ss = str.toCharArray()
        val sb = StringBuffer()
        var s: String?
        for (i in ss.indices) {
            val r = (Math.random() * 100.0).toInt()
            s = ss[i] + ""
            if (r in 0..40) {
                sb.append(s)
            } else if (r in 41..70) {
                sb.append(s.lowercase(Locale.getDefault()))
            } else if (r in 71..100) {
                sb.append(s.uppercase(Locale.getDefault()))
            }
        }
        for (i in ss.indices) {
            val ii = (Math.random() * sb.length.toDouble()).toInt()
            val isi = (Math.random() * intStr.length.toDouble()).toInt()
            sb.insert(ii, intStr[isi])
        }
        return sb.toString()
    }

    fun getClientIpAddress(request: HttpServletRequest): String {
        for (header in HEADERS_TO_TRY) {
            val ip = request.getHeader(header)
            if (ip != null && ip.isNotEmpty() && !"unknown".equals(ip, ignoreCase = true)) {
                return ip.split(",").first()
            }
        }
        return request.remoteAddr
    }

    fun getURI(request: HttpServletRequest): String {
        val url = StringBuffer()
        url.append(request.scheme + "://")
        url.append(request.getHeader("Host"))
        url.append(request.requestURI)
        if (request.queryString != null)
            url.append("?" + request.queryString)
        return url.toString()
    }


    @Throws(Exception::class)
    fun checkDirPath(_path: String, msg: String): String {
        var path = _path
        var url: URL
        url = ResourceUtils.getURL(path)
        path = url!!.toString()
        if (path.startsWith("file:")) path = path.substring(5)
        if (path.endsWith("/") == false && path.endsWith("\\") == false)
            throw RuntimeException("$msg must be endsWith \\ or /")
        if (File(path).isDirectory == false || File(path).canWrite() == false) {
            throw RuntimeException(msg + " not found or can`t write!! path is " + path)
        }
        return path
    }

    fun toDateString(d: Date): String {
        return DEFAULT_DATE_FORMAT.format(d)
    }

    @Throws(Exception::class)
    fun evalJS(js: File, func: String, vararg args: Any): Any {
        val engineManager = ScriptEngineManager()
        val engine = engineManager.getEngineByName("javascript")
        engine.put("logger", logger)
        engine.eval(FileReader(js))
        val invocable = engine as Invocable
        return invocable.invokeFunction(func, *args)
    }

    @Throws(Exception::class)
    fun setValue(o: Any, name: String, value: Any?) {
//        org.apache.commons.beanutils.BeanUtils.setProperty(o,name,value)
        val f = o.javaClass.getDeclaredField(name)
        f.isAccessible = true
        f.set(o, value)
    }

    @Throws(Exception::class)
    fun getValue(o: Any, name: String): Any? {
        val f = o.javaClass.getDeclaredField(name)
        f.isAccessible = true
        return f.get(o)
    }

    fun listToMap(objs: List<*>, objName: String, pp: String): Map<Any, ArrayList<*>> {
        val m = HashMap<Any, ArrayList<Any?>>()
        if (objs.size == 0) return m
        var objPD: PropertyDescriptor? = null
        var objPPPD: PropertyDescriptor? = null
        try {

            for (i in objs.indices) {
                if (objPD == null)
                    objPD = BeanUtils.getPropertyDescriptor(objs[i]!!.javaClass, objName)
                val obj = objPD!!.readMethod.invoke(objs[i]) ?: continue
                if (objPPPD == null) objPPPD = BeanUtils.getPropertyDescriptor(obj.javaClass, pp)
                val on = objPPPD!!.readMethod.invoke(obj)
                if (!m.containsKey(on)) m[on] = ArrayList<Any?>()
                m[on]!!.add(objs[i])
            }
        } catch (e: IllegalAccessException) {
            e.printStackTrace()
        } catch (e: InvocationTargetException) {
            e.printStackTrace()
        }

        return m
    }

    fun <T : Enum<T>> parseEnum(enumType: Class<T>, name: String): T? {
        try {
            return java.lang.Enum.valueOf(enumType, name)
        } catch (e: Exception) {
        }
        return null
    }

    @Throws(IOException::class)
    fun createExcel(sheetName: String?, excels: List<List<Any>>, os: OutputStream): Long {
        val wb = HSSFWorkbook()
        var sheet: HSSFSheet?
        if (sheetName != null)
            sheet = wb.createSheet(sheetName)
        else
            sheet = wb.createSheet()
        for (i in excels.indices) {
            val row = sheet!!.createRow(i)
            for (j in 0 until excels[i].size) {
                val cell = row.createCell(j)
                if (excels[i][j] == null) {
                } else if (excels[i][j] is Date)
                    cell.setCellValue(excels[i][j] as Date)
                else if (excels[i][j] is Calendar) {
                    cell.setCellValue(excels[i][j] as Calendar)
                } else if (excels[i][j] is Float || excels[i][j] is Double) {
                    cell.setCellValue(excels[i][j] as Double)
                } else if (excels[i][j] is Long || excels[i][j] is Int) {
                    cell.setCellValue((excels[i][j] as Int).toDouble())
                } else if (excels[i][j] is Boolean) {
                    cell.setCellValue(excels[i][j] as Boolean)
                } else if (excels[i][j] is RichTextString) {
                    cell.setCellValue(excels[i][j] as RichTextString)
                } else {
                    cell.setCellValue(excels[i][j].toString())
                }
            }
        }
        val length = LongArray(1)
        wb.write(object : OutputStream() {
            @Throws(IOException::class)
            override fun write(b: Int) {
                os.write(b)
                length[0]++
            }
        })
        wb.close()
        return length[0]
    }

    fun toMap(vararg args: Any?): Map<Any, Any?> {
        val m = HashMap<Any, Any?>()
        for (i in 0 until args.size / 2)
            m[args[i * 2]!!] = args[i * 2 + 1]
        return m
    }

    @Throws(Exception::class)
    fun readExcel(`is`: InputStream): MutableList<List<String?>> {
        val data = ArrayList<List<String?>>()
        val wb = WorkbookFactory.create(`is`)
        val sheet = wb.getSheetAt(0)
        var maxLineEnd = 0;
        for (i in sheet.firstRowNum..sheet.lastRowNum) {
            val row = sheet.getRow(i) ?: continue
            val line = ArrayList<String?>()
            val start = row.firstCellNum.toInt();
            val end = row.lastCellNum.toInt();
            if (maxLineEnd < end)
                maxLineEnd = end;
            for (j in 0..end) {
                if (j < start) {
                    line.add(null);
                    continue;
                }
                val cell = row.getCell(j)
                if (cell == null) {
                    line.add(null)
                    continue
                }
                when (cell.cellType) {
                    CellType.BLANK -> line.add(null)
                    CellType.BOOLEAN -> line.add(cell.booleanCellValue.toString().trim().ifBlank { null })
                    CellType.ERROR -> line.add(cell.errorCellValue.toString().trim().ifBlank { null })
                    CellType.NUMERIC -> line.add(cell.numericCellValue.toBigDecimal().toPlainString().trim().ifBlank { null })
                    CellType.STRING -> line.add(cell.stringCellValue.trim().ifBlank { null })
                    CellType.FORMULA -> line.add(cell.cellFormula.trim().ifBlank { null })
                    else -> line.add(cell.toString().trim().ifBlank { null })
                }
            }
            data.add(line)
        }
        data.forEach {
            while (it.size < maxLineEnd)
                (it as MutableList).add(null)
        }
        return data
    }

    fun getErrMsg(_e: Throwable): String {
        var msgs = mutableListOf<String>()
        var e: Throwable? = _e
        do {
            if (e!!.message != null) {
                msgs.add(0, e.javaClass.simpleName + ": " + e.message!!)
            }
            if (e!!.cause != e)
                e = e.cause
            else
                e = null
        } while (e != null)
        return msgs.joinToString(" -> ")
    }

    fun xlsToDate(data: Any): Date? {
        val fmts = arrayOf("yyyy-MM-dd HH:mm:ss", "yyyy-MM-ddTHH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM-dd", "yyyyMMdd")
//        2024年04月05日8时35分29秒
        if (data.toString().matches(Regex("^\\d+(\\.\\d+)$"))) {
            val a = java.lang.Double.valueOf(data.toString())
            return DateUtil.getJavaDate(a)
        }
        for (fmt in fmts) {
            val dd = SimpleDateFormat(fmt)
            try {
                return dd.parse(data.toString())
                break
            } catch (e: ParseException) {
            }
        }
        throw IllegalArgumentException("无法识别的时间格式：" + data)
    }

    /**
     * 判断字符串乱码的比例
     *
     * @param strName 字符串
     * @return 比例
     */
    fun messyCodeRate(str: String, charset: Charset = Charsets.UTF_8): Float {
        val cleanStr = str.replace(Regex("[\\r\\n\\t\\s]+"), "")
        val messyStr = charset.newDecoder().replacement()
        val messyCount = StringUtils.countMatches(cleanStr, messyStr);
        return (messyCount * messyStr.length * 10000f / cleanStr.length).toInt() / 10000f
    }

    /**
     * 含有中文，字母，数字等常用字符的比例
     */
    fun plainTextCodeRate(str: String): Float {
        val cleanStr = str.replace(Regex("[\\r\\n\\t\\s]+"), "")
        val plainTextCodePattern = Pattern.compile("[\\u4e00-\\u9fa5|\\w]")
        var count = 0;
        val m = plainTextCodePattern.matcher(cleanStr);
        while (m.find())
            count++;
        return (count * 10000f / cleanStr.length).toInt() / 10000f
    }

    fun getVersionNumber(version: String): Long {
        return if (version.matches(Regex("^\\d+\\.\\d+\\.\\d+$")))
            version.replace(Regex("\\."), "0").toLong()
        else
            0;
    }
}

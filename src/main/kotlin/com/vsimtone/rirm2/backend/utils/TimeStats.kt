package com.vsimtone.rirm2.backend.utils

class TimeStats {
    var times = mutableMapOf<String, Long>()
    var prevTime = System.currentTimeMillis();
    var startTime = System.currentTimeMillis();

    fun reset() {
        prevTime = System.currentTimeMillis();
    }

    fun point(name: String) {
        var t = System.currentTimeMillis();
        var use = t - prevTime;
        if (times[name] == null) times[name] = 0;
        times[name] = times[name]!! + use;
        prevTime = t;
    }

    override fun toString(): String {
        var ss = mutableListOf<String>()
        times.forEach {
            ss.add(it.key + "=" + DateUtil.useTimeToHum(it.value));
        }
        ss.add("total=" + DateUtil.useTimeToHum(System.currentTimeMillis() - startTime));
        return ss.joinToString(", ")
    }
}

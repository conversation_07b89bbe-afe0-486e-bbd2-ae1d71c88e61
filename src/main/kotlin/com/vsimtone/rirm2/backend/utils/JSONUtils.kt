package com.vsimtone.rirm2.backend.utils


import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.json.JsonReadFeature
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import java.text.SimpleDateFormat

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 15-7-27.
 */
object JSONUtils {

    val om: ObjectMapper
    val logger = Log.get(JSONUtils::class.java)

    init {
        val build = Jackson2ObjectMapperBuilder.json()
        build.dateFormat(SimpleDateFormat("yyyy-MM-dd HH:mm:ss"))
        om = build.build()
        om.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        om.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);
        om.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    fun <T> toObject(data: String, c: Class<T>): T {
        try {
            return om.readValue(data, c)
        } catch (e: Exception) {
            var shortData = data;
            if (shortData.length > 1000) shortData = shortData.substring(0, 500) + "..." + shortData.substring(shortData.length - 500);
            logger.error("\n---------json decode error. cls=${c.simpleName}\n message->\n${e.message}\ndata->\n${shortData}\n--------", e)
            throw e;
        }
    }

    fun toString(data: Any, pretty: Boolean = false): String {
        if (pretty) {
            return om.writerWithDefaultPrettyPrinter().writeValueAsString(data)
        }
        return om.writeValueAsString(data)
    }

}

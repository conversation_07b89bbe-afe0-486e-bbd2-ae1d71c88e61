package com.vsimtone.rirm2.backend.handler

import com.vsimtone.rirm2.backend.bean.RestException
import com.vsimtone.rirm2.backend.utils.AppUtils
import com.vsimtone.rirm2.backend.utils.JSONUtils
import com.vsimtone.rirm2.backend.utils.Log
import javax.servlet.http.HttpServletRequest
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.authentication.InsufficientAuthenticationException
import org.springframework.security.web.csrf.InvalidCsrfTokenException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler

/**
 * Created by zhangkun on 15-8-3.
 */
@ControllerAdvice
class SpringErrorHandler {


    protected var logger = Log[this.javaClass]

    @ExceptionHandler(Exception::class)
    fun normalException(request: HttpServletRequest, e: Exception): ResponseEntity<Map<*, *>> {
        if (e is RestException)
            return ResponseEntity(mapOf("errmsg" to e.errmsg, "errcode" to e.errcode, "data" to null), HttpStatus.OK)
        if (e is InvalidCsrfTokenException || e is org.springframework.security.access.AccessDeniedException || e is InsufficientAuthenticationException) {
            val info = HashMap<String, Any?>()
            info["errmsg"] = e.message
            info["errcode"] = 403
            logger.error("Access denied: ${AppUtils.getURI(request)} from ${AppUtils.getClientIpAddress(request)}")
            return ResponseEntity(info, HttpStatus.FORBIDDEN)
        }
        logger.error("\n-> Request exception : " + AppUtils.getURI(request) + " from ${AppUtils.getClientIpAddress(request)}\n-> Body: " + JSONUtils.toString(request.parameterMap), e);
        val info = HashMap<String, Any?>()
        info["errmsg"] = "Internal Server Error"
        info["errcode"] = 500
        return ResponseEntity(info, HttpStatus.INTERNAL_SERVER_ERROR)
    }

}

package com.vsimtone.rirm2.backend.repository

import com.vsimtone.rirm2.backend.entity.FileInfo
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface FileInfoRepository : BaseRepository<FileInfo> {
    fun findByToken(token: String): FileInfo?
    fun findBySha256sum(sha256: String): FileInfo?

    @Modifying
    @Query("update FileInfo set folder.id = ?1 where folder.id = ?2")
    fun updateFolderByFolder(folder1: Long, folder2: Long): Int

    @Modifying
    @Query("update FileInfo set folder.id = ?1 where folder.id is null")
    fun updateFolderByNoFolder(folder1: Long): Int
}
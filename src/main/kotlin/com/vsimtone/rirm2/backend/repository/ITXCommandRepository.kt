package com.vsimtone.rirm2.backend.repository

import com.vsimtone.rirm2.backend.entity.ChangeFormItem
import com.vsimtone.rirm2.backend.entity.ITXCommand
import org.springframework.stereotype.Repository

@Repository
interface ITXCommandRepository : BaseRepository<ITXCommand> {
    fun countAllByChangeFormItem(changeFormItem: ChangeFormItem): Long?
    fun countAllByChangeFormItemAndAuditResult(changeFormItem: ChangeFormItem, auditResult: ITXCommand.Companion.AuditResult): Long?
    fun countAllByChangeFormItemAndProcessResult(changeFormItem: ChangeFormItem, r: ITXCommand.Companion.ProcessResult): Long?
}

package com.vsimtone.rirm2.backend.repository

import com.vsimtone.rirm2.backend.entity.ExcelImport
import com.vsimtone.rirm2.backend.entity.FileInfo
import org.springframework.stereotype.Repository

@Repository
interface ExcelImportRepository : BaseRepository<ExcelImport> {
    fun findAllByStatusIn(vararg status: ExcelImport.Status): List<ExcelImport>
    fun findByFile(file: FileInfo): ExcelImport?
}

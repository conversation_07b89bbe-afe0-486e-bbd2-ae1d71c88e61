package com.vsimtone.rirm2.backend.repository

import com.vsimtone.rirm2.backend.entity.Organization
import org.springframework.stereotype.Repository

@Repository
interface OrganizationRepository: BaseRepository<Organization> {

    fun findByName(name: String): Organization?

    fun findByParentIdAndName(parent: Long?,name: String): Organization?

    fun findByParentId(parent: Long?): List<Organization>
}
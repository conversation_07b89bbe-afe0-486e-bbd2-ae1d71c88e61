package com.vsimtone.rirm2.backend.repository

import com.vsimtone.rirm2.backend.entity.ChangeFormCommand
import com.vsimtone.rirm2.backend.entity.ChangeFormItem
import com.vsimtone.rirm2.backend.entity.ExcelImport
import org.springframework.stereotype.Repository

@Repository
interface ChangeFormCommandRepository : BaseRepository<ChangeFormCommand> {
    fun findAllByChangeFormId(item: String): List<ChangeFormCommand>
    fun countAllByChangeFormItem(item: ChangeFormItem): Long?
    fun deleteByFromImport(ei: ExcelImport): Long
}

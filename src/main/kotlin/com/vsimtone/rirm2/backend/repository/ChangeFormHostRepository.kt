package com.vsimtone.rirm2.backend.repository

import com.vsimtone.rirm2.backend.entity.ChangeFormHost
import com.vsimtone.rirm2.backend.entity.ChangeFormItem
import com.vsimtone.rirm2.backend.entity.ExcelImport
import org.springframework.stereotype.Repository

@Repository
interface ChangeFormHostRepository : BaseRepository<ChangeFormHost> {
    fun findAllByChangeFormId(item: String): List<ChangeFormHost>
    fun countAllByChangeFormItem(item: ChangeFormItem): Long?
    fun deleteByFromImport(ei: ExcelImport): Long
}

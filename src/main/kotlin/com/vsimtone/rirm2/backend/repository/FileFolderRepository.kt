package com.vsimtone.rirm2.backend.repository

import com.vsimtone.rirm2.backend.entity.FileFolder
import org.springframework.stereotype.Repository

@Repository
interface FileFolderRepository : BaseRepository<FileFolder> {
    fun findByParentId(parent: Long): List<FileFolder>
    fun findFirstByParentIdAndName(parent: Long, name: String): FileFolder?
    fun findByParentIsNull(): FileFolder?
    fun findByName(name: String): FileFolder?
}

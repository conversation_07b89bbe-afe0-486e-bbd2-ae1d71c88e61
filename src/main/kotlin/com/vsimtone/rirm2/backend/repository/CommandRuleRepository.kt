package com.vsimtone.rirm2.backend.repository

import com.vsimtone.rirm2.backend.entity.CommandCategory
import com.vsimtone.rirm2.backend.entity.CommandRule
import org.springframework.stereotype.Repository

@Repository
interface CommandRuleRepository : BaseRepository<CommandRule> {

    fun findByRuleCode(ruleCode: String): CommandRule?

//    fun includeCommandCategory(commandCategory: CommandCategory): List<CommandRule>?

}

package com.vsimtone.rirm2.backend.aop


import com.vsimtone.rirm2.backend.annotation.OptLogAudit
import com.vsimtone.rirm2.backend.bean.ApiResp
import com.vsimtone.rirm2.backend.controller.BaseController
import com.vsimtone.rirm2.backend.entity.OptLog
import com.vsimtone.rirm2.backend.service.AdminUserService
import com.vsimtone.rirm2.backend.service.OptLogService
import com.vsimtone.rirm2.backend.utils.AppUtils
import org.aspectj.lang.ProceedingJoinPoint
import org.aspectj.lang.annotation.Around
import org.aspectj.lang.annotation.Aspect
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component

@Aspect
@Component
class OptLogAspect {

    @Autowired
    lateinit var optLogService: OptLogService

    @Autowired
    @Lazy
    lateinit var userService: AdminUserService

    @Around("@annotation(args)")
    fun audit(point: ProceedingJoinPoint, args: OptLogAudit): Any {
        val log = OptLog()
        log.type = args.type
        log.moduleName = args.moduleName
        log.objName = args.objName
        log.username = userService.currUser()?.username ?: ""
        val details = mutableMapOf<String, Any?>()
        try {
            val target = point.target
            if (target is BaseController) {
                val req = target.getRequest()
                details.put("请求地址", AppUtils.getURI(req));
                details.put("请求来源IP", AppUtils.getClientIpAddress(req));
                details.put("请求浏览器UA", req.getHeader("user-agent"));
            }
            val ret = point.proceed();
            if (ret is ApiResp) {
                details.put("响应代码", ret.errcode)
                if (ret.errmsg != null)
                    details.put("响应消息", ret.errmsg)
//                log.details.put("响应数据", ret.data);
            }
            return ret;
        } catch (e: Exception) {
            details.put("执行异常", e.message);
            throw e;
        } finally {
            log.details = details;
            optLogService.lazyLog(log);
        }
    }
}
#!/usr/bin/env bash
if [[ "${IS_CAPTURER_AGENT}" == "1" ]]; then
  exit 0
fi
export IS_CAPTURER_AGENT=1
clear
#echo -n "CapturerAgent-ServerIP: "
#read -r ip
#echo -n "CapturerAgent-Username: "
#read -r username
#echo -n "CapturerAgent-Password: "
#read -r password

ip=127.0.0.1
username=root
password=88129147

clear
SHELL=bash /opt/CFRM/TTYCapture/scripts/gxttyCapture.sh
expectShell=$(
  cat <<EOF
spawn -noecho ssh -q -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $username@$ip
stty -echo

set pwdSendCount 0
set timeout 30
set prompt "(%|#|>|\\$ )"

expect {
   "(yes/no)"  {
       send "yes\n"
       exp_continue
    }
    -re \$prompt {
       stty echo
    }
   "password:" {
       if { \$pwdSendCount != 0 } {
           send -- \x3
           expect eof
           exit 1
       } else {
           stty -echo
           set pwdSendCount [expr \$pwdSendCount + 1 ]
           send "${password}\r"
           set timeout 5
           exp_continue
       }
    }
    timeout {
      if { \$pwdSendCount == 0 } {
           send -- \x3
           expect eof
           exit 2
      }
    }
    eof {
      exit 3
    }
}

interact
EOF
)
expect -c "$expectShell"

expectExtCode=$?

if [[ "$expectExtCode" == 1 ]]; then
  echo 密码错误,按任意键退出
  read -r
fi

if [[ "$expectExtCode" == 2 ]]; then
  echo 未能识别的密码提示,按任意键退出
  read -r
fi
if [[ "$expectExtCode" == 3 ]]; then
  echo 链接被关闭,按任意键退出
  read -r
fi

exit 0

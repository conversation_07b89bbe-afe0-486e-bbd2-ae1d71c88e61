stages:
  - deploy
rirm2_server_upgrade:
  stage: deploy
  script: |
    export JAVA_HOME=$(echo /usr/lib/jvm/java-8-openjdk)
    export PATH=$JAVA_HOME/bin:$PATH
    rm -rf build
    java -version
    echo Building ...
    ./gradlew --no-daemon clean pack
    echo Uploading ...
    scp build/*.tar root@************:/opt/rirm2/server/
    echo Updating ...
    ssh root@************ /opt/rirm2/server/update.sh
    echo Done
